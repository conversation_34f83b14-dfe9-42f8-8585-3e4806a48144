{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/public/*": ["./public/*"], "@/mocks/*": ["./mocks/*"], "@/tests/*": ["./tests/*"], "@/*": ["./src/*"]}, "types": ["vitest/importMeta"]}, "include": ["next-env.d.ts", "src/**/*.ts", "src/**/*.tsx", "tests/**/*.ts", "mocks/**/*.ts", ".next/types/**/*.ts", "vitest.config.mts", "vitest-setup.ts"], "exclude": ["node_modules"]}