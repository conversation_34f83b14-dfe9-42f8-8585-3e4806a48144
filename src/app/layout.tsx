import { Providers } from "@/app/providers";
import type { Metada<PERSON> } from "next";
import localFont from "next/font/local";
import { NuqsAdapter } from "nuqs/adapters/next/app";
import "./globals.css";

const irsFont = localFont({
  src: "../../public/font/iconfont.woff2",
  display: "swap",
  style: "normal",
  variable: "--font-irs",
});

export const metadata: Metadata = {
  title: "IRRISS",
  description: "diverse",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN">
      <body className={`${irsFont.variable} antialiased`}>
        <NuqsAdapter>
          <Providers>{children}</Providers>
        </NuqsAdapter>
      </body>
    </html>
  );
}
