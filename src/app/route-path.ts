const url = (p: string) => process.env["NEXT_PUBLIC_APP_BASE_URL"] + p;
const make = (p: string) => ({
    path: p,
    url: url(p),
    URL: () => new URL(url(p))
});
export const paths = {
    ...make(""),
    llm: {
        ...make("" + "/llm"),
        chat: {
            ...make("" + "/llm" + "/chat")
        }
    },
    project: {
        ...make("" + "/project"),
        projectId: (projectId: string) => ({
            ...make("" + "/project" + ("/" + projectId)),
            app: {
                channelTool: {
                    ...make("" + "/project" + ("/" + projectId) + "/app" + "/channel-tool")
                },
                ingest: {
                    ...make("" + "/project" + ("/" + projectId) + "/app" + "/ingest")
                },
                register: {
                    ...make("" + "/project" + ("/" + projectId) + "/app" + "/register")
                }
            },
            task: {
                ...make("" + "/project" + ("/" + projectId) + "/task"),
                taskId: (taskId: string) => ({
                    ...make("" + "/project" + ("/" + projectId) + "/task" + ("/" + taskId))
                })
            }
        })
    },
    account: {
        ...make("" + "/account")
    },
    atp: {
        account: {
            ...make("" + "/atp" + "/account")
        },
        recharge: {
            ...make("" + "/atp" + "/recharge")
        },
        transaction: {
            ...make("" + "/atp" + "/transaction")
        }
    },
    msg: {
        ...make("" + "/msg")
    },
    bmx: {
        itemId: (itemId: string) => ({
            ...make("" + "/bmx" + ("/" + itemId))
        })
    },
    landing: {
        ...make("" + "/landing")
    },
    login: {
        ...make("" + "/login")
    },
    protocol: {
        atp: {
            ...make("" + "/protocol" + "/atp")
        }
    }
};
