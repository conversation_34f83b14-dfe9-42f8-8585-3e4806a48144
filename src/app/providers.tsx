import StoreProvider from "@/lib/app-store/store-provider";
import { AntdRegistry } from "@ant-design/nextjs-registry";
import { App, ConfigProvider } from "antd";
import zhCN from "antd/locale/zh_CN";
import "dayjs/locale/zh-cn";
import { NavigationGuardProvider } from "next-navigation-guard";

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <AntdRegistry layer>
      <ConfigProvider
        locale={zhCN}
        theme={{
          token: {
            colorPrimary: "#655DFF",
            colorText: "rgba(23, 31, 43, 0.8)",
          },
          components: {
            Menu: {
              itemSelectedBg: "#F5F5F5",
              itemHeight: 46,
              iconMarginInlineEnd: 8,
            },
            Tree: {
              directoryNodeSelectedBg: "#e7e5ff",
              directoryNodeSelectedColor: "rgba(23, 31, 43, 0.8)",
            },
          },
        }}
      >
        <StoreProvider>
          <NavigationGuardProvider>
            <App component={false}>{children}</App>
          </NavigationGuardProvider>
        </StoreProvider>
      </ConfigProvider>
    </AntdRegistry>
  );
}
