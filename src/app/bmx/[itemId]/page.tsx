import { ImageEditor } from "@/app-ui/components/image/editor";
import { SiderMenu } from "@/app-ui/components/image/sider-menu";
import { ToolBar } from "@/app-ui/components/image/tool-bar/tool-bar";
import { di } from "@/app-ui/di";
import { Skeleton } from "antd";
import { Content } from "antd/es/layout/layout";
import Sider from "antd/es/layout/Sider";
import { Metadata } from "next";
import { Suspense } from "react";

export type ParamsType = {
  params: Promise<{ itemId: string }>;
};
type Props = ParamsType & {};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { itemId } = await params;

  return {
    title: itemId,
  };
}

export default async function BMXPage({ params }: Props) {
  await di.ensureLogin();

  const { itemId } = await params;
  const imgQuerySvc = await di.getImgQuerySvc();
  const imgInfo = await imgQuerySvc.imgInfo({ itemId });

  return (
    <div className="flex flex-col h-screen">
      <div className="flex border-b h-16">
        <div className="flex-auto">
          <Suspense fallback={<Skeleton></Skeleton>}>
            <ToolBar imgInfo={imgInfo} />
          </Suspense>
        </div>
        <div className="flex-none basis-[260px]"></div>
      </div>
      <div className="flex flex-auto">
        <Sider width={128} className="bg-white">
          <SiderMenu />
        </Sider>
        <Content>
          <ImageEditor imgInfo={imgInfo} />
        </Content>
      </div>
    </div>
  );
}
