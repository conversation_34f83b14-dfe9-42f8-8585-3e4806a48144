import { PrjAppPanel } from "@/app-ui/components/project/app-panel";
import { PrjHeader } from "@/app-ui/components/project/header";
import { PrjResource } from "@/app-ui/components/project/resource";
import { di } from "@/app-ui/di";
import { Flex, Splitter } from "antd";

export default async function ProjectLayout({
  children,
  params,
}: LayoutProps<"/project/[projectId]">) {
  const { projectId } = await params;
  const projectQuerySvc = await di.getProjectQuerySvc();
  const project = await projectQuerySvc.getProject(projectId);

  return (
    <>
      <Flex className="h-screen" vertical>
        <PrjHeader name={project.name} />

        <Splitter className="flex-1 overflow-hidden">
          <Splitter.Panel min={400} max={800} defaultSize={400}>
            <div className="flex flex-col h-full">
              <div className="flex-auto border-b">
                <PrjResource projectId={projectId} />
              </div>
              <div className="flex-auto">
                <PrjAppPanel projectId={projectId} />
              </div>
            </div>
          </Splitter.Panel>
          <Splitter.Panel>{children}</Splitter.Panel>
        </Splitter>
      </Flex>
    </>
  );
}
