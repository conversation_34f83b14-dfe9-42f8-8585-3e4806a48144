import { TaskList } from "@/app-ui/components/task/list";
import { di } from "@/app-ui/di";

export default async function TaskListPage({
  params,
}: {
  params: Promise<{ projectId: string }>;
}) {
  await di.ensureLogin();
  const { projectId } = await params;
  const svc = await di.getTaskQuerySvc();
  const tasks = await svc.tasks({ prjId: projectId });

  return <TaskList projectId={projectId} tasks={tasks} />;
}
