import { di } from "@/app-ui/di";
import { IrsModelId } from "@/lib/domain/ai/llm/models";
import { createUnifiedAPIRouteHandler } from "@/lib/infra/nextjs/router-handler";
import { ModelContext } from "@assistant-ui/react";
import {
  consumeStream,
  createUIMessageStreamResponse,
  validateUIMessages,
} from "ai";
import { NextRequest } from "next/server";
import z from "zod";

const chatMetadataSchema = z.object({
  custom: z.object({
    threadId: z.string(),
    parentId: z.string().optional(),
    modelId: z.enum(IrsModelId),
  }),
  callSettings: z.custom<ModelContext["callSettings"]>(),
});

export type ChatRunConfigCustom = z.infer<typeof chatMetadataSchema>["custom"];

const chatRequestBodySchema = z.object({
  id: z.string(),
  trigger: z.string(),
  messages: z.array(z.unknown()),
  metadata: chatMetadataSchema,
  tools: z.record(z.string(), z.unknown()),
});

async function chat(req: NextRequest) {
  const operator = await di.ensureLogin();
  const llmSvc = await di.getLLMSvc();
  const prisma = await di.getPrisma();
  const reqJson = await req.json();

  const { messages, metadata } = chatRequestBodySchema.parse(reqJson);
  const { threadId, parentId, modelId } = metadata.custom;

  const uiMessages = await validateUIMessages({
    messages,
  });

  const uiMessageStream = await llmSvc.chat(prisma, operator, {
    threadId,
    modelId,
    messages: uiMessages,
    parentId: parentId,
    abortSignal: req.signal,
    callSettings: {
      ...metadata.callSettings,
      maxOutputTokens: metadata.callSettings?.maxTokens,
    },
  });

  const res = createUIMessageStreamResponse({
    stream: uiMessageStream,
    consumeSseStream: consumeStream,
  });

  return res;
}

export const POST = createUnifiedAPIRouteHandler(chat);
