import { di } from "@/app-ui/di";
import { IrsModel } from "@/lib/domain/ai/llm/irs";
import { TopicModelId } from "@/lib/domain/ai/llm/models";
import { PROMPT_TITLE_GENERATOR } from "@/lib/domain/common/constants";
import { createUnifiedAPIRouteHandler } from "@/lib/infra/nextjs/router-handler";
import { ThreadMessage } from "@assistant-ui/react";
import { streamText } from "ai";
import { type NextRequest } from "next/server";

const FAKE_MSG_ID_FOR_TITLE_GENERATION = "GENERATE_TITLE";

type AssistantCloudRunsStreamBody = {
  thread_id: string;
  messages: ThreadMessage[];
};

function getDefaultTitle(msgs: ThreadMessage[]) {
  let title = "new chat";

  const userMsg = msgs.find((x) => x.role == "user");
  if (userMsg) {
    title = extractMessageText(userMsg);
  }

  return title;
}

function extractMessageText(msg: ThreadMessage) {
  return msg.content
    .filter((x) => x.type == "text")
    .map((x) => x.text)
    .join("\n");
}

async function generateTitle(req: NextRequest) {
  const { messages: auiMessages, thread_id }: AssistantCloudRunsStreamBody =
    await req.json();

  const llmPvdSvc = await di.getLLMPvdSvc();
  const thread_svc = await di.getThreadSvc();
  const prisma = await di.getPrisma();
  const atpSvc = await di.getATPSvc();

  const operator = await di.ensureLogin();

  await atpSvc.ensureWithAtp(prisma, operator);

  const title = getDefaultTitle(auiMessages);

  await thread_svc.updateThreadTitle(
    prisma,
    {
      threadId: thread_id,
      title: title,
    },
    operator
  );

  const irsLLM = new IrsModel(llmPvdSvc, TopicModelId);

  const ac = new AbortController();

  let chunkTexts = "";
  const result = streamText({
    model: irsLLM,
    messages: [
      {
        role: "system",
        content: PROMPT_TITLE_GENERATOR,
      },
      {
        role: "user",
        content: `
        ${auiMessages
          .map((message) => `${message.role}: ${extractMessageText(message)}`)
          .join("\n\n")}
        
          请总结上述对话为32个字以内的标题。`,
      },
    ],
    onChunk({ chunk }) {
      if (!ac.signal.aborted && chunk.type == "text-delta") {
        chunkTexts += chunk.text;
        if (chunkTexts.length > 32) {
          // 只是为了意外情况下前端不一直 streaming title，正常不该触发
          ac.abort();
        }
      }
    },
    onFinish: async ({ text, usage: tokenUsage }) => {
      if (!ac.signal.aborted) {
        await thread_svc.updateThreadTitle(
          prisma,
          {
            threadId: thread_id,
            title: text,
          },
          operator
        );

        await atpSvc.consumeTokens(prisma, operator, {
          threadId: thread_id,
          msgId: FAKE_MSG_ID_FOR_TITLE_GENERATION,
          irsModelInfo: irsLLM.getInfo(),
          tokenUsage: tokenUsage,
        });
      }
    },
    abortSignal: ac.signal,
  });

  // 只是为了意外情况下前端不一直 streaming title，正常不该触发
  ac.signal.onabort = async () => {
    await thread_svc.updateThreadTitle(
      prisma,
      {
        threadId: thread_id,
        title: chunkTexts,
      },
      operator
    );
  };

  return result.toTextStreamResponse();
}

export const POST = createUnifiedAPIRouteHandler(generateTitle);
