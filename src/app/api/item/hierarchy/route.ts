import z from "zod";
import { NextResponse, type NextRequest } from "next/server";
import { di } from "@/app-ui/di";
import { createUnifiedAPIRouteHandler } from "@/lib/infra/nextjs/router-handler";
import { ItemTypeFlag } from "@/lib/domain/item/svc";

const ItemsHierarchyReqSchema = z.object({
  prjId: z.string(),
});

export type ItemsHierarchyReq = z.infer<typeof ItemsHierarchyReqSchema>;

const ItemsHierarchyResSchema = z.array(
  z.object({
    id: z.string(),
    name: z.string(),
    pid: z.string().optional(),
  })
);

export type ItemsHierarchyRes = z.infer<typeof ItemsHierarchyResSchema>;

export const GET = createUnifiedAPIRouteHandler(async (req: NextRequest) => {
  await di.ensureLogin();
  const itemQuerySvc = await di.getItemQuerySvc();

  const params = req.nextUrl.searchParams;
  const queryReq = Object.fromEntries(params.entries());
  const parsedReq = ItemsHierarchyReqSchema.parse(queryReq);

  const queryRes = await itemQuerySvc.itemsHierarchy({
    ...parsedReq,
    typeFlag: ItemTypeFlag.Folder,
  });
  const parsedRes = ItemsHierarchyResSchema.parse(queryRes);
  return NextResponse.json(parsedRes);
});
