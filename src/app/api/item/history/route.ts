import z from "zod";
import { di } from "@/app-ui/di";
import { type NextRequest, NextResponse } from "next/server";
import { createUnifiedAPIRouteHandler } from "@/lib/infra/nextjs/router-handler";

const HistoryItemsReqSchema = z.object({
  prjId: z.string(),
  length: z.coerce.number().min(1).optional(),
});

export type HistoryItemsReq = z.infer<typeof HistoryItemsReqSchema>;

const HistoryItemsResSchema = z.array(
  z.object({
    id: z.string(),
    name: z.string(),
    created_at: z.date(),
  })
);

export type HistoryItemsRes = z.infer<typeof HistoryItemsResSchema>;

export const GET = createUnifiedAPIRouteHandler(async (req: NextRequest) => {
  await di.ensureLogin();

  const itemQuerySvc = await di.getItemQuerySvc();

  const params = req.nextUrl.searchParams;

  const queryInput = Object.fromEntries(params.entries());
  const parsedInput = HistoryItemsReqSchema.parse(queryInput);

  const queryRes = await itemQuerySvc.items(parsedInput);
  const parsedRes = HistoryItemsResSchema.parse(queryRes);

  return NextResponse.json(parsedRes);
});
