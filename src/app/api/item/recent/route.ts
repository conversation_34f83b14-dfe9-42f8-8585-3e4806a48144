import z from "zod";
import { di } from "@/app-ui/di";
import { type NextRequest, NextResponse } from "next/server";
import { createUnifiedAPIRouteHandler } from "@/lib/infra/nextjs/router-handler";

const RecentItemsReqSchema = z.object({
  prjId: z.string(),
  length: z.coerce.number().min(1).optional(),
});

export type RecentItemsReq = z.infer<typeof RecentItemsReqSchema>;

const RecentItemsResSchema = z.array(
  z.object({
    id: z.string(),
    name: z.string(),
    updated_at: z.date(),
  })
);

export type RecentItemsRes = z.infer<typeof RecentItemsResSchema>;

export const GET = createUnifiedAPIRouteHandler(async (req: NextRequest) => {
  await di.ensureLogin();

  const itemQuerySvc = await di.getItemQuerySvc();

  const params = req.nextUrl.searchParams;

  const queryInput = Object.fromEntries(params.entries());
  const parsedInput = RecentItemsReqSchema.parse(queryInput);

  const queryRes = await itemQuerySvc.items({
    ...parsedInput,
    sortBy: "updated_at",
  });
  const parsedRes = RecentItemsResSchema.parse(queryRes);

  return NextResponse.json(parsedRes);
});
