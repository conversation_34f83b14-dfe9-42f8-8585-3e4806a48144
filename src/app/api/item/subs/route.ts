import z from "zod";
import { di } from "@/app-ui/di";
import { ItemTypeFlag } from "@/lib/domain/item/svc";
import { createUnifiedAPIRouteHandler } from "@/lib/infra/nextjs/router-handler";
import { NextResponse, type NextRequest } from "next/server";

const SubItemsReqSchema = z.object({
  prjId: z.string(),
  pid: z.string().optional(),
  itemTypeFlag: z.coerce.number().optional(),
});

export type SubItemsReq = z.infer<typeof SubItemsReqSchema>;

const SubItemsResSchema = z.array(
  z.object({
    id: z.string(),
    name: z.string(),
    size: z.number().nullish(),
    type_flag: z.enum(ItemTypeFlag),
  })
);

export type SubItemsRes = z.infer<typeof SubItemsResSchema>;

export const GET = createUnifiedAPIRouteHandler(async (req: NextRequest) => {
  await di.ensureLogin();

  const itemQuerySvc = await di.getItemQuerySvc();

  const params = req.nextUrl.searchParams;
  const queryInput = Object.fromEntries(params.entries());

  const parsedInput = SubItemsReqSchema.parse(queryInput);

  const queryRes = await itemQuerySvc.subItems(parsedInput);
  const parsedRes = SubItemsResSchema.parse(queryRes);

  return NextResponse.json(parsedRes);
});
