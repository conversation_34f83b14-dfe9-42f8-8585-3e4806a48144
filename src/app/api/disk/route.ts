import { createUnifiedAPIRouteHandler } from "@/lib/infra/nextjs/router-handler";
import { NextRequest, NextResponse } from "next/server";
import { di } from "@/app-ui/di";
import { ObjectInfo } from "@/lib/domain/disk/svc";
import z from "zod";

const ListObjectsReqSchema = z.object({
  projectId: z.string(),
  prefix: z.string(),
  isDir: z.coerce.boolean().optional(),
});

export type ListDiskReq = z.infer<typeof ListObjectsReqSchema>;

export type ListDiskResItem = ObjectInfo;
export type ListDiskRes = ListDiskResItem[];

export const GET = createUnifiedAPIRouteHandler(async (req: NextRequest) => {
  await di.ensureLogin();
  const diskSvc = await di.getDiskSvc();

  const params = req.nextUrl.searchParams;

  const queryReq = Object.fromEntries(params.entries());
  const parsedInput = ListObjectsReqSchema.parse(queryReq);

  const objects = await Array.fromAsync(diskSvc.listFirstLevel(parsedInput));
  return NextResponse.json(objects as ListDiskRes);
});
