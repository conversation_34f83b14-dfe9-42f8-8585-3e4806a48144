import { di } from "@/app-ui/di";
import { createUnifiedAPIRouteHandler } from "@/lib/infra/nextjs/router-handler";
import { type NextRequest } from "next/server";
import { z } from "zod";

const getTileReqSchema = z.object({
  imgId: z.string(),
  x: z.coerce.number(),
  y: z.coerce.number(),
  z: z.coerce.number(),
  channel_id: z.coerce.number(),
  version: z.string(),
});

export type GetTileReq = z.infer<typeof getTileReqSchema>;

export const GET = createUnifiedAPIRouteHandler(async (req: NextRequest) => {
  await di.ensureLogin();
  const imgQuerySvc = await di.getImgQuerySvc();

  const params = req.nextUrl.searchParams;
  const queryInput = Object.fromEntries(params.entries());
  const parsedInput = getTileReqSchema.parse(queryInput);

  const tileRes = await imgQuerySvc.getImgTile(parsedInput);
  return tileRes;
});
