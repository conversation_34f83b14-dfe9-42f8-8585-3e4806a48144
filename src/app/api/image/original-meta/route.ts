import { di } from "@/app-ui/di";
import { createUnifiedAPIRouteHandler } from "@/lib/infra/nextjs/router-handler";
import { type NextRequest } from "next/server";
import z from "zod";

const ImgOriginalMetaInputSchema = z.object({
  itemId: z.string(),
});

export const GET = createUnifiedAPIRouteHandler(async (req: NextRequest) => {
  await di.ensureLogin();
  const imgQuerySvc = await di.getImgQuerySvc();

  const params = req.nextUrl.searchParams;

  const queryInput = Object.fromEntries(params.entries());
  const parsedInput = ImgOriginalMetaInputSchema.parse(queryInput);

  const queryRes = await imgQuerySvc.imgOriginalMeta(parsedInput);
  return queryRes;
});
