import { di } from "@/app-ui/di";
import { createUnifiedAPIRouteHandler } from "@/lib/infra/nextjs/router-handler";
import { type NextRequest, NextResponse } from "next/server";
import { z } from "zod";

const ImgInfoInputSchema = z.object({
  itemId: z.string(),
});

const ImgInfoResSchema = z.object({
  id: z.string(),
  name: z.string(),
  prjId: z.string(),
  imgMeta: z.object({
    tile_size: z.int(),
    base_x: z.int(),
    base_y: z.int(),
    min_level: z.int(),
    max_level: z.int(),
    dtype: z.string(),
    channels: z.array(
      z.object({
        id: z.int(),
        name: z.string(),
        min: z.number(),
        max: z.number(),
        color: z.string().nullish(),
        view_max: z.number().nullish(),
        view_min: z.number().nullish(),
        view_shown: z.boolean().nullish(),
      })
    ),
    axes: z.string(),
    shape: z.array(z.int()),
    phys_x: z.number().nullish(),
    phys_x_unit: z.string().nullish(),
    phys_y: z.number().nullish(),
    phys_y_unit: z.string().nullish(),
    phys_z: z.number().nullish(),
    phys_z_unit: z.string().nullish(),
    data_version: z.string(),
    view_gamma: z.number().nullish(),
  }),
});

export type ImgInfoRes = z.infer<typeof ImgInfoResSchema>;

export const GET = createUnifiedAPIRouteHandler(async (req: NextRequest) => {
  await di.ensureLogin();
  const imgQuerySvc = await di.getImgQuerySvc();

  const params = req.nextUrl.searchParams;

  const queryInput = Object.fromEntries(params.entries());
  const parsedInput = ImgInfoInputSchema.parse(queryInput);

  const queryRes = await imgQuerySvc.imgInfo(parsedInput);
  const parsedRes = ImgInfoResSchema.parse(queryRes);

  return NextResponse.json(parsedRes);
});
