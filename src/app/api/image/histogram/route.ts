import { di } from "@/app-ui/di";
import { createUnifiedAPIRouteHandler } from "@/lib/infra/nextjs/router-handler";
import { NextRequest, NextResponse } from "next/server";
import z from "zod";

const GetHistogramInputSchema = z.object({
  itemId: z.string(),
});

const GetHistogramResSchema = z.object({
  channels: z.array(
    z.object({
      id: z.number(),
      hist: z.array(z.number()),
      bins: z.array(z.number()),
    })
  ),
});

export const GET = createUnifiedAPIRouteHandler(async (req: NextRequest) => {
  await di.ensureLogin();
  const imgQuerySvc = await di.getImgQuerySvc();

  const params = req.nextUrl.searchParams;

  const queryInput = Object.fromEntries(params.entries());
  const parsedInput = GetHistogramInputSchema.parse(queryInput);

  const queryRes = await imgQuerySvc.getHistogram(parsedInput);
  const parsedRes = GetHistogramResSchema.parse(queryRes);

  return NextResponse.json(parsedRes);
});
