import { di } from "@/app-ui/di";
import { createUnifiedAPIRouteHandler } from "@/lib/infra/nextjs/router-handler";
import { NextRequest, NextResponse } from "next/server";
import z from "zod";

const GetMinMaxByQuantileInputSchema = z.object({
  id: z.string(),
  channel_id: z.coerce.number(),
  min_quantile: z.coerce.number().nullish(),
  max_quantile: z.coerce.number().nullish(),
  ratio_type: z.enum(["intensity", "pixel"]),
});

const GetMinMaxByQuantileResSchema = z.object({
  min: z.number().nullish(),
  max: z.number().nullish(),
});

export const GET = createUnifiedAPIRouteHandler(async (req: NextRequest) => {
  await di.ensureLogin();
  const imgQuerySvc = await di.getImgQuerySvc();

  const params = req.nextUrl.searchParams;

  const queryInput = Object.fromEntries(params.entries());
  const parsedInput = GetMinMaxByQuantileInputSchema.parse(queryInput);

  const queryRes = await imgQuerySvc.getMinMaxByQuantile({
    ...parsedInput,
    in_unique: parsedInput.ratio_type == "intensity",
  });
  const parsedRes = GetMinMaxByQuantileResSchema.parse(queryRes);

  return NextResponse.json(parsedRes);
});
