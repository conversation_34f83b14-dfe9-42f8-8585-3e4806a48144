import { di } from "@/app-ui/di";
import { createUnifiedAPIRouteHandler } from "@/lib/infra/nextjs/router-handler";
import { TaskStatus, TaskType } from "@/lib/domain/task/svc";
import { NextResponse, type NextRequest } from "next/server";
import z from "zod";

const RunningTasksReqSchema = z.object({
  prjId: z.string(),
  length: z.coerce.number().min(1).optional(),
});

export type RunningTasksReq = z.infer<typeof RunningTasksReqSchema>;

const RunningTasksResSchema = z.array(
  z.object({
    id: z.string(),
    task_type: z.enum(TaskType),
    start_at: z.date(),
  })
);

export type RunningTasksRes = z.infer<typeof RunningTasksResSchema>;

export const GET = createUnifiedAPIRouteHandler(async (req: NextRequest) => {
  await di.ensureLogin();
  const taskQuerySvc = await di.getTaskQuerySvc();

  const params = req.nextUrl.searchParams;

  const queryInput = Object.fromEntries(params.entries());
  const parsedInput = RunningTasksReqSchema.parse(queryInput);

  const queryRes = await taskQuerySvc.tasks({
    ...parsedInput,
    status: TaskStatus.Running,
  });
  const parsedRes = RunningTasksResSchema.parse(queryRes);

  return NextResponse.json(parsedRes);
});
