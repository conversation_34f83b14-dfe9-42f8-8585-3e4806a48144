import { di } from "@/app-ui/di";
import { createUnifiedAPIRouteHandler } from "@/lib/infra/nextjs/router-handler";
import { TaskStatus, TaskType } from "@/lib/domain/task/svc";
import { NextResponse, type NextRequest } from "next/server";
import z from "zod";

const FinishedTasksReqSchema = z.object({
  prjId: z.string(),
  length: z.coerce.number().min(1).optional(),
});

export type FinishedTasksReq = z.infer<typeof FinishedTasksReqSchema>;

const FinishedTasksResSchema = z.array(
  z.object({
    id: z.string(),
    task_type: z.enum(TaskType).nullable(),
    finished_at: z.date().nullable(),
  })
);

export type FinishedTasksRes = z.infer<typeof FinishedTasksResSchema>;

export const GET = createUnifiedAPIRouteHandler(async (req: NextRequest) => {
  await di.ensureLogin();
  const taskQuerySvc = await di.getTaskQuerySvc();

  const params = req.nextUrl.searchParams;

  const queryInput = Object.fromEntries(params.entries());
  const parsedInput = FinishedTasksReqSchema.parse(queryInput);

  const queryRes = await taskQuerySvc.tasks({
    ...parsedInput,
    status: TaskStatus.Finished,
  });
  const parsedRes = FinishedTasksResSchema.parse(queryRes);

  return NextResponse.json(parsedRes);
});
