@layer theme, base, antd, components, utilities, overrideAntd;

@import "tailwindcss";
@plugin 'tailwindcss-animate';

@custom-variant dark (&:is(.dark *));

@theme {
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));

  --color-card: hsl(var(--card));
  --color-card-foreground: hsl(var(--card-foreground));

  --color-popover: hsl(var(--popover));
  --color-popover-foreground: hsl(var(--popover-foreground));

  --color-primary: hsl(var(--primary));
  --color-primary-foreground: hsl(var(--primary-foreground));

  --color-secondary: hsl(var(--secondary));
  --color-secondary-foreground: hsl(var(--secondary-foreground));

  --color-muted: hsl(var(--muted));
  --color-muted-foreground: hsl(var(--muted-foreground));

  --color-accent: hsl(var(--accent));
  --color-accent-foreground: hsl(var(--accent-foreground));

  --color-destructive: hsl(var(--destructive));
  --color-destructive-foreground: hsl(var(--destructive-foreground));

  --color-border: hsl(var(--border));
  --color-input: hsl(var(--input));
  --color-ring: hsl(var(--ring));

  --color-chart-1: hsl(var(--chart-1));
  --color-chart-2: hsl(var(--chart-2));
  --color-chart-3: hsl(var(--chart-3));
  --color-chart-4: hsl(var(--chart-4));
  --color-chart-5: hsl(var(--chart-5));

  --radius-lg: var(--radius);
  --radius-md: calc(var(--radius) - 2px);
  --radius-sm: calc(var(--radius) - 4px);

  --color-irs-primary: hsl(var(--irs-primary));
  --color-irs-primary-content: hsl(var(--irs-primary-content));

  --thread-max-width: 50rem;
  --font-irs: var(--font-irs);
}

@utility flex-center {
  @apply flex items-center justify-center;
}

@layer utilities {
  body {
    font-family: Arial, Helvetica, sans-serif;
  }
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }

  html {
    @apply overflow-hidden;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;

    --irs-primary: 243, 100%, 68%;
    --irs-primary-content: 217, 10%, 30%;
  }
  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;

    --irs-primary: 243, 100%, 68%;
    --irs-primary-content: 217, 10%, 30%;
  }
}
@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer overrideAntd {
  .ant-menu-item {
    @apply flex items-center cursor-pointer;
  }
  .ant-tabs-tab-active {
    @apply font-medium;
  }
  .ant-tabs-top > .ant-tabs-nav::before {
    @apply border-[#EFEFEF];
  }
  .ant-layout {
    @apply bg-white;
  }
  .ant-layout-sider {
    @apply border-r border-[#E4E4E7];
  }
  .ant-layout-sider-children,
  .ant-layout-content {
    @apply flex flex-col overflow-hidden;
  }
  .ant-menu-item:not(.ant-menu-item-selected):active {
    @apply bg-[#F5F5F5];
  }
  .ant-menu-item:not(.ant-menu-item-selected):active {
    @apply text-irs-primary;
  }
}

@keyframes fade {
  0% {
    background: #7832f1;
  }
  50% {
    background: #d6c1fa;
  }
  100% {
    background: #7832f1;
  }
}
@keyframes fade2 {
  0% {
    background: #d6c1fa;
  }
  50% {
    background: #7832f1;
  }
  100% {
    background: #d6c1fa;
  }
}

@media (width >= 1920px) {
  :root {
    --thread-max-width: 64rem;
  }
}

.Icon_Arrow:before {
  content: "\e91c";
}

.Icon_OnlyShow:before {
  content: "\e91a";
}

.Icon_Operation:before {
  content: "\e919";
}

.Icon_Edit:before {
  content: "\e918";
}

.Icon_Tailor:before {
  content: "\e917";
}

.Icon_Square:before {
  content: "\e915";
}

.Icon_AdaptiveWindow:before {
  content: "\e910";
}

.Icon_Log:before {
  content: "\e911";
}

.Icon_Project:before {
  content: "\e912";
}

.Icon_Hand:before {
  content: "\e913";
}

.Icon_Ruler:before {
  content: "\e914";
}

.Icon_Aim:before {
  content: "\e916";
}
