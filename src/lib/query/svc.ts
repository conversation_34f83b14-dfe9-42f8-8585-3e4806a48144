import type {
  TransactionsSourceInfo,
  TransactionsSourceType,
} from "@/lib/domain/atp/m";
import type { Operator } from "@/lib/domain/user/m";
import type { PrismaClient } from "@prisma/client";

export class QuerySvc {
  constructor(
    private readonly operator: Operator,
    private readonly prisma: PrismaClient
  ) {}

  async rechargeOrders(): Promise<RechargeOrdersRes[]> {
    const recharges = await this.prisma.recharge.findMany({
      where: {
        user_id: this.operator.id,
      },
      select: {
        id: true,
        amount: true,
        status: true,
        atp: true,
        pay_transaction_id: true,
        pay_time: true,
        pay_callback_time: true,
        created_at: true,
      },
      orderBy: {
        created_at: "desc",
      },
    });

    return recharges.map((x) => {
      return {
        ...x,
        amount: x.amount.toNumber(),
        atp: x.atp.toNumber(),
      };
    });
  }

  async projects(): Promise<ProjectsRes[]> {
    const projects = await this.prisma.project.findMany({
      where: {
        created_by_id: this.operator.id,
        deleted_at: 0,
      },
      orderBy: {
        created_at: "desc",
      },
    });

    return projects;
  }

  async transactions(): Promise<TransactionsRes[]> {
    const transactions = await this.prisma.transaction.findMany({
      where: {
        user_id: this.operator.id,
      },
      select: {
        id: true,
        atp: true,
        source_type: true,
        source_info: true,
        created_at: true,
      },
    });

    return transactions.map((x) => {
      return {
        ...x,
        atp: x.atp.toNumber(),
      } as TransactionsRes;
    });
  }
}

export type RechargeOrdersRes = {
  id: string;
  amount: number;
  status: string;
  atp: number;
  pay_transaction_id: string | null;
  pay_time: Date | null;
  pay_callback_time: Date | null;
  created_at: Date;
};

export type TransactionsRes = {
  id: string;
  atp: number;
  created_at: Date;
  source_type: TransactionsSourceType;
  source_info: TransactionsSourceInfo;
};

export type ProjectsRes = {
  id: string;
  name: string;
  remark: string | null;
  created_at: Date;
  updated_at: Date;
};
