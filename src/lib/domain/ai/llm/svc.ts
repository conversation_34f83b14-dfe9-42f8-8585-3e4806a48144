import { IrsModel } from "@/lib/domain/ai/llm/irs";
import { ChatRunOptions } from "@/lib/domain/ai/llm/m";
import { LLMPvdSvc } from "@/lib/domain/ai/provider/svc";
import type { ATPSvc } from "@/lib/domain/atp/svc";
import { DomainError } from "@/lib/domain/common/error";
import type { ThreadSvc } from "@/lib/domain/thread/svc";
import type { Operator } from "@/lib/domain/user/m";
import type { PrismaClient } from "@prisma/client";
import {
  streamText,
  convertToModelMessages,
  CallSettings,
  ModelMessage,
  LanguageModel,
} from "ai";
import { customAlphabet } from "nanoid";
const nanoid = customAlphabet("1234567890abcdefghijklmnopqrstuvwxyz", 10);

export class LLMSvc {
  constructor(
    private readonly threadSvc: ThreadSvc,
    private readonly atpSvc: ATPSvc,
    private readonly llmPvdSvc: LLMPvdSvc
  ) {}

  streamText({
    model,
    messages,
    callSettings,
    abortSignal,
  }: {
    model: LanguageModel;
    messages: ModelMessage[];
    callSettings?: CallSettings;
    abortSignal?: AbortSignal;
  }) {
    const providerOptions = this.llmPvdSvc.getProviderOptions();
    const res = streamText({
      ...callSettings,
      model: model,
      messages,
      abortSignal,
      providerOptions,
    });

    return res;
  }

  async chat(
    prisma: PrismaClient,
    operator: Operator,
    options: ChatRunOptions
  ) {
    const { abortSignal, parentId, threadId, modelId, messages, callSettings } =
      options;

    await this.atpSvc.ensureWithAtp(prisma, operator);

    const lastMsg = messages.findLast((x) => x.role == "user");
    if (!lastMsg) {
      throw new DomainError("arg_parse_failed");
    }

    const lastUserMsgId = lastMsg.id;

    await this.threadSvc.findOrCreateThreadMessage(operator, {
      msgId: lastUserMsgId,
      msg: lastMsg,
      parentId,
      threadId,
    });

    const modelMessages = convertToModelMessages(messages);
    const irsLLM = new IrsModel(this.llmPvdSvc, modelId);
    const streamTextRes = this.streamText({
      model: irsLLM,
      messages: modelMessages,
      callSettings,
      abortSignal,
    });

    const assistantMsgId = nanoid();

    const uiMsgStream = streamTextRes.toUIMessageStream({
      sendReasoning: true,
      generateMessageId() {
        return assistantMsgId;
      },
      onFinish: async ({ responseMessage }) => {
        await streamTextRes.totalUsage.then(
          async (tokenUsage) => {
            await this.threadSvc.findOrCreateThreadMessage(operator, {
              msgId: assistantMsgId,
              msg: responseMessage,
              parentId: lastUserMsgId,
              threadId,
              consumption: {
                irsModelInfo: irsLLM.getInfo(),
                tokenUsage: tokenUsage,
              },
            });
          },
          async () => {
            await this.threadSvc.findOrCreateThreadMessage(operator, {
              msgId: assistantMsgId,
              msg: responseMessage,
              parentId: lastUserMsgId,
              threadId,
            });
          }
        );
      },
    });

    return uiMsgStream;
  }
}
