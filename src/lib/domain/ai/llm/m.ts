import type { IrsModelId } from "@/lib/domain/ai/llm/models";
import type { CallSettings, LanguageModelUsage, UIMessage } from "ai";

export type ChatRunOptions = {
  threadId: string;
  modelId: IrsModelId;
  messages: UIMessage[];
  parentId?: string;
  callSettings?: CallSettings;
  abortSignal?: AbortSignal;
};

/**
 * this is an extra filed that might be used in frontend,
 * so don't inclue sensitive info
 * @property modelId - The identifier of the language model used.
 * @property tokenUsage - The token usage details for the language model.
 */
export type ModelUsage = {
  modelId: IrsModelId;
  tokenUsage?: LanguageModelUsage;
};
