import { IrsModelId } from "@/lib/domain/ai/llm/models";
import { LLMPvdState, LLMPvdSvc } from "@/lib/domain/ai/provider/svc";
import { LanguageModelV2StreamPart, LanguageModelV2 } from "@ai-sdk/provider";
import { simulateReadableStream } from "ai";
import { MockLanguageModelV2 } from "ai/test";
import pRetry, { type RetryContext } from "p-retry";

export type IrsModelSetting = {
  useMocked?: boolean;
};

export type LLMPricing = {
  atpPerPromptToken: number;
  atpPerCompletionToken: number;
};

export type IrsModelInfo = {
  irsModel: IrsModelId;
  provider: string;
  model: string;
  pricing: LLMPricing;
};

export class IrsModel implements LanguageModelV2 {
  pvd: LLMPvdState;
  private _inner_llm: LanguageModelV2;

  readonly specificationVersion = "v2";

  get provider() {
    return this._inner_llm.provider;
  }

  get modelId() {
    return this._inner_llm.modelId;
  }

  get supportedUrls() {
    return this._inner_llm.supportedUrls;
  }

  getInfo(): IrsModelInfo {
    const pvdCfg = this.pvd.cfg;
    return {
      irsModel: this.irsModelId,
      provider: pvdCfg.provider,
      model: pvdCfg.model,
      pricing: pvdCfg.pricing,
    };
  }

  /**
   *
   */
  constructor(
    private readonly pvdSvc: LLMPvdSvc,
    readonly irsModelId: IrsModelId,
    readonly settings?: IrsModelSetting
  ) {
    this.pvd = this.pvdSvc.choosePvd(irsModelId);
    this._inner_llm = this.createInnerLLM();
  }

  async doStream(
    options: Parameters<LanguageModelV2["doStream"]>[0]
  ): Promise<Awaited<ReturnType<LanguageModelV2["doStream"]>>> {
    const res = await pRetry(
      async () => {
        return await this._inner_llm.doStream(options);
      },
      {
        maxTimeout: 1000,
        signal: options.abortSignal,
        shouldRetry: this.shouldRetry.bind(this),
        onFailedAttempt: this.onFailedAttempt.bind(this),
      }
    );

    return res;
  }

  async doGenerate(
    options: Parameters<LanguageModelV2["doGenerate"]>[0]
  ): Promise<Awaited<ReturnType<LanguageModelV2["doGenerate"]>>> {
    const res = await pRetry(
      async () => {
        return await this._inner_llm.doGenerate(options);
      },
      {
        maxTimeout: 1000,
        signal: options.abortSignal,
        shouldRetry: this.shouldRetry,
        onFailedAttempt: this.onFailedAttempt,
      }
    );
    return res;
  }

  private refreshPvd() {
    this.pvd = this.pvdSvc.choosePvd(this.irsModelId);
    this._inner_llm = this.createInnerLLM();
  }

  private createInnerLLM() {
    const mock = new MockLanguageModelV2({
      async doStream() {
        return {
          stream: simulateReadableStream<LanguageModelV2StreamPart>({
            chunks: [
              { type: "text-start", id: "text-1" },
              { type: "text-delta", id: "text-1", delta: "Hello" },
              { type: "text-delta", id: "text-1", delta: ", " },
              { type: "text-delta", id: "text-1", delta: "world!" },
              { type: "text-end", id: "text-1" },
              {
                type: "finish",
                finishReason: "stop",
                usage: { inputTokens: 3, outputTokens: 10, totalTokens: 13 },
              },
            ],
            chunkDelayInMs: 1000,
          }),
        };
      },
    });

    return this.settings?.useMocked ? mock : this.pvd.chatModel();
  }

  onFailedAttempt(ctx: RetryContext) {
    console.error("model request error: ", ctx);
    this.pvd.markAsUnavailable();
    this.refreshPvd();
  }

  shouldRetry(ctx: RetryContext) {
    const error = ctx.error;
    const retryableErrors = [
      "overloaded",
      "service unavailable",
      "bad gateway",
      "too many requests",
      "internal server error",
      "gateway timeout",
      "rate_limit",
      "unexpected",
      "capacity",
      "timeout",
      "server_error",
      "429", // Too Many Requests
    ];

    const retryableStatusCodes = [
      408, // request timeout
      409, // conflict
      413, // payload too large
      429, // too many requests/rate limits
    ];

    if ("statusCode" in error) {
      const statusCode = error["statusCode"] as number;
      const hitStatusCodeMatch =
        statusCode &&
        (retryableStatusCodes.includes(statusCode) || statusCode >= 500);

      if (hitStatusCodeMatch) {
        return true;
      }
    }

    const errorString = error.message.toLowerCase();
    const hitErrorMatch = retryableErrors.some((errType) =>
      errorString.includes(errType)
    );

    return hitErrorMatch;
  }
}

if (import.meta.vitest) {
  const { it, expect, vi } = import.meta.vitest;
  const { mock } = await import("vitest-mock-extended");

  it("should throw when error doesn't need to retry", async () => {
    const logSpy = vi.spyOn(console, "error").mockImplementation(() => {});
    const pvdSvc = mock<LLMPvdSvc>();
    const pvd1 = mock<LLMPvdState>();
    const llm1 = mock<LanguageModelV2>();
    pvd1.chatModel.mockReturnValue(llm1);
    pvdSvc.choosePvd.mockReturnValue(pvd1);
    llm1.doStream.mockRejectedValue(new Error("xxx"));
    const m = new IrsModel(pvdSvc, IrsModelId.DeepSeekR1);

    await expect(m.doStream({} as never)).rejects.toThrow("xxx");

    expect(logSpy).toHaveBeenCalledWith(
      "model request error: ",
      expect.anything()
    );
  });

  it("should fail over to anohter pvd when error needs to retry", async () => {
    const logSpy = vi.spyOn(console, "error").mockImplementation(() => {});
    const pvdSvc = mock<LLMPvdSvc>();
    const pvd1 = mock<LLMPvdState>();
    const llm1 = mock<LanguageModelV2>();
    pvd1.chatModel.mockReturnValue(llm1);
    llm1.doStream.mockRejectedValue(new Error("TOO many Requests"));

    const pvd2 = mock<LLMPvdState>();
    const llm2 = mock<LanguageModelV2>();
    pvd2.chatModel.mockReturnValue(llm2);
    llm2.doStream.mockResolvedValue("do-stream-res" as never);

    pvdSvc.choosePvd.mockReturnValueOnce(pvd1).mockReturnValueOnce(pvd2);

    const m = new IrsModel(pvdSvc, IrsModelId.DeepSeekR1);

    const res = await m.doStream({} as never);

    expect(res).toBe("do-stream-res");
    expect(logSpy).toHaveBeenCalledWith(
      "model request error: ",
      expect.objectContaining({
        error: expect.objectContaining({
          message: "TOO many Requests",
        }),
      })
    );
  });
}
