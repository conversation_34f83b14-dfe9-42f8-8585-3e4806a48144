import "server-only";

import { AppConfig, loadAppConfig } from "@/lib/app-config";
import { IrsModelId } from "@/lib/domain/ai/llm/models";
import { DomainError } from "@/lib/domain/common/error";
import type { SharedV2ProviderOptions } from "@ai-sdk/provider";
import {
  createOpenAICompatible,
  OpenAICompatibleProvider,
} from "@ai-sdk/openai-compatible";
import { LLMPricing } from "@/lib/domain/ai/llm/irs";

enum LLMPvdId {
  SiliconFlow = "siliconflow",
  ARK = "ark",
}

type LLMPvdAuth = {
  baseURL: string;
  apiKey: string;
};

type LLMPvdConfig = {
  provider: LLMPvdId;
  model: string;
  pricing: LLMPricing;
  weight: number;
};

export class LLMPvdState {
  dynamicWeight: number = 0;
  private lastFailedAt?: number;
  private llmFac: OpenAICompatibleProvider;

  constructor(readonly cfg: LLMPvdConfig, auth: LLMPvdAuth) {
    const llmFac = createOpenAICompatible({
      name: cfg.provider,
      baseURL: auth.baseURL,
      apiKey: auth.apiKey,
    });

    this.llmFac = llmFac;
  }

  markAsUnavailable() {
    this.lastFailedAt = Date.now();
  }

  markAsAvailable() {
    this.lastFailedAt = undefined;
  }

  isAvailable(resetInterval: number) {
    if (this.lastFailedAt == undefined) {
      return true;
    }

    const needsReset = Date.now() - this.lastFailedAt > resetInterval;
    if (needsReset) {
      this.markAsAvailable();
      return true;
    } else {
      return false;
    }
  }

  chatModel() {
    return this.llmFac.chatModel(this.cfg.model);
  }
}

export class LLMPvdSvc {
  private readonly providerAuths: Record<LLMPvdId, LLMPvdAuth>;
  private readonly providerOptions: SharedV2ProviderOptions;
  private readonly llmPvds: Record<IrsModelId, LLMPvdState[]>;

  constructor(
    appConfig: AppConfig,
    llmPvds?: Record<IrsModelId, LLMPvdState[]>,
    private readonly resetInterval: number = 3 * 60 * 1000
  ) {
    this.providerAuths = {
      [LLMPvdId.SiliconFlow]: {
        baseURL: "https://api.siliconflow.cn/v1",
        apiKey: appConfig.SILICON_API_KEY,
      },
      [LLMPvdId.ARK]: {
        baseURL: "https://ark.cn-beijing.volces.com/api/v3",
        apiKey: appConfig.ARK_API_KEY,
      },
    };

    this.providerOptions = {
      [LLMPvdId.ARK]: {
        stream_options: {
          include_usage: true,
        },
      },
    };

    this.llmPvds = llmPvds ?? {
      [IrsModelId.DeepSeekR1]: [
        {
          provider: LLMPvdId.SiliconFlow,
          model: "Pro/deepseek-ai/DeepSeek-R1",
          weight: 1,
          pricing: { atpPerPromptToken: 4.8, atpPerCompletionToken: 19.2 },
        },
        {
          provider: LLMPvdId.ARK,
          model: "deepseek-r1-250528",
          weight: 1,
          pricing: { atpPerPromptToken: 4.8, atpPerCompletionToken: 19.2 },
        },
      ].map((x) => new LLMPvdState(x, this.providerAuths[x.provider])),
      [IrsModelId.DeepSeekV3]: [
        {
          provider: LLMPvdId.SiliconFlow,
          model: "Pro/deepseek-ai/DeepSeek-V3.1",
          weight: 1,
          pricing: { atpPerPromptToken: 4.8, atpPerCompletionToken: 14.4 },
        },
        {
          provider: LLMPvdId.ARK,
          model: "deepseek-v3-1-250821",
          weight: 1,
          pricing: { atpPerPromptToken: 4.8, atpPerCompletionToken: 14.4 },
        },
      ].map((x) => new LLMPvdState(x, this.providerAuths[x.provider])),
      [IrsModelId.Qwen3235BInstruct]: [
        {
          provider: LLMPvdId.SiliconFlow,
          model: "Qwen/Qwen3-235B-A22B",
          weight: 1,
          pricing: { atpPerPromptToken: 3, atpPerCompletionToken: 12 },
        },
      ].map((x) => new LLMPvdState(x, this.providerAuths[x.provider])),
      [IrsModelId.Qwen27BInstruct]: [
        {
          provider: LLMPvdId.SiliconFlow,
          model: "Pro/Qwen/Qwen2-7B-Instruct",
          weight: 1,
          pricing: { atpPerPromptToken: 0.42, atpPerCompletionToken: 0.42 },
        },
      ].map((x) => new LLMPvdState(x, this.providerAuths[x.provider])),
    };
  }

  getProviderOptions(): SharedV2ProviderOptions {
    return this.providerOptions;
  }

  // 负载均衡算法：基于权重的轮询调度
  choosePvd(modelId: IrsModelId): LLMPvdState {
    let bestProvider: LLMPvdState | undefined;
    let totalWeight = 0;

    for (const provider of this.llmPvds[modelId]) {
      if (!provider.isAvailable(this.resetInterval)) {
        continue;
      }

      provider.dynamicWeight += provider.cfg.weight;
      totalWeight += provider.cfg.weight;

      if (
        !bestProvider ||
        provider.dynamicWeight > bestProvider.dynamicWeight
      ) {
        bestProvider = provider;
      }
    }

    if (!bestProvider) {
      throw new DomainError("no_available_provider");
    }

    bestProvider.dynamicWeight -= totalWeight;
    return bestProvider;
  }
}

if (import.meta.vitest) {
  const { it, expect, vi } = import.meta.vitest;
  const testModleId = IrsModelId.DeepSeekR1;

  async function getSvc(
    weights: { pvd: LLMPvdId; weight: number }[],
    interval?: number
  ) {
    const appConfig = await loadAppConfig();
    const fakeAuth = { baseURL: "fake", apiKey: "fake" };
    const fakePricing = { atpPerPromptToken: 4.8, atpPerCompletionToken: 19.2 };
    const pvds = {
      [testModleId]: weights.map((x) => {
        const cfg = {
          provider: x.pvd,
          model: "m",
          weight: x.weight,
          pricing: fakePricing,
        };
        return new LLMPvdState(cfg, fakeAuth);
      }),
    };
    const pvdSvc = new LLMPvdSvc(appConfig, pvds as never, interval);
    return pvdSvc;
  }

  it("should load pvd by its weight", async () => {
    const sw = 2;
    const aw = 1;

    const pvdSvc = await getSvc([
      { pvd: LLMPvdId.SiliconFlow, weight: sw },
      { pvd: LLMPvdId.ARK, weight: aw },
    ]);

    const chosen: string[] = [];
    const totalCount = (sw + aw) * 5;
    for (let i = 0; i < totalCount; i++) {
      const pvd = pvdSvc.choosePvd(testModleId);
      chosen.push(pvd.cfg.provider);
    }

    const sfCount = chosen.filter((x) => x == LLMPvdId.SiliconFlow).length;
    const arkCount = chosen.filter((x) => x == LLMPvdId.ARK).length;
    expect(sfCount / arkCount).toBe(sw / aw);
  });

  it("should skip unavailable providers", async () => {
    const sw = 1;
    const aw = 1;

    const pvdSvc = await getSvc([
      { pvd: LLMPvdId.SiliconFlow, weight: sw },
      { pvd: LLMPvdId.ARK, weight: aw },
    ]);

    let chosen: string[] = [];
    let firstChoosen!: LLMPvdState;
    let testCount = 5;
    for (let i = 0; i < testCount; i++) {
      const pvd = pvdSvc.choosePvd(testModleId);
      if (firstChoosen == undefined) {
        firstChoosen = pvd;
        firstChoosen.markAsUnavailable();
      }

      chosen.push(pvd.cfg.provider);
    }

    let firstChoosenCount = chosen.filter(
      (x) => x == firstChoosen.cfg.provider
    ).length;
    let restChoosenCount = chosen.filter(
      (x) => x != firstChoosen.cfg.provider
    ).length;
    expect(firstChoosenCount).toEqual(1);
    expect(restChoosenCount).toEqual(testCount - 1);

    // recovery first pvd
    firstChoosen.markAsAvailable();
    chosen = [];
    testCount = 6;
    for (let i = 0; i < testCount; i++) {
      const pvd = pvdSvc.choosePvd(testModleId);
      chosen.push(pvd.cfg.provider);
    }

    firstChoosenCount = chosen.filter(
      (x) => x == firstChoosen.cfg.provider
    ).length;
    restChoosenCount = chosen.filter(
      (x) => x != firstChoosen.cfg.provider
    ).length;

    expect(firstChoosenCount).toBe(restChoosenCount);
  });

  it("should auto reset unavailable providers after interval", async () => {
    const sw = 1;
    const aw = 1;
    const resetInterval = 1000 * 60;
    const pvdSvc = await getSvc(
      [
        { pvd: LLMPvdId.SiliconFlow, weight: sw },
        { pvd: LLMPvdId.ARK, weight: aw },
      ],
      resetInterval
    );

    vi.useFakeTimers({ now: 0 });
    const firstChoosen = pvdSvc.choosePvd(testModleId);
    firstChoosen.markAsUnavailable();

    const chosen: string[] = [];
    const testCount = 10;
    for (let i = 0; i < testCount; i++) {
      if (i == 5) {
        vi.advanceTimersByTime(resetInterval + 1);
      }
      const pvd = pvdSvc.choosePvd(testModleId);
      chosen.push(pvd.cfg.provider);
    }

    expect(chosen).toEqual([
      "ark",
      "ark",
      "ark",
      "ark",
      "ark",
      "ark",
      "siliconflow",
      "ark",
      "siliconflow",
      "ark",
    ]);
  });
}
