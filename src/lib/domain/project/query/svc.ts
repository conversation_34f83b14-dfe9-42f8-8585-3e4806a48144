import { DomainError } from "@/lib/domain/common/error";
import { getProject } from "@/lib/domain/project/dbt";
import { PrismaClient } from "@prisma/client";

export class ProjectQuerySvc {
  constructor(private readonly prisma: PrismaClient) {}

  async getProject(projectId: string) {
    const project = await getProject(this.prisma, projectId);

    if (!project) {
      throw new DomainError("project_not_found");
    }

    return project;
  }
}

if (import.meta.vitest) {
  const { describe, it, expect, vi } = import.meta.vitest;

  const getSUT = async () => {
    // we can use this to doMock dbt, not rely on hoisted mock
    vi.resetModules();
    const { ProjectQuerySvc } = await import("./svc");
    const prisma = {} as PrismaClient;
    const svc = new ProjectQuerySvc(prisma);
    return svc;
  };

  describe("getProject", () => {
    it("should throw if project not found", async () => {
      vi.doMock("@/lib/domain/project/dbt", () => ({
        getProject: vi.fn().mockResolvedValueOnce(null),
      }));

      const svc = await getSUT();

      await expect(svc.getProject("uuidxa")).rejects.toThrow(
        "project_not_found"
      );
    });

    it("should return project if found", async () => {
      const mockedProject = {
        id: "uuidxa",
        name: "test-prj",
      };
      vi.doMock("@/lib/domain/project/dbt", () => ({
        getProject: vi.fn().mockResolvedValue(mockedProject),
      }));

      const svc = await getSUT();
      const project = await svc.getProject("uuidxa");
      expect(project).toEqual(mockedProject);
    });
  });
}
