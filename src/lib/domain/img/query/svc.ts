import { getItem } from "@/lib/domain/item/dbt";
import { ItemQuerySvc } from "@/lib/domain/item/query/svc";
import { get_prjId_by_piid_path, ItemTypeFlag } from "@/lib/domain/item/svc";
import { GazeClient, GetImgTileReq } from "@/lib/infra/gaze/client";
import { PrismaClient } from "@prisma/client";

type GetImgTileOptions = Omit<GetImgTileReq, "id"> & {
  imgId: string;
};

type GetMinMaxByQuantileOptions = {
  id: string;
  channel_id: number;
  min_quantile?: number | null;
  max_quantile?: number | null;
  in_unique: boolean;
};

type Channel = {
  id: number;
  name: string;
  min: number;
  max: number;
  view_min?: number | null;
  view_max?: number | null;
  view_shown?: boolean | null;
  color?: string | null;
};

export type ImgMeta = {
  tile_size: number;
  base_x: number;
  base_y: number;
  min_level: number;
  max_level: number;
  dtype: string;
  channels: Channel[];
  axes: string;
  shape: number[];
  phys_x?: number | null;
  phys_x_unit?: string | null;
  phys_y?: number | null;
  phys_y_unit?: string | null;
  phys_z?: number | null;
  phys_z_unit?: string | null;
  data_version: string;
  view_gamma?: number | null;
};

export type ImgInfo = {
  id: string;
  name: string;
  imgMeta: ImgMeta;
  prjId: string;
};

export class ImgQuerySvc {
  constructor(
    private prisma: PrismaClient,
    private gazeClient: GazeClient,
    private itemQuerySvc: ItemQuerySvc
  ) {}

  async getImgTile(options: GetImgTileOptions) {
    const tdbGroupUri = await this.itemQuerySvc.getTdbGroupUriById({
      itemId: options.imgId,
    });

    const req = {
      ...options,
      id: tdbGroupUri,
    };
    const res = this.gazeClient.getImgTile(req);
    return res;
  }

  async imgInfo({ itemId }: { itemId: string }) {
    const item = await getItem(this.prisma, {
      itemId,
      typeFlag: ItemTypeFlag.File,
    });

    const tdbGroupUri = await this.itemQuerySvc.getTdbGroupUri(item);
    const remoteRes = await this.gazeClient.getImgInfo({ tdbGroupUri });
    const prjId = get_prjId_by_piid_path(item.piid_path);

    const imgInfo: ImgInfo = {
      id: item.id,
      name: item.name,
      imgMeta: remoteRes.img_meta,
      prjId,
    };
    return imgInfo;
  }

  async imgOriginalMeta({ itemId }: { itemId: string }) {
    const tdbGroupUri = await this.itemQuerySvc.getTdbGroupUriById({ itemId });
    const remoteRes = await this.gazeClient.getImgOriginalMeta({
      tdbGroupUri,
    });
    return remoteRes;
  }

  async getMinMaxByQuantile(options: GetMinMaxByQuantileOptions) {
    const tdbGroupUri = await this.itemQuerySvc.getTdbGroupUriById({
      itemId: options.id,
    });
    const remoteRes = await this.gazeClient.getMinMaxByQuantile({
      ...options,
      id: tdbGroupUri,
    });
    return remoteRes;
  }

  async getHistogram({ itemId }: { itemId: string }) {
    const tdbGroupUri = await this.itemQuerySvc.getTdbGroupUriById({
      itemId,
    });
    const remoteRes = await this.gazeClient.getHistogram({ tdbGroupUri });
    return remoteRes;
  }
}

if (import.meta.vitest) {
  const { describe, it, expect } = await import("vitest");
  const { mock } = await import("vitest-mock-extended");

  const gazeClient = mock<GazeClient>();
  const itemQuerySvc = mock<ItemQuerySvc>();
  const prisma = mock<PrismaClient>();
  const imgQuerySvc = new ImgQuerySvc(prisma, gazeClient, itemQuerySvc);

  describe("get min max by quantile", () => {
    it("should get min max by quantile", async () => {
      const tdbGroupUri = "s3://test-bkt/project/proj-id-xxx/item/item-id-xxx/";
      itemQuerySvc.getTdbGroupUriById.mockResolvedValueOnce(tdbGroupUri);

      await imgQuerySvc.getMinMaxByQuantile({
        id: "item-id-xxx",
        channel_id: 0,
        in_unique: false,
      });

      expect(gazeClient.getMinMaxByQuantile).toHaveBeenCalledWith({
        id: tdbGroupUri,
        channel_id: 0,
        in_unique: false,
      });
    });
  });

  describe("get histogram", () => {
    it("should get histogram", async () => {
      const tdbGroupUri = "s3://test-bkt/project/proj-id-xxx/item/item-id-xxx/";
      itemQuerySvc.getTdbGroupUriById.mockResolvedValueOnce(tdbGroupUri);

      await imgQuerySvc.getHistogram({ itemId: "item-id-xxx" });

      expect(gazeClient.getHistogram).toHaveBeenCalledWith({
        tdbGroupUri,
      });
    });
  });
}
