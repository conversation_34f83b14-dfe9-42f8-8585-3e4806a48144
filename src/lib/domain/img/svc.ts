import { ItemQuerySvc } from "@/lib/domain/item/query/svc";
import { GazeClient, type SaveImgMetaReq } from "@/lib/infra/gaze/client";
import { PrismaClient } from "@prisma/client";

export class ImgSvc {
  constructor(
    private prisma: PrismaClient,
    private itemQuerySvc: ItemQuerySvc,
    private gazeClient: GazeClient
  ) {}

  async saveImgMeta(options: SaveImgMetaReq) {
    const tdbGroupUri = await this.itemQuerySvc.getTdbGroupUriById({
      itemId: options.id,
    });
    await this.gazeClient.saveImgMeta({
      ...options,
      id: tdbGroupUri,
    });
  }
}
