import { IrsModelInfo } from "@/lib/domain/ai/llm/irs";
import type { ATPSvc } from "@/lib/domain/atp/svc";
import type { ChatModelConfig, ThreadCreateDto } from "@/lib/domain/thread/m";
import type { Operator } from "@/lib/domain/user/m";
import type { PrismaClient } from "@prisma/client";
import type { InputJsonValue } from "@prisma/client/runtime/library";
import type { LanguageModelUsage, UIMessage } from "ai";

export class ThreadSvc {
  constructor(
    private readonly atpSvc: ATPSvc,
    private readonly prisma: PrismaClient
  ) {}

  async updateThreadTitle(
    prisma: PrismaClient,
    dto: { threadId: string; title: string },
    operator: Operator
  ) {
    const title = dto.title.slice(0, 32);
    if (title.length == 0) {
      return;
    }

    await prisma.thread.update({
      where: {
        id: dto.threadId,
        user_id: operator.id,
      },
      data: {
        title: title,
      },
    });
  }

  async createThread(
    prisma: PrismaClient,
    operator: Operator,
    data: ThreadCreateDto
  ) {
    const result = await prisma.thread.create({
      data: {
        user_id: operator.id,
        title: data.title,
        last_message_at: new Date(),
        metadata: data.metadata as InputJsonValue,
      },
      select: {
        id: true,
      },
    });

    return result;
  }

  async updateThreadModelConfig(
    prisma: PrismaClient,
    operator: Operator,
    threadId: string,
    config: ChatModelConfig
  ) {
    await prisma.thread.update({
      where: {
        id: threadId,
        user_id: operator.id,
      },
      data: { model_config: config },
    });
  }

  async findOrCreateThreadMessage(
    operator: Operator,
    {
      msgId,
      msg,
      parentId,
      threadId,
      consumption,
    }: {
      msgId: string;
      msg: UIMessage;
      parentId?: string;
      threadId: string;
      consumption?: {
        irsModelInfo: IrsModelInfo;
        tokenUsage: LanguageModelUsage;
      };
    }
  ) {
    if (consumption) {
      await this.atpSvc.consumeTokens(this.prisma, operator, {
        threadId: threadId,
        msgId: msgId,
        tokenUsage: consumption.tokenUsage,
        irsModelInfo: consumption.irsModelInfo,
      });
    }

    const [createdMsg] = await findOrCreateThreadMessageAndUpdateThreadDate(
      this.prisma,
      operator,
      {
        threadId,
        msgId,
        msg,
        parentId,
      }
    );

    return createdMsg;
  }
}

function refreshThreadDate(
  prisma: PrismaClient,
  operator: Operator,
  threadId: string
) {
  const op = prisma.thread.update({
    where: {
      id: threadId,
      user_id: operator.id,
    },
    data: {
      last_message_at: new Date(),
    },
  });
  return op;
}

function findOrCreateThreadMessage(
  prisma: PrismaClient,
  {
    threadId,
    msgId,
    msg,
    parentId,
  }: {
    threadId: string;
    msgId: string;
    msg: UIMessage;
    parentId?: string;
  }
) {
  const op = prisma.message.upsert({
    where: {
      thread_id_id: {
        thread_id: threadId,
        id: msgId,
      },
    },
    create: {
      id: msgId,
      thread_id: threadId,
      parent_id: parentId,
      format: "ai-sdk/v5",
      content: msg as unknown as InputJsonValue,
    },
    update: {},
  });

  return op;
}

async function findOrCreateThreadMessageAndUpdateThreadDate(
  prisma: PrismaClient,
  operator: Operator,
  {
    threadId,
    msgId,
    msg,
    parentId,
  }: {
    threadId: string;
    msgId: string;
    msg: UIMessage;
    parentId?: string;
  }
) {
  const res = await prisma.$transaction([
    findOrCreateThreadMessage(prisma, {
      threadId,
      msgId,
      msg,
      parentId,
    }),
    refreshThreadDate(prisma, operator, threadId),
  ]);

  return res;
}
