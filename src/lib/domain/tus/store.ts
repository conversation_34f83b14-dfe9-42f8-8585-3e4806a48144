import { <PERSON><PERSON><PERSON><PERSON> } from "@/lib/domain/disk/svc";
import { S3Store } from "@tus/s3-store";

export class IrsS3Store extends S3Store {
  protected infoKey(id: string): string {
    return generateInfoKey(id);
  }
}

function transformDiskPathToInfoPath(diskPath: string) {
  return OSSKey.underDiskUploadTusInfo(diskPath);
}

export function generateInfoKey(diskPath: string) {
  const infoPath = transformDiskPathToInfoPath(diskPath);
  return `${infoPath}.info`;
}

if (import.meta.vitest) {
  const { describe, it, expect } = import.meta.vitest;

  describe("transformDiskPathToInfoPath", () => {
    it("transform disk path to info path", () => {
      const diskPath = "project/prjId/disk/user-folder/user-file.txt";
      const infoPath = transformDiskPathToInfoPath(diskPath);
      expect(infoPath).toBe(
        "disk-upload-tus-info/project/prjId/disk/user-folder/user-file.txt"
      );
    });
  });

  describe("generateInfoKey", () => {
    it("generate info key", () => {
      const diskPath = "project/prjId/disk/user-folder/user-file.txt";
      const infoKey = generateInfoKey(diskPath);
      expect(infoKey).toBe(
        "disk-upload-tus-info/project/prjId/disk/user-folder/user-file.txt.info"
      );
    });
  });
}
