import { AppConfig } from "@/lib/app-config";
import { DomainError } from "@/lib/domain/common/error";
import { DiskSvc, OSSKey } from "@/lib/domain/disk/svc";
import { getItem } from "@/lib/domain/item/dbt";
import { ItemQuerySvc } from "@/lib/domain/item/query/svc";
import {
  createItem,
  get_item_iid_path,
  get_root_iid_path,
  ItemSvc,
  ItemTypeFlag,
} from "@/lib/domain/item/svc";
import {
  addTask,
  bindRemoteTask,
  getTaskByRemoteTaskId,
  recordTaskError,
  resetTaskStateToStart,
  updateTaskRemoteStatus,
} from "@/lib/domain/task/dbt";
import { Operator } from "@/lib/domain/user/m";
import { RestateClient } from "@/lib/infra/restate/client";
import { S3Client } from "@/lib/infra/s3/client";
import { PrismaClient, Task } from "@prisma/client";
import dayjs from "dayjs";
import { nanoid } from "nanoid";
import path from "path";
import wretch, { type Wretch } from "wretch";

export enum TaskType {
  ingest = "ingest",
  rescale_intensity = "rescale-intensity",
  clip_intensity = "clip-intensity",
  bit_depth_convert = "bit-depth-convert",
  flip = "flip",
  save_as = "save-as",
  register = "register",
  merge_channels = "merge-channels",
}

export enum TaskRemoteInnerState {
  NIL = "NIL",
  PENDING_ARGS_AVAIL = "PENDING_ARGS_AVAIL",
  PENDING_NODE_ASSIGNMENT = "PENDING_NODE_ASSIGNMENT",
  PENDING_OBJ_STORE_MEM_AVAIL = "PENDING_OBJ_STORE_MEM_AVAIL",
  PENDING_ARGS_FETCH = "PENDING_ARGS_FETCH",
  SUBMITTED_TO_WORKER = "SUBMITTED_TO_WORKER",
  PENDING_ACTOR_TASK_ARGS_FETCH = "PENDING_ACTOR_TASK_ARGS_FETCH",
  PENDING_ACTOR_TASK_ORDERING_OR_CONCURRENCY = "PENDING_ACTOR_TASK_ORDERING_OR_CONCURRENCY",
  RUNNING = "RUNNING",
  RUNNING_IN_RAY_GET = "RUNNING_IN_RAY_GET",
  RUNNING_IN_RAY_WAIT = "RUNNING_IN_RAY_WAIT",
  FINISHED = "FINISHED",
  FAILED = "FAILED",
}

export enum TaskStatus {
  Running = "Running",
  Finished = "Finished",
  Failed = "Failed",
}

export type TaskReq =
  | IngestTaskReq
  | SaveAsTaskReq
  | RescaleIntensityTaskReq
  | ClipIntensityTaskReq
  | BitDepthConvertTaskReq
  | FlipTaskReq
  | RegisterTaskReq
  | MergeChannelsTaskReq;

export type TaskArgs = TaskReq["taskArgs"];

type IngestTaskReq = {
  taskType: TaskType.ingest;
  taskArgs: {
    projectId: string;
    sourcePath: string;
    outputFolderId?: string;
    outputName: string;
  };
};

type SaveAsTaskReq = {
  taskType: TaskType.save_as;
  taskArgs: {
    itemId: string;
    setting: {
      channelChosenType: "shown" | "all";
      splitChannel?: boolean;
      saveAsType: "irs" | "tiff" | "jpg";
      selectedChannels: {
        id: number;
        name: string;
      }[];
      region?: XYRegion;
    };
    output: {
      folder_name: string;
      tgt_folder_id?: string;
    };
  };
};

type RescaleIntensityTaskReq = {
  taskType: TaskType.rescale_intensity;
  taskArgs: Omit<SaveAsTaskReq["taskArgs"], "setting"> & {
    setting: Omit<SaveAsTaskReq["taskArgs"]["setting"], "selectedChannels"> & {
      selectedChannels: {
        id: number;
        name: string;
        rescale_range: {
          min: number;
          max: number;
        };
      }[];
    };
  };
};

type ClipIntensityTaskReq = {
  taskType: TaskType.clip_intensity;
  taskArgs: Omit<SaveAsTaskReq["taskArgs"], "setting"> & {
    setting: Omit<SaveAsTaskReq["taskArgs"]["setting"], "selectedChannels"> & {
      selectedChannels: {
        id: number;
        name: string;
        clip_range: {
          min: number;
          max: number;
        };
      }[];
    };
  };
};

type BitDepthConvertTaskReq = {
  taskType: TaskType.bit_depth_convert;
  taskArgs: SaveAsTaskReq["taskArgs"] & {
    setting: SaveAsTaskReq["taskArgs"]["setting"] & {
      tgt_bit_depth: "uint8" | "uint16" | "float32";
    };
  };
};

type FlipTaskReq = {
  taskType: TaskType.flip;
  taskArgs: SaveAsTaskReq["taskArgs"] & {
    setting: SaveAsTaskReq["taskArgs"]["setting"] & {
      flip_dims: "X" | "Y" | "XY";
    };
  };
};

type RegisterTaskReq = {
  taskType: TaskType.register;
  taskArgs: {
    input: {
      inputImgs: { key: string; title: string }[];
      outputFolderId: string;
    };
    output: {
      outputImgName: string;
      mergeChannels: Record<string, number[]>;
    };
    parameters: {
      TransformType?: string;
      NumberOfResolutions?: number;
      NumberOfSpatialSamples?: number;
      MaximumNumberOfIterations?: number;
      FixedImagePyramid?: string;
      MovingImagePyramid?: string;
      ImageSampler?: string;
      Interpolator?: string;
      ResampleInterpolator?: string;
      Metric?: string;
      Optimizer?: string;
      nonParams: {
        fixedImgKey: string;
        regChannels: Record<
          string,
          {
            label: string;
            value: number;
          }
        >;
        channels: Record<string, { label: string; value: number }[]>;
        registerMethod: "elastix" | "sift" | "orb";
      };
    };
  };
};

type MergeChannelsTaskReq = {
  taskType: TaskType.merge_channels;
  taskArgs: {
    input: {
      inputImgs: {
        key: string;
        title: string;
      }[];
    };
    output: {
      outputImgName: string;
      outputFolderId: string;
    };
    channels: {
      imgInfos: Record<
        string,
        {
          channelOptions: [
            {
              label: string;
              value: number;
            }
          ];
          imgX: number;
          imgY: number;
        }
      >;
      mergeChannels: Record<string, number[]>;
    };
  };
};

type IngestExtra = {
  decodedItemId: string;
};

type SaveAsExtra = {
  outputItemIds: string[];
  outputsMoveInfo?: {
    srcKey: string;
    dstKey: string;
  };
};

type MergeChannelsExtra = {
  outputImgId: string;
};

type RegisterExtra = {
  outputImgId: string;
};

export type TaskExtra =
  | IngestExtra
  | SaveAsExtra
  | RegisterExtra
  | MergeChannelsExtra;

export class TaskSvc {
  private remoteTaskApi: Wretch;
  constructor(
    private readonly itemQuerySvc: ItemQuerySvc,
    private readonly itemSvc: ItemSvc,
    private readonly prisma: PrismaClient,
    appConfig: AppConfig,
    private readonly rsClient: RestateClient,
    private readonly s3Client: S3Client,
    private readonly diskSvc: DiskSvc
  ) {
    this.remoteTaskApi = wretch(appConfig.REMOTE_TASK_API_URL);
  }

  async runIngest({ task, operator }: { task: Task; operator: Operator }) {
    const taskId = task.id;

    await this.itemSvc.clearTaskItems(operator, { taskId });

    const { projectId, sourcePath, outputFolderId, outputName } =
      task.task_args as IngestTaskReq["taskArgs"];

    let output_folder_iid_path = get_root_iid_path(projectId);
    if (outputFolderId) {
      const output_folder = await getItem(this.prisma, {
        itemId: outputFolderId,
        typeFlag: ItemTypeFlag.Folder,
      });

      output_folder_iid_path = get_item_iid_path(output_folder);
    }

    const decodedItem = await createItem(this.prisma, {
      piid_path: output_folder_iid_path,
      typeFlag: ItemTypeFlag.File,
      name: outputName,
      taskId: taskId,
      operator: operator,
    });

    const sourceOssKey = OSSKey.underProjectDisk({ projectId }, sourcePath);
    const decodedItemTdbGroupUri = await this.itemQuerySvc.getTdbGroupUri(
      decodedItem
    );

    const remoteTaskId = await this.startRemoteTask({
      task_id: taskId,
      remote_task_req: {
        task_type: "ingest",
        task_args: {
          source_path: sourceOssKey,
          output_path: decodedItemTdbGroupUri,
        },
      },
    });

    const extra = {
      decodedItemId: decodedItem.id,
    } as IngestExtra;

    await this.bindRemoteTaskAndUpdateStatus({
      taskId: taskId,
      remoteTaskId: remoteTaskId,
      extra: extra,
    });
  }

  async runFlip({ task, operator }: { task: Task; operator: Operator }) {
    await this._runSaveAsTask(
      task,
      operator,
      (remoteReq, taskArgs: FlipTaskReq["taskArgs"]) => {
        remoteReq.task_args.req.flip_axes = {
          dims: taskArgs.setting.flip_dims,
        };
      }
    );
  }

  async runRescaleIntensity({
    task,
    operator,
  }: {
    task: Task;
    operator: Operator;
  }) {
    await this._runSaveAsTask(
      task,
      operator,
      (remoteReq, taskArgs: RescaleIntensityTaskReq["taskArgs"]) => {
        const {
          setting: { selectedChannels: channels },
        } = taskArgs;

        remoteReq.task_args.req.channel_outputs.forEach((co) => {
          co.channel_filters = co.channel_filters.map((ch) => {
            const range = channels.find((c) => c.id === ch.idx)!.rescale_range;
            return {
              idx: ch.idx,
              rescale_range: [range.min, range.max],
            };
          });
        });
      }
    );
  }

  async runBitDepthConvert({
    task,
    operator,
  }: {
    task: Task;
    operator: Operator;
  }) {
    await this._runSaveAsTask(
      task,
      operator,
      (remoteReq, taskArgs: BitDepthConvertTaskReq["taskArgs"]) => {
        const {
          setting: { tgt_bit_depth },
        } = taskArgs;
        remoteReq.task_args.req.tgt_bit_depth = tgt_bit_depth;
      }
    );
  }

  async runClipIntensity({
    task,
    operator,
  }: {
    task: Task;
    operator: Operator;
  }) {
    await this._runSaveAsTask(
      task,
      operator,
      (remoteReq, taskArgs: ClipIntensityTaskReq["taskArgs"]) => {
        const {
          setting: { selectedChannels: channels },
        } = taskArgs;

        remoteReq.task_args.req.channel_outputs.forEach((ch) => {
          ch.channel_filters = ch.channel_filters.map((ch) => {
            const range = channels.find((c) => c.id === ch.idx)!.clip_range;
            return {
              idx: ch.idx,
              clip_range: [range.min, range.max],
            };
          });
        });
      }
    );
  }

  async runSaveAs({ task, operator }: { task: Task; operator: Operator }) {
    await this._runSaveAsTask(task, operator);
  }

  async runRegister({ task, operator }: { task: Task; operator: Operator }) {
    const { id: taskId, project_id: projectId } = task;

    const taskArgs = task.task_args as RegisterTaskReq["taskArgs"];

    await this.itemSvc.clearTaskItems(operator, { taskId });

    const {
      parameters: {
        nonParams: { fixedImgKey, regChannels, registerMethod },
        ...regParams
      },
      input: { inputImgs, outputFolderId },
      output: { outputImgName, mergeChannels },
    } = taskArgs;

    const merge_imgs: MergeImg[] = [];
    const moving_imgs: ModalityImg[] = [];
    let fixed_img: ModalityImg | undefined;

    for (const img of inputImgs) {
      const img_id = img.key;
      const tdb_group_uri = await this.itemQuerySvc.getTdbGroupUriById({
        itemId: img_id,
      });
      const channels = mergeChannels[img_id];
      const regChannel = regChannels[img_id];

      if (channels) {
        merge_imgs.push({
          id: tdb_group_uri,
          channels: channels,
        });
      }

      if (regChannel) {
        const channelId = regChannel.value;
        const imgModality = {
          id: tdb_group_uri,
          channel_id: channelId,
        };
        if (img_id === fixedImgKey) {
          fixed_img = imgModality;
        } else {
          moving_imgs.push(imgModality);
        }
      }
    }

    if (!fixed_img) {
      throw new DomainError("arg_parse_failed", taskArgs);
    }

    const targetFolder = await getItem(this.prisma, {
      itemId: outputFolderId,
      typeFlag: ItemTypeFlag.Folder,
    });

    const outputFolder = await createItem(this.prisma, {
      piid_path: get_item_iid_path(targetFolder),
      typeFlag: ItemTypeFlag.Folder,
      name: outputImgName,
      taskId,
      operator,
    });

    const outputFile = await createItem(this.prisma, {
      piid_path: get_item_iid_path(outputFolder),
      name: replaceSuffix(outputImgName, ".irs"),
      typeFlag: ItemTypeFlag.File,
      taskId,
      operator,
    });

    const outputImgTdbGroupUri = await this.itemQuerySvc.getTdbGroupUri(
      outputFile
    );

    const remoteReq: RemoteTaskRegisterReq = {
      task_type: "register_and_merge",
      task_args: {
        req: {
          output_folder_path: OSSKey.underProjectTask({ taskId, projectId }),
          output_img_id: outputImgTdbGroupUri,
          merge_imgs,
          fixed_img,
          moving_imgs,
          reg_params: regParams,
          register_method: registerMethod,
        },
      },
    };

    const extra: RegisterExtra = {
      outputImgId: outputFile.id,
    };

    const remoteTaskId = await this.startRemoteTask({
      task_id: taskId,
      remote_task_req: remoteReq,
    });

    await this.bindRemoteTaskAndUpdateStatus({
      taskId: taskId,
      remoteTaskId: remoteTaskId,
      extra: extra,
    });
  }

  async runMergeChannels({
    task,
    operator,
  }: {
    task: Task;
    operator: Operator;
  }) {
    const taskId = task.id;
    const taskArgs = task.task_args as MergeChannelsTaskReq["taskArgs"];
    const {
      input: { inputImgs },
      output: { outputFolderId, outputImgName },
      channels: { mergeChannels },
    } = taskArgs;

    await this.itemSvc.clearTaskItems(operator, { taskId });

    const targetFolder = await getItem(this.prisma, {
      itemId: outputFolderId,
      typeFlag: ItemTypeFlag.Folder,
    });

    const outputFolder = await createItem(this.prisma, {
      piid_path: get_item_iid_path(targetFolder),
      typeFlag: ItemTypeFlag.Folder,
      name: outputImgName,
      taskId,
      operator,
    });

    const outputFile = await createItem(this.prisma, {
      piid_path: get_item_iid_path(outputFolder),
      name: replaceSuffix(outputImgName, ".irs"),
      typeFlag: ItemTypeFlag.File,
      taskId,
      operator,
    });

    const outputImgTdbGroupUri = await this.itemQuerySvc.getTdbGroupUri(
      outputFile
    );

    const mergeImgs: MergeImg[] = [];

    for (const img of inputImgs) {
      const imgId = img.key;
      const chs = mergeChannels[imgId];
      const imgTdbGroupUri = await this.itemQuerySvc.getTdbGroupUriById({
        itemId: imgId,
      });

      if (mergeChannels) {
        mergeImgs.push({
          id: imgTdbGroupUri,
          channels: chs,
        });
      }
    }

    const remoteReq: RemoteTaskMergeChannelsReq = {
      task_type: "merge_channels",
      task_args: {
        req: {
          output_img_id: outputImgTdbGroupUri,
          merge_imgs: mergeImgs,
        },
      },
    };

    const remoteTaskId = await this.startRemoteTask({
      task_id: taskId,
      remote_task_req: remoteReq,
    });

    const extra: MergeChannelsExtra = {
      outputImgId: outputFile.id,
    };

    await this.bindRemoteTaskAndUpdateStatus({
      taskId: taskId,
      remoteTaskId: remoteTaskId,
      extra: extra,
    });
  }

  async afterMergeChannels({ task }: { task: Task }) {
    const extra = task.extra as MergeChannelsExtra;
    const outputImgId = extra.outputImgId;
    await this.itemSvc.updateItemSize({ itemId: outputImgId });
  }

  async afterRegister({ task }: { task: Task }) {
    const extra = task.extra as RegisterExtra;
    const outputImgId = extra.outputImgId;
    await this.itemSvc.updateItemSize({ itemId: outputImgId });
  }

  async afterIngest({ task }: { task: Task }) {
    const extra = task.extra as IngestExtra;
    const decodedItemId = extra.decodedItemId;
    await this.itemSvc.updateItemSize({ itemId: decodedItemId });
  }

  async afterSaveAs({ task }: { task: Task }) {
    const { outputItemIds, outputsMoveInfo } = task.extra as SaveAsExtra;

    if (outputsMoveInfo) {
      await this.diskSvc.moveToOssKey({
        srcOssKey: outputsMoveInfo.srcKey,
        dstOssKey: outputsMoveInfo.dstKey,
        forbidOverwrite: false,
      });
    }

    for (const outputItemId of outputItemIds) {
      await this.itemSvc.updateItemSize({ itemId: outputItemId });
    }
  }

  async add_task_and_run(
    operator: Operator,
    {
      projectId,
      taskReq,
      snapShot,
    }: {
      projectId: string;
      taskReq: TaskReq;
      snapShot?: Blob;
    }
  ) {
    const uniqueTaskId = nanoid(25);

    if (snapShot) {
      await this.saveSnapShot({ projectId, taskId: uniqueTaskId, snapShot });
    }

    const task = await addTask(this.prisma, {
      projectId: projectId,
      taskReq: taskReq,
      operator: operator,
      taskId: uniqueTaskId,
    });

    await this.runTask({ task, operator });
    return task;
  }

  async rerunTaskById(operator: Operator, { taskId }: { taskId: string }) {
    const task = await this.prisma.task.findFirst({
      where: {
        id: taskId,
      },
    });

    if (task) {
      await this.runTask({ task, operator });
    }
  }

  async runTask({ task, operator }: { task: Task; operator: Operator }) {
    const taskId = task.id;
    try {
      await resetTaskStateToStart(this.prisma, {
        task_id: task.id,
        operator,
      });

      switch (task.task_type as TaskType) {
        case TaskType.ingest:
          await this.runIngest({ task, operator });
          break;
        case TaskType.save_as:
          await this.runSaveAs({ task, operator });
          break;
        case TaskType.rescale_intensity:
          await this.runRescaleIntensity({ task, operator });
          break;
        case TaskType.clip_intensity:
          await this.runClipIntensity({ task, operator });
          break;
        case TaskType.bit_depth_convert:
          await this.runBitDepthConvert({ task, operator });
          break;
        case TaskType.flip:
          await this.runFlip({ task, operator });
          break;
        case TaskType.register:
          await this.runRegister({ task, operator });
          break;
        case TaskType.merge_channels:
          await this.runMergeChannels({ task, operator });
          break;
        default:
          throw new DomainError("task_type_not_supported", {
            task_type: task.task_type,
          });
      }
    } catch (error) {
      let errorMsg = "系统异常";
      console.error("run task failed: ", taskId, error);

      if (error instanceof DomainError) {
        errorMsg = error.toString();
      }

      try {
        await recordTaskError(this.prisma, {
          task_id: taskId,
          errorMsg,
        });
      } catch (error) {
        console.error("record task error failed in run_task.", taskId, error);
      }
    }
  }

  async startRemoteTask({
    task_id,
    remote_task_req,
  }: {
    task_id: string;
    remote_task_req: RemoteTaskReq;
  }) {
    try {
      const remoteTaskId = await this.remoteTaskApi
        .url(`/start-task/${task_id}`)
        .post(remote_task_req)
        .json<string>();

      return remoteTaskId;
    } catch (err) {
      console.error(`failed to call start-ray-task`, {
        task_id,
        remote_task_req,
        err,
      });

      throw new DomainError("start_task_api_failed", { cause: String(err) });
    }
  }

  async processAfterTaskDone({ taskId }: { taskId: string }) {
    const task = await this.prisma.task.findFirst({
      where: {
        id: taskId,
      },
    });
    if (!task) {
      return;
    }

    const taskType = task.task_type;
    try {
      switch (taskType as TaskType) {
        case TaskType.ingest:
          await this.afterIngest({ task });
          break;
        case TaskType.save_as:
        case TaskType.rescale_intensity:
        case TaskType.clip_intensity:
        case TaskType.bit_depth_convert:
        case TaskType.flip:
          await this.afterSaveAs({ task });
          break;
        case TaskType.register:
          await this.afterRegister({ task });
          break;
        case TaskType.merge_channels:
          await this.afterMergeChannels({ task });
          break;
        default:
          // todo: raise error to restate?
          throw new Error(
            `task_type ${taskType} is not supported for processing after task done for ${taskId}`
          );
      }
    } catch (error) {
      console.error("processAfterTaskDone failed: ", error, task);
      // todo: raise error to restate?
    }
  }

  async bindRemoteTaskAndUpdateStatus({
    taskId,
    remoteTaskId,
    extra,
  }: {
    taskId: string;
    remoteTaskId: string;
    extra?: TaskExtra;
  }) {
    await bindRemoteTask(this.prisma, {
      task_id: taskId,
      remote_task_id: remoteTaskId,
      extra: extra,
    });

    await this.rsClient.taskSvc.sendClient.updateRemoteTaskStatus({
      taskId,
      remoteTaskId,
    });
  }

  async updateRemoteTaskStatus({
    taskId,
    remoteTaskId,
  }: {
    taskId: string;
    remoteTaskId: string;
  }): Promise<{ keepPolling: boolean }> {
    const task = await getTaskByRemoteTaskId(this.prisma, {
      remoteTaskId,
      taskId,
    });

    if (!task) {
      return { keepPolling: false };
    }

    const timeoutSecondForLostState = 30;
    const taskDurationInSecond = dayjs().diff(task.start_at, "second");

    const remoteTaskState = await this.getRemoteTaskState({ remoteTaskId });

    if (remoteTaskState == null) {
      if (taskDurationInSecond > timeoutSecondForLostState) {
        await recordTaskError(this.prisma, {
          task_id: taskId,
          errorMsg: "计算服务重启，请重试任务",
        });

        return { keepPolling: false };
      }

      return { keepPolling: true };
    }

    const remoteTaskStatus = remoteTaskState.state;

    if (remoteTaskStatus == TaskRemoteInnerState.FAILED) {
      const errorMsg = remoteTaskState.error_message;
      await recordTaskError(this.prisma, {
        task_id: taskId,
        errorMsg: errorMsg,
        remote_task_status: remoteTaskStatus,
      });

      return { keepPolling: false };
    } else if (remoteTaskStatus == TaskRemoteInnerState.FINISHED) {
      await this.rsClient.taskSvc.sendClient.processAfterTaskDone({ taskId });
      await updateTaskRemoteStatus(this.prisma, {
        taskId,
        remoteTaskStatus,
      });

      return { keepPolling: false };
    } else {
      await updateTaskRemoteStatus(this.prisma, {
        taskId,
        remoteTaskStatus,
      });

      return { keepPolling: true };
    }
  }

  async getRemoteTaskState({ remoteTaskId }: { remoteTaskId: string }) {
    const state = await this.remoteTaskApi
      .url(`/task-state/${remoteTaskId}`)
      .get()
      .json<{
        state: TaskRemoteInnerState;
        creation_time_ms?: number;
        start_time_ms?: number;
        end_time_ms?: number;
        error_message?: string;
      } | null>();
    return state;
  }

  async saveSnapShot({
    projectId,
    taskId,
    snapShot,
  }: {
    projectId: string;
    taskId: string;
    snapShot: Blob;
  }) {
    const snapshotOssKey = OSSKey.taskSnapshot({
      projectId,
      taskId,
    });
    await this.s3Client.putObject(
      snapshotOssKey,
      snapShot.stream(),
      snapShot.type
    );
  }

  async _runSaveAsTask<T extends SaveAsTaskReq["taskArgs"]>(
    task: Task,
    operator: Operator,
    enhanceRemoteReq?: (remoteReq: RemoteTaskSaveAsReq, taskArgs: T) => void
  ) {
    const taskId = task.id;
    const projectId = task.project_id;
    const taskArgs = task.task_args as T;

    const { remoteReq, extra } = await this._prepareBaseSaveAs({
      projectId,
      taskId,
      taskArgs,
      operator,
    });

    enhanceRemoteReq?.(remoteReq, taskArgs);

    const remoteTaskId = await this.startRemoteTask({
      task_id: taskId,
      remote_task_req: remoteReq,
    });

    await this.bindRemoteTaskAndUpdateStatus({
      taskId: taskId,
      remoteTaskId: remoteTaskId,
      extra,
    });
  }

  async _prepareBaseSaveAs({
    taskId,
    taskArgs,
    operator,
    projectId,
  }: {
    taskId: string;
    projectId: string;
    taskArgs: SaveAsTaskReq["taskArgs"];
    operator: Operator;
  }) {
    const {
      itemId,
      setting: { saveAsType, selectedChannels: channels, region, splitChannel },
      output: { folder_name, tgt_folder_id },
    } = taskArgs;

    await this.itemSvc.clearTaskItems(operator, { taskId });

    const sourceItem = await getItem(this.prisma, {
      itemId,
      typeFlag: ItemTypeFlag.File,
    });

    const extra: SaveAsExtra = {
      outputItemIds: [],
    };

    const src_img_id = await this.itemQuerySvc.getTdbGroupUri(sourceItem);

    const remoteReq: RemoteTaskSaveAsReq = {
      task_type: "save_as",
      task_args: {
        req: {
          src_img_id: src_img_id,
          tgt_type: saveAsType,
          channel_outputs: [],
          xy_region: region,
        },
      },
    };

    const sorted_channels = channels.sort((a, b) => a.id - b.id);

    if (saveAsType === "irs") {
      let output_folder_iid_path = sourceItem.piid_path;
      if (tgt_folder_id) {
        const output_folder = await getItem(this.prisma, {
          itemId: tgt_folder_id,
          typeFlag: ItemTypeFlag.Folder,
        });
        output_folder_iid_path = get_item_iid_path(output_folder);
      }

      const store_folder = await createItem(this.prisma, {
        piid_path: output_folder_iid_path,
        typeFlag: ItemTypeFlag.Folder,
        name: folder_name,
        taskId,
        operator,
      });

      await genChannelOutputs(async (item_name) => {
        const output_item = await createItem(this.prisma, {
          piid_path: get_item_iid_path(store_folder),
          typeFlag: ItemTypeFlag.File,
          operator,
          name: item_name,
          taskId,
        });
        extra.outputItemIds.push(output_item.id);
        const tdb_group_uri = await this.itemQuerySvc.getTdbGroupUri(
          output_item
        );
        return tdb_group_uri;
      });
    } else {
      await genChannelOutputs((item_name) => {
        const sub_path = OSSKey.underTaskTmpOutputs({ taskId }, item_name);
        return sub_path;
      });

      const outputsSrcFolderKey = OSSKey.underTaskTmpOutputs({ taskId }, "/");
      const mvDstFolderKey = OSSKey.underProjectDisk(
        { projectId },
        tgt_folder_id ?? "/"
      );

      extra.outputsMoveInfo = {
        srcKey: outputsSrcFolderKey,
        dstKey: mvDstFolderKey,
      };
    }

    return { remoteReq, extra };

    async function genChannelOutputs(
      genSubPath: (item_name: string) => string | Promise<string>
    ) {
      if (splitChannel) {
        for (const ch of sorted_channels) {
          const item_name = replaceSuffix(
            `${ch.id}-${ch.name}-${folder_name}`,
            saveAsType
          );
          const sub_path = await genSubPath(item_name);
          remoteReq.task_args.req.channel_outputs.push({
            sub_path,
            channel_filters: [{ idx: ch.id }],
          });
        }
      } else {
        const item_name = replaceSuffix(folder_name, saveAsType);
        const sub_path = await genSubPath(item_name);
        remoteReq.task_args.req.channel_outputs.push({
          sub_path,
          channel_filters: sorted_channels.map((ch) => ({
            idx: ch.id,
          })),
        });
      }
    }
  }
}

function replaceSuffix(name: string, suffix: string) {
  const orgSuffix = path.extname(name);
  const baseName = path.basename(name, orgSuffix);
  const newName = `${baseName}.${suffix}`;
  return newName;
}

type RemoteTaskReq =
  | RemoteTaskIngestReq
  | RemoteTaskSaveAsReq
  | RemoteTaskRegisterReq
  | RemoteTaskMergeChannelsReq;

type RemoteTaskIngestReq = {
  task_type: "ingest";
  task_args: {
    source_path: string;
    output_path: string;
  };
};

type ChannelFilter = {
  idx: number;
  rescale_range?: [number, number];
  clip_range?: [number, number];
};

type ChannelOutput = {
  sub_path: string;
  channel_filters: ChannelFilter[];
};

type XYRegion = {
  x: number;
  y: number;
  width: number;
  height: number;
};

type RemoteTaskSaveAsReq = {
  task_type: "save_as";
  task_args: {
    req: {
      src_img_id: string;
      tgt_type: string;
      channel_outputs: ChannelOutput[];
      xy_region?: XYRegion;
      tgt_bit_depth?: string;
      flip_axes?: { dims: "X" | "Y" | "XY" };
    };
  };
};

type MergeImg = { id: string; channels: number[] };

type ModalityImg = { id: string; channel_id: number; level?: number };

type RemoteTaskRegisterReq = {
  task_type: "register_and_merge";
  task_args: {
    req: {
      output_folder_path: string;
      output_img_id: string;
      merge_imgs: MergeImg[];
      fixed_img: ModalityImg;
      moving_imgs: ModalityImg[];
      reg_params: Record<string, unknown>;
      register_method: "elastix" | "sift" | "orb";
    };
  };
};

type RemoteTaskMergeChannelsReq = {
  task_type: "merge_channels";
  task_args: {
    req: {
      output_img_id: string;
      merge_imgs: MergeImg[];
    };
  };
};

if (import.meta.vitest) {
  const { it, expect, describe } = import.meta.vitest;

  describe("replaceSuffix", () => {
    it("should replace suffix", () => {
      expect(replaceSuffix("test.test.txt", "jpg")).toBe("test.test.jpg");
      expect(replaceSuffix("test", "jpg")).toBe("test.jpg");
      expect(replaceSuffix("test.txt", "jpg")).toBe("test.jpg");
    });

    it("should return object name", () => {
      expect(replaceSuffix("/folder/test", "jpg")).toBe("test.jpg");
    });
  });
}
