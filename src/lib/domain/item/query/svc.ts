import {
  getItem,
  getItemsByPiidPath,
  getItemsByPrjId,
  get_iid_path_by_id,
} from "@/lib/domain/item/dbt";
import { ItemTypeFlag } from "@/lib/domain/item/svc";
import { S3Client } from "@/lib/infra/s3/client";
import { Item, type PrismaClient } from "@prisma/client";
import path from "path";

export class ItemQuerySvc {
  constructor(
    private readonly prisma: PrismaClient,
    private readonly s3Client: S3Client
  ) {}

  async getTdbGroupUriById({ itemId }: { itemId: string }) {
    const item = await getItem(this.prisma, {
      itemId,
      typeFlag: ItemTypeFlag.File,
    });
    const bucket = this.s3Client.bucket;
    const tdbGroupUri = getTdbGroupUri(bucket, item);
    return tdbGroupUri;
  }

  async getTdbGroupUri(item: { piid_path: string; id: string }) {
    const bucket = this.s3Client.bucket;
    const tdbGroupUri = getTdbGroupUri(bucket, item);
    return tdbGroupUri;
  }

  async subItems({
    prjId,
    pid,
    itemTypeFlag,
  }: {
    prjId: string;
    pid?: string;
    itemTypeFlag?: ItemTypeFlag;
  }) {
    const piid_path = await get_iid_path_by_id(this.prisma, {
      itemId: pid,
      prjId,
    });

    const items = await getItemsByPiidPath(this.prisma, {
      piid_path,
      typeFlag: itemTypeFlag,
    });

    return items;
  }

  async items({
    prjId,
    length,
    sortBy = "created_at",
    order = "desc",
  }: {
    prjId: string;
    sortBy?: "created_at" | "updated_at";
    order?: "desc" | "asc";
    length?: number;
  }) {
    const items = await getItemsByPrjId(this.prisma, {
      prjId,
      orderBy: { [sortBy]: order },
      length,
      typeFlag: ItemTypeFlag.File,
    });
    return items;
  }

  async itemsHierarchy({
    prjId,
    typeFlag,
  }: {
    prjId: string;
    typeFlag?: ItemTypeFlag;
  }) {
    const items = await getItemsByPrjId(this.prisma, {
      prjId,
      orderBy: { created_at: "desc" },
      typeFlag,
    });

    const result = attachPid(items);
    return result;
  }
}

function getTdbGroupUri(
  bucket: string,
  { piid_path, id }: { piid_path: string; id: string }
) {
  const ossKey = getOssKey({ piid_path, id });
  const ossKeyWithBkt = path.join(bucket, ossKey);
  return `s3://${ossKeyWithBkt}`;
}

function attachPid(items: Item[]) {
  const iidToIdMap: Record<number, string> = {};
  items.forEach((item) => {
    iidToIdMap[item.iid] = item.id;
  });

  const result = items.map((item) => {
    let pid;
    const piids = item.piid_path.split("/").filter(Boolean);

    if (piids.length > 1) {
      const piid = piids[piids.length - 1];
      pid = iidToIdMap[Number(piid)];
    }

    return {
      ...item,
      pid,
    };
  });
  return result;
}

export function getOssKey({
  piid_path,
  id,
}: {
  piid_path: string;
  id: string;
}) {
  const projectId = piid_path.split("/").filter(Boolean)[0];
  const ossKey = path.join("project/", projectId, "/item/", id, "/");
  return ossKey;
}

if (import.meta.vitest) {
  const { test, expect, vi, describe } = import.meta.vitest;
  const { mock } = await import("vitest-mock-extended");

  const getSUT = async () => {
    vi.resetModules();
    const { ItemQuerySvc } = await import("./svc");
    const s3Client = mock<S3Client>({ bucket: "test-bkt" });
    const prisma = mock<PrismaClient>();
    const svc = new ItemQuerySvc(prisma, s3Client);
    return svc;
  };

  describe("get tdb uri", async () => {
    test("get tdb uri by id", async () => {
      vi.doMock("@/lib/domain/item/dbt", () => ({
        getItem: vi.fn().mockResolvedValueOnce({
          piid_path: "/proj-id-xxx/1/2/4",
          id: "item-id-3x",
        }),
      }));

      const svc = await getSUT();
      const tdbUrl = await svc.getTdbGroupUriById({ itemId: "item-id-3x" });
      expect(tdbUrl).toBe("s3://test-bkt/project/proj-id-xxx/item/item-id-3x/");
    });

    test("get tdb uri by item", async () => {
      vi.doMock("@/lib/domain/item/dbt", () => ({
        getItem: vi.fn().mockResolvedValueOnce({
          piid_path: "/proj-id-xxx/1/2/3",
          id: "item-id-4x",
        }),
      }));

      const svc = await getSUT();
      const tdbUrl = await svc.getTdbGroupUri({
        id: "item-id-4x",
        piid_path: "/proj-id-xxx/1/2/3",
      });
      expect(tdbUrl).toBe("s3://test-bkt/project/proj-id-xxx/item/item-id-4x/");
    });

    test("get oss key", async () => {
      const tdbUrl = getOssKey({
        piid_path: "/proj-id-xxx/1/2/3",
        id: "item-id-5x",
      });

      expect(tdbUrl).toBe("project/proj-id-xxx/item/item-id-5x/");
    });
  });

  describe("attachPid", () => {
    const items = [
      {
        iid: 30,
        id: "uuid_1",
        piid_path: "/prjId/22/26/",
        name: "1444",
      },

      {
        iid: 26,
        id: "uuid_2",
        piid_path: "/prjId/22/",
        name: "144",
      },
      {
        iid: 22,
        id: "uuid_3",
        piid_path: "/prjId/",
        name: "14",
      },
      {
        iid: 21,
        id: "uuid_4",
        piid_path: "/prjId/",
        name: "13",
      },
    ];

    test("should attach pid correctly", () => {
      const result = attachPid(items as Item[]);

      expect(result).toEqual([
        {
          iid: 30,
          id: "uuid_1",
          piid_path: "/prjId/22/26/",
          name: "1444",
          pid: "uuid_2",
        },
        {
          iid: 26,
          id: "uuid_2",
          piid_path: "/prjId/22/",
          name: "144",
          pid: "uuid_3",
        },
        {
          iid: 22,
          id: "uuid_3",
          piid_path: "/prjId/",
          name: "14",
          pid: undefined,
        },
        {
          iid: 21,
          id: "uuid_4",
          piid_path: "/prjId/",
          name: "13",
          pid: undefined,
        },
      ]);
    });
  });
}
