import { DomainError } from "@/lib/domain/common/error";
import {
  ItemFileNameReg,
  ItemFolderNameReg,
} from "@/lib/domain/item/constants";
import { getItem, get_iid_path_by_id } from "@/lib/domain/item/dbt";
import { getOssKey } from "@/lib/domain/item/query/svc";
import { Operator } from "@/lib/domain/user/m";
import { RestateClient } from "@/lib/infra/restate/client";
import { S3Client } from "@/lib/infra/s3/client";
import {
  isDbUniqueConstraintError,
  makeNameWithDate,
  unixtime_now_in_ms,
} from "@/lib/utils";
import { type Item, Prisma, type PrismaClient } from "@prisma/client";
import pRetry from "p-retry";
import path from "path";
import { z } from "zod";

export enum ItemTypeFlag {
  Folder = 0,
  File = 1,
}

const PIID_PATH_SEPARATOR = "/";

export const CreateFolderInputSchema = z.object({
  projectId: z.string(),
  pid: z.string().optional(),
  name: z.string().regex(ItemFolderNameReg),
});

export const MoveItemInputSchema = z.object({
  projectId: z.string(),
  sourceId: z.string(),
  targetId: z.string().optional(),
});

export const DeleteItemInputSchema = z.object({
  itemId: z.string(),
});

export const UpdateItemNameInputSchema = z
  .object({
    itemId: z.string(),
    name: z.string(),
    isFile: z.boolean().optional(),
  })
  .superRefine(({ isFile, name }, ctx) => {
    const reg = isFile ? ItemFileNameReg : ItemFolderNameReg;
    if (!reg.test(name)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
      });
    }
  });

export const CopyItemInputSchema = z.object({
  projectId: z.string(),
  sourceId: z.string(),
  targetId: z.string().optional(),
});

export type CreateFolderInput = z.infer<typeof CreateFolderInputSchema>;

export type MoveItemInput = z.infer<typeof MoveItemInputSchema>;
export type DeleteItemInput = z.infer<typeof DeleteItemInputSchema>;
export type UpdateItemNameInput = z.infer<typeof UpdateItemNameInputSchema>;

export type CopyItemInput = z.infer<typeof CopyItemInputSchema>;

export function get_item_iid_path({
  piid_path,
  iid,
}: {
  piid_path: string;
  iid: number;
}) {
  return path.join(piid_path, iid.toString(), PIID_PATH_SEPARATOR);
}

export function get_root_iid_path(projectId: string) {
  return path.join(PIID_PATH_SEPARATOR, projectId, PIID_PATH_SEPARATOR);
}

export function get_prjId_by_piid_path(piid_path: string) {
  return piid_path.split(PIID_PATH_SEPARATOR).filter(Boolean)[0];
}

export class ItemSvc {
  constructor(
    private readonly prisma: PrismaClient,
    private readonly s3Client: S3Client,
    private readonly rsClient: RestateClient
  ) {}

  async updateItemSize({ itemId }: { itemId: string }) {
    const item = await getItem(this.prisma, { itemId });
    const ossKey = getOssKey(item);
    const size = await this.s3Client.totalSizeInBytes(ossKey);
    await updateItemSize(this.prisma, { itemId, size });
  }

  async clearTaskItems(operator: Operator, { taskId }: { taskId: string }) {
    const deleted_at = unixtime_now_in_ms();
    const items = await this.prisma.item.updateManyAndReturn({
      where: {
        task_id: taskId,
        deleted_at: 0,
      },
      data: {
        deleted_at: deleted_at,
        updated_by_id: operator.id,
      },
      select: {
        id: true,
      },
    });
    const itemIds = items.map((x) => x.id);

    if (itemIds.length === 0) {
      return;
    }

    await this.rsClient.itemSvc.sendClient.deleteItems({ itemIds });
  }

  async deleteItems({ itemIds }: { itemIds: string[] }) {
    const items = await this.prisma.item.findMany({
      where: {
        id: { in: itemIds },
        deleted_at: { not: 0 },
      },
      select: {
        id: true,
        piid_path: true,
      },
    });
    const objKeys = items.map((item) => getOssKey(item));

    await this.s3Client.deleteObjects(objKeys);

    await this.prisma.item.deleteMany({
      where: {
        id: { in: itemIds },
      },
    });
  }
  async deleteItem(operator: Operator, { itemId }: DeleteItemInput) {
    const deleted_at = unixtime_now_in_ms();
    const item = await getItem(this.prisma, { itemId });
    const iidPath = get_item_iid_path({
      piid_path: item.piid_path,
      iid: item.iid,
    });
    const updatedItems = await this.prisma.item.updateManyAndReturn({
      where: {
        OR: [{ piid_path: { startsWith: iidPath } }, { iid: item.iid }],
        deleted_at: 0,
      },
      data: {
        deleted_at: deleted_at,
        updated_by_id: operator.id,
      },
    });
    const itemIds = updatedItems.map((x) => x.id);

    if (itemIds.length === 0) {
      return;
    }
    await this.rsClient.itemSvc.sendClient.deleteItems({ itemIds });
  }

  async moveItem(
    operator: Operator,
    { projectId, sourceId, targetId }: MoveItemInput
  ) {
    const sourceItem = await getItem(this.prisma, { itemId: sourceId });
    const sourcePiidPath = get_item_iid_path({
      piid_path: sourceItem.piid_path,
      iid: sourceItem.iid,
    });
    const targetPiidPath = await get_iid_path_by_id(this.prisma, {
      itemId: targetId,
      prjId: projectId,
    });

    if (targetPiidPath === sourceItem.piid_path) {
      // same level
      return;
    }

    if (targetPiidPath.startsWith(sourcePiidPath)) {
      throw new DomainError("forbid_to_move_to_sub_dir");
    }

    const tryMoveItem = async (attemptCount: number) => {
      let uniqueName = sourceItem.name;
      if (attemptCount > 1) {
        uniqueName = makeNameWithDate(
          sourceItem.name,
          sourceItem.type_flag === ItemTypeFlag.Folder
        );
      }
      await moveItemInTx(this.prisma, operator, {
        sourceItem,
        targetPiidPath,
        uniqueName,
      });
    };

    await pRetry(tryMoveItem, {
      retries: 1,
      shouldRetry: ({ error }) => isDbUniqueConstraintError(error),
    });
  }

  async createFolder(
    operator: Operator,
    { projectId, pid, name }: CreateFolderInput
  ) {
    const piid_path = await get_iid_path_by_id(this.prisma, {
      itemId: pid,
      prjId: projectId,
    });
    await createItem(this.prisma, {
      piid_path,
      typeFlag: ItemTypeFlag.Folder,
      operator,
      name,
    });
  }

  async updateItemName(
    operator: Operator,
    { itemId, name }: UpdateItemNameInput
  ) {
    try {
      await this.prisma.item.update({
        where: {
          id: itemId,
        },
        data: {
          name,
          updated_by_id: operator.id,
        },
      });
    } catch (error) {
      if (isDbUniqueConstraintError(error)) {
        throw new DomainError("item_name_conflict");
      } else {
        throw error;
      }
    }
  }

  async copyItem() {}
}

export async function updateItemSize(
  prisma: PrismaClient,
  { itemId, size }: { itemId: string; size?: number }
) {
  await prisma.item.update({
    where: {
      id: itemId,
    },
    data: {
      size: size,
    },
  });
}

export async function createItem(
  prisma: PrismaClient,
  {
    piid_path,
    typeFlag,
    operator,
    name,
    taskId,
    size,
  }: {
    piid_path: string;
    operator: Operator;
    typeFlag: ItemTypeFlag;
    name: string;
    taskId?: string;
    size?: number;
  }
) {
  async function createWithRetry(attemptCount: number) {
    let resolvedName = name;
    if (attemptCount > 1 && taskId) {
      resolvedName = `${taskId}-${name}`;
    }

    const item = await prisma.item.create({
      data: {
        piid_path: piid_path,
        name: resolvedName,
        type_flag: typeFlag,
        created_by_id: operator.id,
        updated_by_id: operator.id,
        task_id: taskId,
        size: size,
      },
    });
    return item;
  }

  const item = await pRetry(createWithRetry, {
    retries: 2,
    shouldRetry: ({ error }) => isDbUniqueConstraintError(error),
  });

  return item;
}

export async function moveItemInTx(
  prisma: PrismaClient,
  operator: Operator,
  {
    sourceItem,
    targetPiidPath,
    uniqueName,
  }: {
    sourceItem: Item;
    targetPiidPath: string;
    uniqueName: string;
  }
) {
  await prisma.$transaction(async (tx) => {
    const updatedItem = await tx.item.update({
      where: {
        id: sourceItem.id,
      },
      data: {
        piid_path: targetPiidPath,
        updated_by_id: operator.id,
        name: uniqueName,
      },
    });

    const originalPiidPath = get_item_iid_path({
      piid_path: sourceItem.piid_path,
      iid: sourceItem.iid,
    });

    const movedPiidPath = get_item_iid_path({
      piid_path: updatedItem.piid_path,
      iid: updatedItem.iid,
    });

    const sql = Prisma.sql`
      update item
      set piid_path = ${movedPiidPath} || substr(piid_path, ${
      originalPiidPath.length + 1
    }::int)
      where piid_path like ${originalPiidPath + "%"}`;
    await tx.$executeRaw(sql);
  });
}

if (import.meta.vitest) {
  const { it, expect, vi, describe } = import.meta.vitest;
  const { mock } = await import("vitest-mock-extended");

  const s3Client = mock<S3Client>({ bucket: "test-bkt" });
  const rsClient = mock<RestateClient>();

  describe("move item", () => {
    const operator = { id: "user-id-xxx", isTestAccount: false };
    const mockItems = {
      A: {
        iid: 1,
        piid_path: "/prjId/",
        name: "A",
        id: "idA",
      },
      B: {
        iid: 2,
        piid_path: "/prjId/1/",
        name: "B",
        id: "idB",
      },
      C: {
        iid: 3,
        piid_path: "/prjId/1/2/",
        name: "C",
        id: "idC",
      },
      X: {
        iid: 4,
        piid_path: "/prjId/",
        name: "X",
        id: "idX",
      },
      Y: {
        iid: 5,
        piid_path: "/prjId/4/",
        name: "Y",
        id: "idY",
      },
      Z: {
        iid: 6,
        piid_path: "/prjId/4/",
        name: "Z",
        id: "idZ",
      },
    };
    /**
     * Relation:
     * prj:
     *    |- A
     *      |- B
     *        |- C
     *    |- X
     *      |- Y
     *      |- A
     */

    it("move item to target folder: A => Y", async () => {
      const tx = {
        item: {
          update: vi.fn().mockResolvedValueOnce({
            ...mockItems.A,
            piid_path: "/prjId/4/5/",
          }),
        },
        $executeRaw: vi.fn(),
      };
      const mockPrisma = mock<PrismaClient>({
        item: {
          findUnique: vi
            .fn()
            .mockResolvedValueOnce(mockItems.A)
            .mockResolvedValueOnce(mockItems.Y),
        },
        $transaction: vi.fn().mockImplementation((fn) => fn(tx)),
      });
      const svc = new ItemSvc(mockPrisma, s3Client, rsClient);
      const input = {
        projectId: "prjId",
        sourceId: mockItems.A.id,
        targetId: mockItems.Y.id,
      };

      await svc.moveItem(operator, input);

      expect(tx.item.update).toHaveBeenCalledWith({
        where: {
          id: mockItems.A.id,
        },
        data: {
          piid_path: "/prjId/4/5/",
          updated_by_id: operator.id,
          name: mockItems.A.name,
        },
      });

      expect(tx.$executeRaw).toHaveBeenCalledWith({
        values: ["/prjId/4/5/1/", 10, "/prjId/1/%"],
        strings: [
          "\n      update item\n      set piid_path = ",
          " || substr(piid_path, ",
          "::int)\n      where piid_path like ",
          "",
        ],
      });
    });

    it("move item to target and name conflict: A => X", async () => {
      const tx = {
        item: {
          update: vi
            .fn()
            .mockRejectedValueOnce(
              new Prisma.PrismaClientKnownRequestError("xx", {
                code: "P2002",
                clientVersion: "xx",
              })
            )
            .mockResolvedValueOnce({
              ...mockItems.A,
              piid_path: "/prjId/4/5/",
              name: "A_xxxxxx",
            }),
        },
        $executeRaw: vi.fn(),
      };
      const mockPrisma = mock<PrismaClient>({
        item: {
          findUnique: vi
            .fn()
            .mockResolvedValueOnce(mockItems.A)
            .mockResolvedValueOnce(mockItems.Y),
        },
        $transaction: vi.fn().mockImplementation((fn) => fn(tx)),
      });
      const svc = new ItemSvc(mockPrisma, s3Client, rsClient);
      const input = {
        projectId: "prjId",
        sourceId: mockItems.A.id,
        targetId: mockItems.X.id,
      };

      vi.mock(import("@/lib/utils"), async (importOriginal) => {
        const mod = await importOriginal(); // type is inferred
        return {
          ...mod,
          makeNameWithDate: vi.fn().mockReturnValue("A_xxxxxx"),
        };
      });

      await svc.moveItem(operator, input);
      expect(tx.item.update).toHaveBeenCalledWith({
        where: {
          id: mockItems.A.id,
        },
        data: {
          piid_path: "/prjId/4/5/",
          updated_by_id: operator.id,
          name: "A_xxxxxx",
        },
      });
    });

    it("move item to root: C => root", async () => {
      const tx = {
        item: {
          update: vi.fn().mockResolvedValueOnce({
            ...mockItems.A,
            piid_path: "/prjId/4/5/",
          }),
        },
        $executeRaw: vi.fn(),
      };
      const mockPrisma = mock<PrismaClient>({
        item: {
          findUnique: vi.fn().mockResolvedValueOnce(mockItems.C),
        },
        $transaction: vi.fn().mockImplementation((fn) => fn(tx)),
      });
      const svc = new ItemSvc(mockPrisma, s3Client, rsClient);

      const input = {
        projectId: "prjId",
        sourceId: mockItems.C.id,
      };
      await svc.moveItem(operator, input);

      expect(tx.item.update).toHaveBeenCalledWith({
        where: {
          id: mockItems.C.id,
        },
        data: {
          piid_path: "/prjId/",
          updated_by_id: operator.id,
          name: mockItems.C.name,
        },
      });
    });

    it("move item to sub dir: A => B", async () => {
      const mockPrisma = mock<PrismaClient>({
        item: {
          findUnique: vi
            .fn()
            .mockResolvedValueOnce(mockItems.A)
            .mockResolvedValueOnce(mockItems.B),
          update: vi.fn(),
        },
      });
      const svc = new ItemSvc(mockPrisma, s3Client, rsClient);
      const input = {
        projectId: "prjId",
        sourceId: mockItems.A.id,
        targetId: mockItems.B.id,
      };

      await expect(svc.moveItem(operator, input)).rejects.toThrow(
        "forbid_to_move_to_sub_dir"
      );
    });

    it("move item to parent folder: B => A", async () => {
      const tx = {
        item: {
          update: vi.fn(),
        },
      };
      const mockPrisma = mock<PrismaClient>({
        item: {
          findUnique: vi
            .fn()
            .mockResolvedValueOnce(mockItems.B)
            .mockResolvedValueOnce(mockItems.A),
        },
        $transaction: vi.fn().mockImplementation((fn) => fn(tx)),
      });
      const svc = new ItemSvc(mockPrisma, s3Client, rsClient);
      const input = {
        projectId: "prjId",
        sourceId: mockItems.B.id,
        targetId: mockItems.A.id,
      };

      await svc.moveItem(operator, input);

      expect(tx.item.update).toHaveBeenCalledTimes(0);
    });
  });

  describe("get prjId by piid path", () => {
    it("get prjId by piid path", async () => {
      expect(get_prjId_by_piid_path("/prjId/1/2/")).toBe("prjId");
    });
  });
}
