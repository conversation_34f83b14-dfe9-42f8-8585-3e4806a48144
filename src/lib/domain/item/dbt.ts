import { DomainError } from "@/lib/domain/common/error";
import {
  get_item_iid_path,
  get_root_iid_path,
  ItemTypeFlag,
} from "@/lib/domain/item/svc";
import { Prisma, PrismaClient } from "@prisma/client";

export async function getItem(
  prisma: PrismaClient,
  { itemId, typeFlag }: { itemId: string; typeFlag?: ItemTypeFlag }
) {
  const item = await prisma.item.findUnique({
    where: {
      id: itemId,
      type_flag: typeFlag,
      deleted_at: 0,
    },
  });

  if (!item) {
    throw new DomainError("item_not_found", { itemId });
  }
  return item;
}

export async function getItemsByPiidPath(
  prisma: PrismaClient,
  { piid_path, typeFlag }: { piid_path: string; typeFlag?: ItemTypeFlag }
) {
  const items = await prisma.item.findMany({
    where: {
      piid_path,
      deleted_at: 0,
      type_flag: typeFlag,
      OR: [
        { type_flag: ItemTypeFlag.Folder },
        { type_flag: ItemTypeFlag.File, size: { gt: 0 } },
      ],
    },
    orderBy: {
      created_at: "desc",
    },
  });

  return items;
}

export async function get_iid_path_by_id(
  prisma: PrismaClient,
  {
    itemId,
    prjId,
  }: {
    itemId?: string;
    prjId: string;
  }
) {
  let iid_path = get_root_iid_path(prjId);

  if (itemId) {
    const item = await getItem(prisma, {
      itemId,
      typeFlag: ItemTypeFlag.Folder,
    });
    iid_path = get_item_iid_path({
      piid_path: item.piid_path,
      iid: item.iid,
    });
  }

  return iid_path;
}

export async function getItemsByPrjId(
  prisma: PrismaClient,
  {
    prjId,
    orderBy,
    length,
    typeFlag,
  }: {
    prjId: string;
    length?: number;
    typeFlag?: ItemTypeFlag;
    orderBy?:
      | Prisma.ItemOrderByWithRelationInput
      | Prisma.ItemOrderByWithRelationInput[];
  }
) {
  const items = await prisma.item.findMany({
    where: {
      piid_path: {
        startsWith: get_root_iid_path(prjId),
      },
      deleted_at: 0,
      type_flag: typeFlag,
      OR: [
        { type_flag: ItemTypeFlag.Folder },
        { type_flag: ItemTypeFlag.File, size: { gt: 0 } },
      ],
    },
    take: length,
    orderBy,
  });

  return items;
}
