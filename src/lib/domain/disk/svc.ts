import { DomainError } from "@/lib/domain/common/error";
import { S3Client } from "@/lib/infra/s3/client";
import { isS3FileAlreadyExistsError, makeNameWithDate } from "@/lib/utils";
import { type LifecycleRule } from "@aws-sdk/client-s3";
import path from "path";

export type FileInfo = {
  isDir: false;
  size?: number;
  lastModified?: Date;
  diskPath: string;
};

export type DirInfo = {
  isDir: true;
  diskPath: string;
};

export class OSSKey {
  static projectRoot = "project/";
  static taskTmpOutputsRoot = "task-tmp-outputs/";
  static diskUploadTusInfoRoot = "disk-upload-tus-info/";

  static underProjectDisk(
    {
      projectId,
    }: {
      projectId: string;
    },
    ...paths: string[]
  ) {
    return path.join(this.projectRoot, projectId, "disk/", ...paths);
  }

  static relativeToDiskRoot(ossKey: string) {
    const diskRoot = this.underProjectDisk({ projectId: ".*?" });
    return ossKey.replace(diskRoot, "/");
  }

  static underProjectTask(
    {
      projectId,
      taskId,
    }: {
      projectId: string;
      taskId: string;
    },
    ...paths: string[]
  ) {
    return path.join(this.projectRoot, projectId, "task/", taskId, ...paths);
  }

  static underDiskUploadTusInfo(...paths: string[]) {
    return path.join(this.diskUploadTusInfoRoot, ...paths);
  }

  static underTaskTmpOutputs(
    { taskId }: { taskId: string },
    ...paths: string[]
  ) {
    return path.join(this.taskTmpOutputsRoot, taskId, ...paths);
  }

  static taskSnapshot({
    projectId,
    taskId,
  }: {
    projectId: string;
    taskId: string;
  }) {
    return this.underProjectTask({ projectId, taskId }, "snampshot");
  }
}

export type ObjectInfo = FileInfo | DirInfo;

export class DiskSvc {
  private readonly lifecycleRules: LifecycleRule[];

  constructor(private readonly s3Client: S3Client) {
    this.lifecycleRules = [
      {
        ID: "TusInfoCleanup",
        Status: "Enabled",
        Filter: {
          Prefix: OSSKey.diskUploadTusInfoRoot,
        },
        Expiration: {
          Days: 3,
        },
      },
      {
        ID: "UploadMultipartCleanup",
        Status: "Enabled",
        Filter: {
          Prefix: OSSKey.projectRoot,
        },
        AbortIncompleteMultipartUpload: {
          DaysAfterInitiation: 3,
        },
      },
      {
        ID: "TaskTmpOutputsCleanup",
        Status: "Enabled",
        Filter: {
          Prefix: OSSKey.taskTmpOutputsRoot,
        },
        Expiration: {
          Days: 3,
        },
      },
    ];
  }

  async *listFirstLevel({
    projectId,
    prefix,
    isDir,
  }: {
    projectId: string;
    prefix: string;
    isDir?: boolean;
  }) {
    const diskPathPrefix = OSSKey.underProjectDisk({ projectId }, prefix);

    const objects = this.s3Client.listInfiniteObject({
      prefix: diskPathPrefix,
      delimiter: "/",
    });

    for await (const { Contents, CommonPrefixes } of objects) {
      for (const { Prefix } of CommonPrefixes) {
        if (!Prefix) {
          continue;
        }

        const objInfo: ObjectInfo = {
          isDir: true,
          diskPath: OSSKey.relativeToDiskRoot(Prefix),
        };
        yield objInfo;
      }

      if (isDir) {
        return;
      }

      for (const { Key, LastModified, Size } of Contents) {
        if (!Key || Key === diskPathPrefix) {
          // skip self as this function only list first level
          continue;
        }
        const objInfo: ObjectInfo = {
          isDir: false,
          size: Size,
          lastModified: LastModified,
          diskPath: OSSKey.relativeToDiskRoot(Key),
        };
        yield objInfo;
      }
    }
  }

  async createDir({
    projectId,
    parentPath,
    name,
  }: {
    projectId: string;
    parentPath: string;
    name: string;
  }) {
    const ossPath = OSSKey.underProjectDisk({ projectId }, parentPath, name);
    const result = await this.s3Client.createDir(ossPath);
    return result;
  }

  async remove({
    projectId,
    pathPart,
  }: {
    projectId: string;
    pathPart: string;
  }) {
    const diskKey = OSSKey.underProjectDisk({ projectId }, pathPart);
    await this.removeOssKey({ prefix: diskKey });
  }

  async removeOssKey({ prefix }: { prefix: string }) {
    const prefixIsDir = isDirPath(prefix);

    if (prefixIsDir) {
      const objects = this.s3Client.listInfiniteObject({
        prefix: prefix,
      });

      for await (const { Contents, CommonPrefixes } of objects) {
        const keys = Contents.map(({ Key }) => Key).filter(
          (key) => key !== undefined
        );

        if (keys.length > 0) {
          await this.s3Client.deleteObjects(keys);
        }

        const commonPrefixesKeys = CommonPrefixes.map(
          ({ Prefix }) => Prefix
        ).filter((key) => key !== undefined);

        if (commonPrefixesKeys.length > 0) {
          await this.s3Client.deleteObjects(commonPrefixesKeys);
        }
      }
    } else {
      await this.s3Client.deleteObject(prefix);
    }
  }

  private async batchCopyObjects({
    srcKeyPrefix,
    toDstKey,
    forbidOverwrite,
  }: {
    srcKeyPrefix: string;
    toDstKey: (sourceKey: string) => string;
    forbidOverwrite: boolean;
  }) {
    const srcKeyIsDir = isDirPath(srcKeyPrefix);
    if (srcKeyIsDir) {
      const dirs = this.s3Client.listInfiniteObject({
        prefix: srcKeyPrefix,
      });

      for await (const { Contents } of dirs) {
        for (const content of Contents ?? []) {
          const { Key } = content;
          if (!Key) continue;

          const destinationKey = path.normalize(toDstKey(Key));
          await this.s3Client.copyObject(Key, destinationKey, forbidOverwrite);
        }
      }
    } else {
      const destinationKey = path.normalize(toDstKey(srcKeyPrefix));
      await this.s3Client.copyObject(
        srcKeyPrefix,
        destinationKey,
        forbidOverwrite
      );
    }
  }

  async copyTo({
    projectId,
    sourcePath,
    destinationPath,
    forbidOverwrite = false,
  }: {
    projectId: string;
    sourcePath: string;
    destinationPath: string;
    forbidOverwrite?: boolean;
  }) {
    const srcOssKey = OSSKey.underProjectDisk({ projectId }, sourcePath);
    const dstOssKey = OSSKey.underProjectDisk({ projectId }, destinationPath);
    await this.copyToOssKey({ srcOssKey, dstOssKey, forbidOverwrite });
  }

  async copyToOssKey({
    srcOssKey,
    dstOssKey,
    forbidOverwrite,
  }: {
    srcOssKey: string;
    dstOssKey: string;
    forbidOverwrite: boolean;
  }) {
    const srcParentPath = path.dirname(srcOssKey);
    await this.batchCopyObjects({
      srcKeyPrefix: srcOssKey,
      toDstKey: (key) => key.replace(srcParentPath, dstOssKey),
      forbidOverwrite,
    });
  }

  async moveTo({
    projectId,
    sourcePath,
    destinationPath,
  }: {
    projectId: string;
    sourcePath: string;
    destinationPath: string;
  }) {
    const srcOssKey = OSSKey.underProjectDisk({ projectId }, sourcePath);
    const dstOssKey = OSSKey.underProjectDisk({ projectId }, destinationPath);
    await this.moveToOssKey({ srcOssKey, dstOssKey, forbidOverwrite: true });
  }

  async moveToOssKey({
    srcOssKey,
    dstOssKey,
    forbidOverwrite,
  }: {
    srcOssKey: string;
    dstOssKey: string;
    forbidOverwrite: boolean;
  }) {
    if (getParentFolderDiskPath(srcOssKey) === dstOssKey) {
      // same level
      return;
    }

    if (dstOssKey.startsWith(srcOssKey)) {
      throw new DomainError("forbid_to_move_to_sub_dir");
    }

    await this.copyToOssKey({ srcOssKey, dstOssKey, forbidOverwrite });
    await this.removeOssKey({ prefix: srcOssKey });
  }

  async createCopy({
    projectId,
    sourcePath,
  }: {
    projectId: string;
    sourcePath: string;
  }) {
    const srcOssKey = OSSKey.underProjectDisk({ projectId }, sourcePath);
    const srcParentPath = path.dirname(srcOssKey);
    const srcIsDir = isDirPath(srcOssKey);
    const srcBaseName = path.basename(srcOssKey);
    const srcBaseNameWithDate = makeNameWithDate(srcBaseName, srcIsDir);
    const dstKey = path.join(
      srcParentPath,
      srcBaseNameWithDate,
      srcIsDir ? "/" : ""
    );

    await this.batchCopyObjects({
      srcKeyPrefix: srcOssKey,
      toDstKey: (srcKey) => srcKey.replace(srcOssKey, dstKey),
      forbidOverwrite: true,
    });
  }

  async rename({
    projectId,
    parentPath,
    originName,
    name,
  }: {
    projectId: string;
    parentPath: string;
    originName: string;
    name: string;
  }) {
    const srcOssKey = OSSKey.underProjectDisk(
      { projectId },
      parentPath,
      originName
    );
    const srcParentKey = OSSKey.underProjectDisk({ projectId }, parentPath);
    const srcIsDir = isDirPath(srcOssKey);
    const dstOssKey = path.join(srcParentKey, name, srcIsDir ? "/" : "");

    try {
      await this.batchCopyObjects({
        srcKeyPrefix: srcOssKey,
        toDstKey: (srcKey) => srcKey.replace(srcOssKey, dstOssKey),
        forbidOverwrite: true,
      });
    } catch (error) {
      if (isS3FileAlreadyExistsError(error)) {
        throw new DomainError("disk_object_already_exists");
      }
      throw error;
    }

    await this.removeOssKey({ prefix: srcOssKey });
  }

  async setupCleanupLifecycle() {
    await this.s3Client.putLifecycle(this.lifecycleRules);
  }
}

export function isDirPath(path: string) {
  return path.endsWith("/");
}

export function getParentFolderDiskPath(diskPath: string) {
  const parentDiskPath = path.join(path.dirname(diskPath), "/");
  return parentDiskPath;
}

if (import.meta.vitest) {
  const { it, expect, describe } = import.meta.vitest;

  describe("isDir", () => {
    it("normal file name", () => {
      expect(isDirPath("a.txt")).toBe(false);
    });

    it("normal folder name", () => {
      expect(isDirPath("dirA/")).toBe(true);
    });
  });
}
