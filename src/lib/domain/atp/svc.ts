import { IrsModelInfo, LLMPricing } from "@/lib/domain/ai/llm/irs";
import {
  AtpPerFen,
  AtpUnit,
  RechargeOrderStatus,
  TransactionsSourceInfo,
  type ATPBalance,
  type ConsumptionExtra,
  type HourlyTransaction,
  type PreConsumption,
  type TransactionsSourceType,
  type WechatPayCallbackData,
} from "@/lib/domain/atp/m";
import { DomainError } from "@/lib/domain/common/error";
import type { Operator } from "@/lib/domain/user/m";
import type {
  CallbackPayInfo,
  QueryPayInfo,
  WeChatPayClient,
} from "@/lib/infra/wechat/client";
import { PrismaClient } from "@prisma/client";
import { Decimal } from "@prisma/client/runtime/library";
import type { LanguageModelUsage } from "ai";
import Big from "big.js";
import dayjs from "dayjs";
import pRetry from "p-retry";

export class ATPSvc {
  constructor(private readonly wechat_pay: WeChatPayClient) {}

  async ensureWithAtp(prisma: PrismaClient, operator: Operator) {
    const balance = await this.getATPBalance(prisma, operator);

    if (!balance || !balance.atp.gt(0)) {
      throw new DomainError("atp_not_sufficient");
    }

    return balance;
  }

  async getATPBalance(
    prisma: PrismaClient,
    operator: Operator
  ): Promise<ATPBalance> {
    const selectedFields = {
      user_id: true,
      atp: true,
      version: true,
    };

    await this.archivePreConsumptionsToAtp(prisma, operator);

    let balance = await prisma.userBalance.findUnique({
      where: {
        user_id: operator.id,
      },

      select: selectedFields,
    });

    if (!balance) {
      balance = await prisma.userBalance.upsert({
        where: {
          user_id: operator.id,
        },
        update: {},
        create: {
          user_id: operator.id,
        },
        select: selectedFields,
      });
    }

    return balance;
  }

  async createRechargeOrder(
    prisma: PrismaClient,
    operator: Operator,
    { amountByFen }: { amountByFen: number }
  ) {
    const user_id = operator.id;
    const atp = Big(amountByFen).times(AtpPerFen).toNumber();
    const order = await prisma.recharge.create({
      data: {
        user_id: user_id,
        status: RechargeOrderStatus.Pending,
        amount: amountByFen,
        atp: atp,
      },
    });

    if (operator.isTestAccount) {
      amountByFen = 1;
    }

    const pay_code_url = await this.wechat_pay.generatePayQRCode(
      order.id,
      amountByFen,
      "IRRISS 账户 ATP 充值"
    );

    return { order_id: order.id, pay_code_url };
  }

  private calculateAtp(pricing: LLMPricing, tokenUsage: LanguageModelUsage) {
    const { inputTokens, outputTokens } = tokenUsage;
    const { atpPerPromptToken, atpPerCompletionToken } = pricing;

    const promptAtp = Big(inputTokens ?? 0).times(atpPerPromptToken);
    const completionAtp = Big(outputTokens ?? 0).times(atpPerCompletionToken);

    return Big(promptAtp).plus(completionAtp).div(AtpUnit).toNumber();
  }

  async consumeTokens(
    prisma: PrismaClient,
    operator: Operator,
    {
      threadId,
      msgId,
      irsModelInfo,
      tokenUsage,
    }: {
      threadId: string;
      msgId: string;
      irsModelInfo: IrsModelInfo;
      tokenUsage: LanguageModelUsage;
    }
  ) {
    const atp = this.calculateAtp(irsModelInfo.pricing, tokenUsage);

    const token_usage = {
      promptTokens: tokenUsage.inputTokens ?? 0,
      completionTokens: tokenUsage.outputTokens ?? 0,
      totalTokens: tokenUsage.totalTokens ?? 0,
    };
    const extra_info: ConsumptionExtra = {
      thread_id: threadId,
      message_id: msgId,
      model: irsModelInfo.model,
      provider: irsModelInfo.provider,
      pricing: irsModelInfo.pricing,
      token_usage: token_usage,
    };

    await prisma.tokenConsumption.create({
      data: {
        atp,
        user_id: operator.id,
        tokens: token_usage.totalTokens,
        is_preconsumption: true,
        extra_info: extra_info,
      },
    });
  }

  async getRechargeStatus(
    prisma: PrismaClient,
    operator: Operator,
    {
      orderId,
      sync,
    }: {
      orderId: string;
      sync: boolean;
    }
  ) {
    const rechargeOrder = await prisma.recharge.findUniqueOrThrow({
      where: {
        id: orderId,
        user_id: operator.id,
      },
      select: {
        status: true,
      },
    });

    const rechargeStatus = rechargeOrder.status as RechargeOrderStatus;

    if (rechargeStatus == RechargeOrderStatus.Pending && sync) {
      const payInfo = await this.wechat_pay.queryOrderPayInfo(orderId);
      const newRechargeStatus = await this.#processPayInfo(prisma, operator, {
        rechargeId: orderId,
        payInfo,
      });

      return newRechargeStatus;
    }

    return rechargeStatus;
  }

  /**
   * recharge order status must be Pending when calling this function
   */
  async #processPayInfo(
    prisma: PrismaClient,
    operator: Operator,
    {
      rechargeId,
      payInfo,
      payCallbackTime,
    }: {
      rechargeId: string;
      payInfo: CallbackPayInfo | QueryPayInfo;
      payCallbackTime?: string;
    }
  ) {
    const trade_state = payInfo.trade_state;
    if (trade_state == "NOTPAY") {
      return RechargeOrderStatus.Pending;
    }

    const newRechargeStatus =
      trade_state === "SUCCESS"
        ? RechargeOrderStatus.PaidSuccess
        : RechargeOrderStatus.PaidFailed;

    const pay_transaction_id = payInfo.transaction_id;
    const pay_time = payInfo.success_time;

    const updatedRecords = await prisma.recharge.updateMany({
      where: {
        id: rechargeId,
        status: RechargeOrderStatus.Pending,
      },
      data: {
        status: newRechargeStatus,
        pay_time: pay_time,
        pay_transaction_id: pay_transaction_id,
        pay_callback_result: payInfo,
        pay_callback_time: payCallbackTime,
      },
    });

    if (updatedRecords.count === 0) {
      throw new DomainError("recharge_order_status_changed");
    }

    if (newRechargeStatus == RechargeOrderStatus.PaidSuccess) {
      await this.tryRechargeAtp(prisma, operator, { rechargeId: rechargeId });
    }

    return newRechargeStatus;
  }

  async tryRechargeAtp(
    prisma: PrismaClient,
    operator: Operator,
    {
      rechargeId,
    }: {
      rechargeId: string;
    }
  ) {
    const userId = operator.id;

    const recharge = async () => {
      return await prisma.$transaction(async (tx) => {
        const balance = await tx.userBalance.findUniqueOrThrow({
          where: {
            user_id: userId,
          },
        });

        const rechargeOrder = await tx.recharge.findUniqueOrThrow({
          where: {
            id: rechargeId,
          },
        });

        if (rechargeOrder.status !== RechargeOrderStatus.PaidSuccess) {
          return;
        }

        // 1. update recharge order status
        await tx.recharge.update({
          where: {
            id: rechargeId,
          },
          data: {
            status: RechargeOrderStatus.Recharged,
          },
        });

        const sourceInfo: TransactionsSourceInfo = {
          type: "recharge",
          info: {
            amount: rechargeOrder.amount.toNumber(),
            atp: rechargeOrder.atp.toNumber(),
          },
        };

        // 2. create transaction record
        const tran_record = await tx.transaction.create({
          data: {
            user_id: userId,
            atp: rechargeOrder.atp,
            source_info: sourceInfo,
            source_type: sourceInfo.type,
          },
        });

        const newVersion = tran_record.id;
        const rechargedAtp = balance.atp.add(rechargeOrder.atp);

        // 3. update balance
        const updatedRecords = await tx.userBalance.updateMany({
          where: {
            user_id: userId,
            version: balance.version,
          },
          data: {
            atp: rechargedAtp,
            version: newVersion,
          },
        });

        if (updatedRecords.count === 0) {
          throw new DomainError("balance_version_changed");
        }

        return rechargedAtp;
      });
    };

    // retry 10 times each 1 secs
    try {
      await pRetry(recharge, {
        maxTimeout: 1000,
        retries: 10,
      });
    } catch (err) {
      const reason = String(err);
      await prisma.failedRecharge.create({
        data: {
          recharge_id: rechargeId,
          reason: reason,
        },
      });
    }
  }

  async processRechageCallback(
    prisma: PrismaClient,
    callbackData: WechatPayCallbackData
  ) {
    const payCallbackTime = callbackData.create_time;

    const payInfo = this.wechat_pay.decipher(callbackData.resource);
    const rechargeId = payInfo.out_trade_no;
    const rechargeOrder = await prisma.recharge.findUniqueOrThrow({
      where: {
        id: rechargeId,
      },
    });
    const rechargeStatus = rechargeOrder.status as RechargeOrderStatus;

    if (rechargeStatus == RechargeOrderStatus.Pending) {
      await this.#processPayInfo(
        prisma,
        { id: rechargeOrder.user_id, isTestAccount: false },
        {
          rechargeId: rechargeId,
          payInfo,
          payCallbackTime: payCallbackTime,
        }
      );
    }
  }

  toHourlyTransactions(
    operator: Operator,
    preConsumptions: PreConsumption[]
  ): HourlyTransaction[] {
    const sourceType: TransactionsSourceType = "token-consumation";
    const grouped = preConsumptions.reduce<
      Record<string, { atp: Decimal; tokens: Decimal }>
    >((acc, curr) => {
      const time = dayjs(curr.consumed_at);
      const date = time.format("YYYY-MM-DD");
      const hour = time.format("HH");
      const key = `${date} ${hour}:00 - ${hour}:59`;

      const belongGroup = acc[key] ?? {
        atp: new Decimal(0),
        tokens: new Decimal(0),
      };
      belongGroup.atp = belongGroup.atp.add(curr.atp);
      belongGroup.tokens = belongGroup.tokens.add(curr.tokens);
      acc[key] = belongGroup;
      return acc;
    }, {});

    const transactions = Object.entries(grouped).map(
      ([dateRange, { atp, tokens }]) => ({
        user_id: operator.id,
        atp,
        source_info: {
          type: sourceType,
          info: {
            tokens: tokens.toNumber(),
            date_range: dateRange,
          },
        },
        source_type: sourceType,
      })
    );

    return transactions;
  }

  async getPreConsumptionsTillLastHour(
    prisma: PrismaClient,
    operator: Operator
  ): Promise<PreConsumption[]> {
    const endTime = dayjs().startOf("hour");

    const operatorPreConsumptions = await prisma.tokenConsumption.findMany({
      where: {
        user_id: operator.id,
        is_preconsumption: true,
      },
      select: {
        id: true,
        consumed_at: true,
        atp: true,
        tokens: true,
      },
    });

    const preConsumptionsTillLastHour = operatorPreConsumptions.filter(
      ({ consumed_at }) => dayjs(consumed_at).isBefore(endTime)
    );

    return preConsumptionsTillLastHour;
  }

  async settleHourlyAtp(
    prisma: PrismaClient,
    operator: Operator,
    preConsumptions: PreConsumption[],
    transactions: HourlyTransaction[]
  ): Promise<{ updatedAtp: Decimal; newVersion: string } | undefined> {
    if (preConsumptions.length === 0) {
      return;
    }

    const updatedBalanceInfo = await prisma.$transaction(async (tx) => {
      await tx.tokenConsumption.updateMany({
        where: {
          id: {
            in: preConsumptions.map((x) => x.id),
          },
        },
        data: {
          is_preconsumption: false,
        },
      });

      if (transactions.length === 0) {
        return;
      }

      const createdTransactions = await tx.transaction.createManyAndReturn({
        data: transactions,
      });

      const balance = await tx.userBalance.findUniqueOrThrow({
        where: {
          user_id: operator.id,
        },
        select: {
          atp: true,
          version: true,
        },
      });

      const totalAtp = transactions.reduce(
        (acc, curr) => acc.add(curr.atp),
        new Decimal(0)
      );

      const updatedAtp = balance.atp.minus(totalAtp);
      const newVersion = createdTransactions[createdTransactions.length - 1].id;
      const updatedBalances = await tx.userBalance.updateMany({
        where: {
          user_id: operator.id,
          version: balance.version,
        },
        data: {
          atp: updatedAtp,
          version: newVersion,
        },
      });

      if (updatedBalances.count === 0) {
        throw new DomainError("balance_version_changed");
      }

      return {
        updatedAtp,
        newVersion,
      };
    });

    return updatedBalanceInfo;
  }

  async archivePreConsumptionsToAtp(prisma: PrismaClient, operator: Operator) {
    const preConsumptions = await this.getPreConsumptionsTillLastHour(
      prisma,
      operator
    );
    const transactions = this.toHourlyTransactions(operator, preConsumptions);
    try {
      await pRetry(
        async () => {
          await this.settleHourlyAtp(
            prisma,
            operator,
            preConsumptions,
            transactions
          );
        },
        {
          maxTimeout: 1000,
          retries: 10,
        }
      );
    } catch (error) {
      // TODO: log collection & alarm
      console.error(error);
    }
  }
}

if (import.meta.vitest) {
  const { it, expect, vi, describe } = import.meta.vitest;
  const atpSvc = new ATPSvc({} as WeChatPayClient);
  const operator: Operator = { id: "1", isTestAccount: false };
  const sourceType = "token-consumation";
  const preConsumptions: PreConsumption[] = [
    {
      id: "1",
      consumed_at: new Date("2025-05-07 23:48:16:386"),
      atp: Decimal("0.0016512"),
      tokens: Decimal("100"),
    },
    {
      id: "2",
      consumed_at: new Date("2025-05-08 09:33:02:03"),
      atp: Decimal("0.0016512"),
      tokens: Decimal("587"),
    },
    {
      id: "3",
      consumed_at: new Date("2025-05-08 09:34:05:08"),
      atp: Decimal("0.********"),
      tokens: Decimal("1286"),
    },
    {
      id: "4",
      consumed_at: new Date("2025-05-08 09:59:59:999"),
      atp: Decimal("0.0030116"),
      tokens: Decimal("1352"),
    },
    {
      id: "5",
      consumed_at: new Date("2025-05-08 10:05:15:03"),
      atp: Decimal("0.********"),
      tokens: Decimal("2567"),
    },
  ];

  describe("test get hourly transactions", () => {
    it("should get hourly transactions rightly", () => {
      const transactions = atpSvc.toHourlyTransactions(
        operator,
        preConsumptions
      );
      expect(transactions.length).toBe(3);
      expect(transactions).toEqual([
        {
          user_id: operator.id,
          atp: Decimal("0.0016512"),
          source_info: {
            info: {
              date_range: "2025-05-07 23:00 - 23:59",
              tokens: 100,
            },
            type: sourceType,
          },
          source_type: sourceType,
        },
        {
          user_id: operator.id,
          atp: Decimal("0.00487196"),
          source_info: {
            info: {
              date_range: "2025-05-08 09:00 - 09:59",
              tokens: 3225,
            },
            type: sourceType,
          },
          source_type: sourceType,
        },
        {
          user_id: operator.id,
          atp: Decimal("0.********"),
          source_info: {
            info: {
              date_range: "2025-05-08 10:00 - 10:59",
              tokens: 2567,
            },
            type: sourceType,
          },
          source_type: sourceType,
        },
      ]);
    });
  });

  describe("test get pre consumptions", () => {
    it("should get pre consumptions rightly", async () => {
      const now = new Date("2025-05-08 10:45:15:03");
      vi.setSystemTime(now);
      const prisma = {
        tokenConsumption: {
          findMany: vi.fn().mockResolvedValue(preConsumptions),
        },
      } as unknown as PrismaClient;

      const result = await atpSvc.getPreConsumptionsTillLastHour(
        prisma,
        operator
      );
      expect(result.length).toBe(4);
      expect(result).toEqual([
        preConsumptions[0],
        preConsumptions[1],
        preConsumptions[2],
        preConsumptions[3],
      ]);
    });
  });

  describe("test settle hourly atp", () => {
    it("should settle hourly atp return undefined", async () => {
      const tx = {
        tokenConsumption: {
          updateMany: vi.fn(),
        },
      };
      const prisma = {
        $transaction: vi.fn().mockImplementation((fn) => fn(tx)),
      } as unknown as PrismaClient;

      const result = await atpSvc.settleHourlyAtp(
        prisma,
        operator,
        preConsumptions,
        []
      );

      expect(result).toBe(undefined);
    });

    it("should throw error when settle hourly atp", async () => {
      const transactions = atpSvc.toHourlyTransactions(
        operator,
        preConsumptions
      );

      const tx = {
        tokenConsumption: {
          updateMany: vi.fn(),
        },
        transaction: {
          createManyAndReturn: vi
            .fn()
            .mockResolvedValue([{ id: "uuid_1" }, { id: "uuid_2" }]),
        },
        userBalance: {
          findUniqueOrThrow: vi.fn().mockResolvedValue({
            atp: Decimal("10.0"),
            version: "aaaa",
          }),
          updateMany: vi.fn().mockResolvedValue({ count: 0 }),
        },
      };
      const prisma = {
        $transaction: vi.fn().mockImplementation((fn) => fn(tx)),
      } as unknown as PrismaClient;

      await expect(
        atpSvc.settleHourlyAtp(prisma, operator, preConsumptions, transactions)
      ).rejects.toThrow(new DomainError("balance_version_changed"));
    });

    it("should return updated info when settle hourly atp", async () => {
      const transactions = atpSvc.toHourlyTransactions(
        operator,
        preConsumptions
      );
      const tx = {
        tokenConsumption: {
          updateMany: vi.fn(),
        },
        transaction: {
          createManyAndReturn: vi
            .fn()
            .mockResolvedValue([{ id: "uuid_1" }, { id: "uuid_2" }]),
        },
        userBalance: {
          findUniqueOrThrow: vi.fn().mockResolvedValue({
            atp: Decimal("10.0"),
            version: "aaaa",
          }),
          updateMany: vi.fn().mockResolvedValue({ count: 1 }),
        },
      };
      const prisma = {
        $transaction: vi.fn().mockImplementation((fn) => fn(tx)),
      } as unknown as PrismaClient;

      const result = await atpSvc.settleHourlyAtp(
        prisma,
        operator,
        preConsumptions,
        transactions
      );

      expect(result).toEqual({
        updatedAtp: Decimal("9.99317568"),
        newVersion: "uuid_2",
      });
    });
  });
}
