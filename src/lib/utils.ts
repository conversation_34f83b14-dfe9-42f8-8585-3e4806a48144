import { ensureATPSufficient } from "@/app-ui/actions/safe-actions";
import { DomainErrorCode, HandledError } from "@/lib/domain/common/error";
import { Prisma } from "@prisma/client";
import { App } from "antd";
import Big from "big.js";
import { clsx, type ClassValue } from "clsx";
import dayjs from "dayjs";
import duration from "dayjs/plugin/duration";
import relativeTime from "dayjs/plugin/relativeTime";
import { useCallback } from "react";
import { twMerge } from "tailwind-merge";

type FormatAtpAmountOptions = {
  sign?: "+" | "-" | "";
  precision?: number;
  round?: Big.RoundingMode;
};

export const DATE_FORMAT = "YYYY-MM-DD";
export const DATE_FORMAT_SLASH = "YYYY/MM/DD";
export const TIME_FORMAT = "HH:mm:ss";
export const TIME_FORMAT_BRIEF = "HH:mm";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function extractApiCodeFromChatError(err: Error): string | undefined {
  const message = err.message;

  try {
    const json = JSON.parse(message);
    return json.error_code;
  } catch {
    const match = message.match(/{"error_code":"([^"]+)"}/);
    return match?.[1];
  }
}

const default_code2msg: Record<DomainErrorCode, string> = {
  auth_failed: "认证失败",
  no_permission: "没有权限",
  thread_not_found: "聊天对话不存在",
  payclient_error: "支付客户端错误",
  atp_not_sufficient: "ATP余额不足",
  balance_version_changed: "余额版本变化",
  recharge_order_status_changed: "充值订单状态变化",
  arg_parse_failed: "参数解析失败",
  llm_not_found: "模型不存在",
  id_verify_error: "实名认证失败",
  no_available_provider: "没有可用的提供商",
  username_already_exists: "用户名已存在",
  forbid_to_move_to_sub_dir: "禁止移动到子目录",
  project_not_found: "项目不存在",
  item_not_found: "数据项不存在",
  task_type_not_supported: "该类任务暂不支持",
  start_task_api_failed: "启动任务失败",
  item_name_conflict: "名称冲突，请修改后重试",
  disk_object_already_exists: "名称冲突，请修改后重试",
};

export type APIKind = "RouteHandler" | "SafeAction";

type ServerError = {
  error_code?: string;
};

type ProcessRes = {
  msg?: string;
};

function processRouteHandlerRes(
  code2msg: Record<string, string>,
  res: unknown
): ProcessRes {
  const routeHandlerRes = res as
    | {
        status?: number;
        data?: ServerError;
      }
    | undefined;

  let msg;

  if (routeHandlerRes?.status === 400) {
    const error_code = routeHandlerRes?.data?.error_code;
    if (error_code) {
      msg = code2msg[error_code] ?? error_code;
    }
  }

  return {
    msg,
  };
}

function processSafeActionRes(
  code2msg: Record<string, string>,
  data: unknown
): ProcessRes {
  const actionRes = data as
    | {
        serverError?: ServerError;
        validationErrors?: unknown;
        bindArgsValidationErrors?: unknown;
      }
    | undefined;

  let msg;

  const error_code = actionRes?.serverError?.error_code;
  if (error_code) {
    msg = code2msg[error_code] ?? error_code;
  } else {
    if (actionRes?.validationErrors || actionRes?.bindArgsValidationErrors)
      msg = "请求参数有误: " + JSON.stringify(actionRes);
  }

  return {
    msg,
  };
}

export function throwIfSafeActionError<T>(data: T) {
  const actionRes = data as
    | {
        serverError?: ServerError;
        validationErrors?: unknown;
        bindArgsValidationErrors?: unknown;
      }
    | undefined;
  const { serverError, validationErrors, bindArgsValidationErrors } =
    actionRes || {};
  if (serverError || validationErrors || bindArgsValidationErrors) {
    throw data;
  }
  return data;
}

export function useHandleApiErrorDefault({
  code2msg,
}: {
  code2msg?: Record<string, string>;
} = {}) {
  const { message } = App.useApp();

  return useCallback(
    (data: unknown) => {
      const mergedCode2Msg: Record<string, string> = {
        ...default_code2msg,
        ...code2msg,
      };
      const internalData = data as
        | {
            error_handled?: boolean;
          }
        | undefined;

      if (internalData?.error_handled) {
        throw data;
      }

      let processRes: ProcessRes;
      processRes = processRouteHandlerRes(mergedCode2Msg, data);
      if (!processRes.msg) {
        processRes = processSafeActionRes(mergedCode2Msg, data);
      }

      let msg = processRes.msg;

      if (!msg) {
        msg = "出错啦: " + JSON.stringify(data);
      }

      message.error({ content: msg, key: msg });

      throw new HandledError("API call failed", { cause: data });
    },
    [code2msg, message]
  );
}

export function formatAtpAmount(
  amount: number | string,
  options?: FormatAtpAmountOptions
) {
  const { sign = "", precision = 4, round = Big.roundDown } = options || {};

  return `${sign}${Big(amount).toFixed(precision, round)}`;
}

export function getByteLength(str: string) {
  const encoder = new TextEncoder();
  const uint8Array = encoder.encode(str);
  return uint8Array.length;
}

export function useHandleAtpNotSufficientError() {
  const { modal } = App.useApp();

  return useCallback(() => {
    modal.confirm({
      title: "ATP余量不足",
      content: "当前账户下ATP余量不足，请购买后使用。",
      autoFocusButton: null,
      okText: "购买",
      onOk: () => {
        window.open("/atp/account", "_blank");
      },
    });
  }, [modal]);
}

export function useEnsureATPSufficient() {
  const handleAtpNotSufficientError = useHandleAtpNotSufficientError();

  return useCallback(async (): Promise<boolean> => {
    const result = await ensureATPSufficient();

    if (result?.serverError?.error_code === "atp_not_sufficient") {
      handleAtpNotSufficientError();
      return false;
    } else {
      return true;
    }
  }, [handleAtpNotSufficientError]);
}

export function humanFileSize(sizeInByte: number | undefined) {
  if (sizeInByte === undefined || sizeInByte === null || sizeInByte <= 0) {
    return "-";
  }
  const i =
    sizeInByte === 0 ? 0 : Math.floor(Math.log(sizeInByte) / Math.log(1024));
  return (
    +(sizeInByte / Math.pow(1024, i)).toFixed(2) * 1 +
    " " +
    ["B", "KB", "MB", "GB", "TB"][i]
  );
}

export function formatDate(
  date: dayjs.ConfigType,
  format: string = DATE_FORMAT + " " + TIME_FORMAT,
  empty: string = "-"
) {
  if (!date) {
    return empty;
  }

  return dayjs(date).format(format);
}

export function unixtime_now_in_ms() {
  return dayjs().valueOf();
}

export function makeNameWithDate(name: string, isFolder: boolean) {
  const timestamp = dayjs().format("HHmmss");

  if (isFolder) {
    return `${name}-${timestamp}`;
  } else {
    const firstDotIndex = name.indexOf(".");
    if (firstDotIndex === -1) {
      return `${name}-${timestamp}`;
    } else {
      const beforeFirstDot = name.substring(0, firstDotIndex);
      const afterFirstDot = name.substring(firstDotIndex);
      return `${beforeFirstDot}-${timestamp}${afterFirstDot}`;
    }
  }
}

export function isDbUniqueConstraintError(error: unknown) {
  return (
    error instanceof Prisma.PrismaClientKnownRequestError &&
    error.code === "P2002"
  );
}

export function isS3FileAlreadyExistsError(error: unknown) {
  const S3Error = error as { code?: unknown; status?: unknown };
  return S3Error.status === 409 && S3Error.code === "FileAlreadyExists";
}

if (import.meta.vitest) {
  const { it, expect, vi, describe } = import.meta.vitest;

  describe("genUniqueName", () => {
    vi.mock("dayjs", () => {
      return {
        default: vi.fn().mockReturnValue({
          format: () => "104532",
        }),
      };
    });

    it("normal file name", () => {
      expect(makeNameWithDate("dll.txt", false)).toBe("dll-104532.txt");
    });

    it("multiple dots in file name", () => {
      expect(makeNameWithDate("a.dll.t.q.txt", false)).toBe(
        "a-104532.dll.t.q.txt"
      );
    });

    it("no dot in file name", () => {
      expect(makeNameWithDate("dockerfile", false)).toBe("dockerfile-104532");
    });

    it("dot in folder name", () => {
      expect(makeNameWithDate("aa.txt", true)).toBe("aa.txt-104532");
    });

    it("normal folder name", () => {
      expect(makeNameWithDate("dirA", false)).toBe("dirA-104532");
    });
  });
}

/**
    int8 
    uint8 
    int16 
    uint16 
    int32 
    uint32 
    int64 
    uint64 
    float32 
    float64 
    complex64
    complex64
 */

export function dtypeToBit(dtype: string) {
  let bits = dtype;
  const matches = dtype.match(/(\d+)/);
  if (matches) {
    bits = matches[0];
  }

  return parseInt(bits);
}

export function dtypeToRange(dtype: string) {
  if (dtype === "float32") {
    return { min: -1, max: 1 };
  }
  const bits = dtypeToBit(dtype);
  return { min: 0, max: Math.pow(2, bits) - 1 };
}

export function durationDisplay(
  startAt: dayjs.ConfigType,
  finishedAt: dayjs.ConfigType
) {
  if (!startAt || !finishedAt) {
    return "-";
  }

  dayjs.extend(duration);
  dayjs.extend(relativeTime);

  return dayjs.duration(dayjs(finishedAt).diff(startAt)).humanize();
}
