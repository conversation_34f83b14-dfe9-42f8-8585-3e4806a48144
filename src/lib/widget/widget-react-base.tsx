/* eslint-disable @typescript-eslint/no-explicit-any -- needs to be rewrite with new widget design */
import { Deck, Widget, WidgetPlacement } from "@deck.gl/core";
import {
  FC,
  createElement,
  createRef,
  forwardRef,
  useImperativeHandle,
  useState,
  type RefObject,
} from "react";
import { Root, createRoot } from "react-dom/client";

type WidgetProps<PropsT> = PropsT & {
  id: string;
  viewId?: string;
  placement?: WidgetPlacement;
};

interface WidgetHandler<PropsT> {
  setState(props: PropsT): void;
}

export default abstract class WidgetReactBase<PropsT extends object>
  implements Widget<WidgetProps<PropsT>> {
  id: string;
  props: WidgetProps<PropsT>;
  viewId?: string;
  placement?: WidgetPlacement;

  root?: Root;
  ref?: RefObject<any>;

  abstract inner: FC<PropsT>;

  constructor(props: WidgetProps<PropsT>) {
    this.props = props;
    this.id = props.id;
    this.viewId = props.viewId;
    this.placement = props.placement;
  }

  onAdd(params: { deck: Deck<any>; viewId: string | null }) {
    const el = document.createElement("div");

    const root = createRoot(el);
    this.root = root;
    this.ref = createRef();
    const wrapped = this.createWrapped();

    setTimeout(() => {
      this.root?.render(wrapped);
    });

    return el;
  }

  onRemove() {
    if (this.root) {
      setTimeout(() => {
        this.root?.unmount();
      });
    }

    this.ref = undefined;
  }

  setProps(props: Partial<WidgetProps<PropsT>>) {
    Object.assign(this.props, props);
    this.update();
  }

  update() {
    this.ref?.current?.setState({ ...this.props });
  }

  createWrapped() {
    const inner = this.inner;
    const props = this.props;

    const wrapper = forwardRef(function Wrapper(_, ref) {
      const [innerProps, setInnerProps] = useState<PropsT>(props);

      useImperativeHandle<unknown, WidgetHandler<PropsT>>(
        ref,
        () => {
          return {
            setState(props: PropsT) {
              setInnerProps(props);
            },
          };
        },
        []
      );

      return createElement(inner, innerProps);
    });

    return createElement(wrapper, { ref: this.ref });
  }
}
