import type {
  FinishedTasksReq,
  FinishedTasksRes,
} from "@/app/api/task/done/route";
import type {
  RunningTasksReq,
  RunningTasksRes,
} from "@/app/api/task/running/route";
import { DiverseApiEndpointBuilder } from "@/lib/apis/diverse/api";

export const taskEndpoints = (builder: DiverseApiEndpointBuilder) => {
  return {
    getRunningTasks: builder.query<RunningTasksRes, RunningTasksReq>({
      query: (req) => {
        return {
          url: "task/running",
          params: req,
        };
      },
      providesTags: (_, err, req) => {
        return err
          ? []
          : [{ type: "task-running", id: `${req.prjId}-${req.length}` }];
      },
    }),
    getFinishedTasks: builder.query<FinishedTasksRes, FinishedTasksReq>({
      query: (req) => {
        return {
          url: "task/done",
          params: req,
        };
      },
      providesTags: (_, err, req) => {
        return err
          ? []
          : [{ type: "task-done", id: `${req.prjId}-${req.length}` }];
      },
    }),
  };
};
