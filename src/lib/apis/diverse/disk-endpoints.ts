import type { ListDiskRes, ListDiskReq } from "@/app/api/disk/route";
import { DiverseApiEndpointBuilder } from "@/lib/apis/diverse/api";

export const diskEndpoints = (builder: DiverseApiEndpointBuilder) => {
  return {
    listDisk: builder.query<ListDiskRes, ListDiskReq>({
      query: (req) => {
        return {
          url: "disk",
          params: req,
        };
      },
      providesTags: (_, err, req) => {
        return err ? [] : [{ type: "disk", id: generateDiskTagId(req) }];
      },
    }),
  };
};

function generateDiskTagId({
  projectId,
  prefix,
}: {
  projectId: string;
  prefix: string;
}) {
  return `${projectId}/${prefix}`;
}
