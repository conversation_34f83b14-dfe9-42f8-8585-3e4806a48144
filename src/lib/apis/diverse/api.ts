import { GetTileReq } from "@/app/api/image/tile/route";
import { diskEndpoints } from "@/lib/apis/diverse/disk-endpoints";
import { imageEndpoints } from "@/lib/apis/diverse/image-endpoints";
import { itemEndpoints } from "@/lib/apis/diverse/item-endpoints";
import { llmEndpoints } from "@/lib/apis/diverse/llm-endpoints";
import { taskEndpoints } from "@/lib/apis/diverse/task-endpoints";
import { TileImage } from "@/lib/layers/intensity-layer";
import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import wretch from "wretch";
import queryString from "wretch/addons/queryString";

// Define a service using a base URL and expected endpoints
export const baseApi = createApi({
  reducerPath: "diverseApi",
  baseQuery: fetchBaseQuery({ baseUrl: "/api" }),
  tagTypes: [
    "thread",
    "disk",
    "item",
    "item-subs",
    "items-recent",
    "items-history",
    "items-hierarchy",
    "task",
    "task-running",
    "task-done",
    "image-meta",
    "image-original-meta",
  ],
  endpoints: () => ({}),
});

export type DiverseApiEndpointBuilder = Parameters<
  Parameters<typeof baseApi.injectEndpoints>[0]["endpoints"]
>[0];

export const diverseApi = baseApi
  .injectEndpoints({
    endpoints: llmEndpoints,
  })
  .injectEndpoints({
    endpoints: diskEndpoints,
  })
  .injectEndpoints({
    endpoints: itemEndpoints,
  })
  .injectEndpoints({
    endpoints: imageEndpoints,
  })
  .injectEndpoints({
    endpoints: taskEndpoints,
  });

export function fetchTile(
  req: GetTileReq,
  init?: RequestInit
): Promise<TileImage> {
  return wretch("/api/image/tile", init)
    .addon(queryString)
    .query(req)
    .get()
    .arrayBuffer((ab) => {
      const tileImage = new TileImage(ab);
      return tileImage;
    });
}

export type FetchTileFn = typeof fetchTile;
