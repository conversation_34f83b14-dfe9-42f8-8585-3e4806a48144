import { DiverseApiEndpointBuilder } from "@/lib/apis/diverse/api";
import { ImgInfo } from "@/lib/domain/img/query/svc";

export const imageEndpoints = (builder: DiverseApiEndpointBuilder) => {
  return {
    getImgMeta: builder.query<ImgInfo, string>({
      query: (itemId) => ({ url: "image/meta", params: { itemId } }),
      providesTags: (res, error, itemId) => [{ type: "image-meta", itemId }],
    }),
    getImgOriginalMeta: builder.query<GetOriginalMetaRes, string>({
      query: (itemId) => ({ url: "image/original-meta", params: { itemId } }),
      providesTags: (res, error, itemId) => [
        { type: "image-original-meta", itemId },
      ],
    }),
    getHistogram: builder.query<GetHistogramRes, GetHistogramReq>({
      query: (req) => {
        return {
          url: "image/histogram",
          params: req,
        };
      },
    }),
    getMinMaxByQuantile: builder.query<
      GetMinMaxByQuantileRes,
      GetMinMaxByQuantileReq
    >({
      query: (req) => ({ url: "image/min-max-by-quantile", params: req }),
    }),
  };
};

export const SupportedSaveAsType = <const>["irs", "tiff", "jpg"];
export type SaveAsType = (typeof SupportedSaveAsType)[number];

export interface XYRegion {
  x: number;
  y: number;
  width: number;
  height: number;
}

export const SupportedBitDepth = <const>["uint8", "uint16", "float32"];

export type BitDepth = (typeof SupportedBitDepth)[number];

export type RatioType = "intensity" | "pixel";

export interface GetMinMaxByQuantileReq {
  id: string;
  channel_id: number;
  min_quantile?: number;
  max_quantile?: number;
  ratio_type: RatioType;
}

export interface GetMinMaxByQuantileRes {
  min?: number;
  max?: number;
}

interface GetOriginalMetaRes {
  meta: object;
}

export interface GetHistogramReq {
  itemId: string;
}

export interface GetHistogramRes {
  channels: ChannelHistogram[];
}

interface ChannelHistogram {
  id: number;
  hist: number[];
  bins: number[];
}
