import type {
  ItemsHierarchyReq,
  ItemsHierarchyRes,
} from "@/app/api/item/hierarchy/route";
import type {
  HistoryItemsReq,
  HistoryItemsRes,
} from "@/app/api/item/history/route";
import type {
  RecentItemsReq,
  RecentItemsRes,
} from "@/app/api/item/recent/route";
import type { SubItemsReq, SubItemsRes } from "@/app/api/item/subs/route";
import { DiverseApiEndpointBuilder } from "@/lib/apis/diverse/api";

export const itemEndpoints = (builder: DiverseApiEndpointBuilder) => {
  return {
    getSubItems: builder.query<SubItemsRes, SubItemsReq>({
      query: (req) => {
        return {
          url: "item/subs",
          params: req,
        };
      },
      providesTags: (_, err, req) => {
        return err
          ? []
          : [
              {
                type: "item-subs",
                id: `${req.prjId}-${req.itemTypeFlag}-${req.pid}`,
              },
            ];
      },
    }),

    getRecentItems: builder.query<RecentItemsRes, RecentItemsReq>({
      query: (req) => {
        return {
          url: "item/recent",
          params: req,
        };
      },
      providesTags: (_, err, req) => {
        return err
          ? []
          : [
              {
                type: "items-recent",
                id: `${req.prjId}-${req.length}`,
              },
            ];
      },
    }),

    getHistoryItems: builder.query<HistoryItemsRes, HistoryItemsReq>({
      query: (req) => {
        return {
          url: "item/history",
          params: req,
        };
      },
      providesTags: (_, err, req) => {
        return err
          ? []
          : [
              {
                type: "items-history",
                id: `${req.prjId}-${req.length}`,
              },
            ];
      },
    }),

    getItemsHierarchy: builder.query<ItemsHierarchyRes, ItemsHierarchyReq>({
      query: (req) => {
        return {
          url: "item/hierarchy",
          params: req,
        };
      },
      providesTags: (_, err, req) => {
        return err
          ? []
          : [
              {
                type: "items-hierarchy",
                id: `${req.prjId}`,
              },
            ];
      },
    }),
  };
};
