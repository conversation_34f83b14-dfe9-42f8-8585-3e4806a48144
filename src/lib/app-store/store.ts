import { diverseApi } from "@/lib/apis/diverse/api";
import { diskSlice } from "@/lib/app-store/disk-slice";
import { imageSlice } from "@/lib/app-store/image-slice";
import { itemSlice } from "@/lib/app-store/item-slice";
import { llmSlice } from "@/lib/app-store/llm-slice";
import { configureStore } from "@reduxjs/toolkit";
import { setupListeners } from "@reduxjs/toolkit/query";
import { useDispatch, useSelector, useStore } from "react-redux";

export const makeStore = () => {
  const store = configureStore({
    reducer: {
      [diverseApi.reducerPath]: diverseApi.reducer,
      [llmSlice.reducerPath]: llmSlice.reducer,
      [diskSlice.reducerPath]: diskSlice.reducer,
      [itemSlice.reducerPath]: itemSlice.reducer,
      [imageSlice.reducerPath]: imageSlice.reducer,
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware().concat(diverseApi.middleware),
  });

  setupListeners(store.dispatch);

  return store;
};

// Infer the type of makeStore
export type AppStore = ReturnType<typeof makeStore>;
// Infer the `RootState` and `AppDispatch` types from the store itself
export type AppState = ReturnType<AppStore["getState"]>;
export type AppDispatch = AppStore["dispatch"];

// Use throughout your app instead of plain `useDispatch` and `useSelector`
export const useAppDispatch = useDispatch.withTypes<AppDispatch>();
export const useAppSelector = useSelector.withTypes<AppState>();
export const useAppStore = useStore.withTypes<AppStore>();
