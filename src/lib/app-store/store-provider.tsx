"use client";
import { AppStore, makeStore } from "@/lib/app-store/store";
// we need this to avoid antd warning on local dev, and it needs in a client component
import "@ant-design/v5-patch-for-react-19";
import { useRef } from "react";
import { Provider } from "react-redux";
export default function StoreProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const storeRef = useRef<AppStore>(undefined);
  if (!storeRef.current) {
    // Create the store instance the first time this renders
    storeRef.current = makeStore();
  }

  return <Provider store={storeRef.current}>{children}</Provider>;
}
