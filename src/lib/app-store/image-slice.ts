import { useAppSelector, type AppState } from "@/lib/app-store/store";
import type { ImgMeta } from "@/lib/domain/img/query/svc";
import type { MagFilter } from "@/lib/layers/intensity-layer";
import {
  blue,
  cyan,
  green,
  magenta,
  orange,
  purple,
  red,
  yellow,
} from "@ant-design/colors";
import { TinyColor } from "@ctrl/tinycolor";
import type {
  FeatureCollection,
  Geometry,
} from "@deck.gl-community/editable-layers";
import type { PayloadAction } from "@reduxjs/toolkit";
import { createSelector, createSlice } from "@reduxjs/toolkit";
import { bbox } from "@turf/bbox";
import { coordEach } from "@turf/meta";
import { useMemo } from "react";

export interface ChannelView {
  id: number;
  min?: number;
  max?: number;
  color?: string;
}

export type ToolId =
  | "scale-bar"
  | "info-picker"
  | "mini-map"
  | "draw-rect"
  | "select-draw";
export type MenuType = "image-operation" | "image-edit" | "image-meta";

interface SetScaleEvent {
  scale: number | "fit-screen";
}

export type InfoPannelType = "channel" | "operation";

interface State {
  setScaleEvent?: SetScaleEvent;
  scaleForShow?: number;
  channelViews: { [key in number]: ChannelView };
  shownChannelIds: number[];
  greyMode: boolean;
  enabledTools: ToolId[];
  gamma: number;
  magFilter: MagFilter;
  currentMenu: MenuType;
  currentChannelId?: number;
  drawFeatures: FeatureCollection;
  currentInfoPannel: InfoPannelType;
}

export const COLOR_PRESETS = [
  "#0000ff",
  "#ff0000",
  "#00ff00",
  "#00ffff",
  "#ff00ff",
  "#ffff00",
  "#ffffff",
  "#007d7d",
  "#7d007d",
  "#7d7d00",
].concat(red, green, blue, cyan, magenta, yellow, orange, purple);

export function useChannelMetaWithDefaultColor(
  channelMetas: ImgMeta["channels"]
) {
  return useMemo(() => {
    return channelMetas.map((x) => {
      const color = x.color || COLOR_PRESETS[x.id];
      return { ...x, color: color };
    });
  }, [channelMetas]);
}

export function useStateMergedChannels(channelMetas: ImgMeta["channels"]) {
  const defaultWithColor = useChannelMetaWithDefaultColor(channelMetas);
  const shownChannelIds = useAppSelector(selectShownChannelIds);
  const channelViews = useAppSelector((x) => x.image.channelViews);
  return useMemo(() => {
    const merged = defaultWithColor.map((x) => {
      const view = channelViews[x.id];
      return {
        ...{
          id: x.id,
          color: x.color,
          name: x.name,
          min: x.view_min ?? 0,
          max: x.view_max ?? x.max,
          shown: x.view_shown ?? true,
        },
        ...view,
        ...{
          shown: shownChannelIds.includes(x.id),
        },
      };
    });
    return merged;
  }, [defaultWithColor, channelViews, shownChannelIds]);
}

export function useStateCurrentChannelView(channelMetas: ImgMeta["channels"]) {
  const mergedChannels = useStateMergedChannels(channelMetas);
  const currentChannelId = useAppSelector(selectCurrentChannelId);
  const currentChannelView = mergedChannels.find(
    (x) => x.id === currentChannelId
  );
  return currentChannelView;
}

export function useStateCurrentChannelMeta(channelMetas: ImgMeta["channels"]) {
  const channelMetasWithDefaultColor =
    useChannelMetaWithDefaultColor(channelMetas);
  const currentChannelId = useAppSelector(selectCurrentChannelId);
  const currentChannelMeta = channelMetasWithDefaultColor.find(
    (x) => x.id === currentChannelId
  );
  return currentChannelMeta;
}

const GreyModeColor: [number, number, number, number] = [1, 1, 1, 1];

/**
 * this will change the color hex string into nomalized numbers
 */
export function useStateShownChannels(channelMetas: ImgMeta["channels"]) {
  const mergedChannels = useStateMergedChannels(channelMetas);
  const greyMode = useAppSelector(selectGreyMode);

  return useMemo(() => {
    const shown = mergedChannels
      .filter((x) => x.shown)
      .map((x) => {
        let normalizedColor = GreyModeColor;

        if (!greyMode) {
          // r,g,b: 0-255, a: 0-1
          const rgba = new TinyColor(x.color).toRgb();
          // color here needs to be a normalized one
          normalizedColor = [rgba.r / 255, rgba.g / 255, rgba.b / 255, rgba.a];
        }

        return { ...x, color: normalizedColor };
      });

    return shown;
  }, [greyMode, mergedChannels]);
}

export function clampToBBox(geometry: Geometry, maxX: number, maxY: number) {
  const minPoint = [0, 0];
  const maxPoint = [maxX, maxY];

  coordEach(geometry, (coord) => {
    coord.forEach((v, i) => {
      coord[i] = Math.min(Math.max(minPoint[i], Math.round(v)), maxPoint[i]);
    });
  });
}

export interface BBox {
  x: number;
  y: number;
  w: number;
  h: number;
}

export function getBBox(drawFeatures: FeatureCollection) {
  if (drawFeatures.features.length > 0) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const bb = bbox(drawFeatures as any);
    const x = bb[0];
    const y = bb[1];
    const w = bb[2] - bb[0];
    const h = bb[3] - bb[1];
    return { x, y, w, h } as BBox;
  }
}

export function selectCurrentInfoPannel(s: AppState) {
  return s.image.currentInfoPannel;
}

export function selectCurrentChannelId(s: AppState) {
  return s.image.currentChannelId;
}

export function selectSetScaleEvent(s: AppState) {
  return s.image.setScaleEvent;
}

export function selectScaleForShow(s: AppState) {
  return s.image.scaleForShow;
}

export function selectEnabledTools(s: AppState) {
  return s.image.enabledTools;
}

export function selectGreyMode(s: AppState) {
  return s.image.greyMode;
}

export function selectShownChannelIds(s: AppState) {
  return s.image.shownChannelIds;
}

export function selectGamma(s: AppState) {
  return s.image.gamma;
}

export function selectMagFilter(s: AppState) {
  return s.image.magFilter;
}

export function selectCurrentMenu(s: AppState) {
  return s.image.currentMenu;
}

export function selectDrawFeatures(s: AppState) {
  return s.image.drawFeatures;
}

export const selectDrawFeaturesBBox = createSelector(
  selectDrawFeatures,
  (drawFeatures) => {
    return getBBox(drawFeatures);
  }
);

export const EmptyFeatureCollection: FeatureCollection = {
  type: "FeatureCollection",
  features: [],
};

const initialState: State = {
  shownChannelIds: [],
  channelViews: {},
  greyMode: false,
  enabledTools: ["scale-bar", "mini-map"],
  gamma: 1,
  currentMenu: "image-operation",
  drawFeatures: EmptyFeatureCollection,
  currentInfoPannel: "channel",
  magFilter: "nearest",
};

export const imageSlice = createSlice({
  name: "image",
  initialState: initialState,
  reducers: {
    triggerSetScaleEvent: (
      state,
      action: PayloadAction<SetScaleEvent | undefined>
    ) => {
      state.setScaleEvent = action.payload;
    },
    setScaleForShow: (state, action: PayloadAction<number | undefined>) => {
      state.scaleForShow = action.payload;
      state.setScaleEvent = undefined;
    },
    setShownChannelIds: (state, action: PayloadAction<number[]>) => {
      state.shownChannelIds = action.payload;
    },
    setChannelView: (state, action: PayloadAction<ChannelView>) => {
      state.channelViews[action.payload.id] = {
        ...state.channelViews[action.payload.id],
        ...action.payload,
      };
    },
    setGreyMode: (state, action: PayloadAction<boolean>) => {
      state.greyMode = action.payload;
    },
    setGamma: (state, action: PayloadAction<number>) => {
      state.gamma = action.payload;
    },
    setMagFilter: (state, action: PayloadAction<MagFilter>) => {
      state.magFilter = action.payload;
    },
    enableTool: (state, action: PayloadAction<ToolId>) => {
      const tool = action.payload;
      if (!state.enabledTools.includes(tool)) {
        state.enabledTools.push(tool);
      }
      if (tool == "draw-rect") {
        state.currentInfoPannel = "operation";
        state.enabledTools = state.enabledTools.filter(
          (x) => x !== "select-draw"
        );
      }

      if (tool == "select-draw") {
        state.currentInfoPannel = "operation";
        state.enabledTools = state.enabledTools.filter(
          (x) => x !== "draw-rect"
        );
      }
    },
    disableTool: (state, action: PayloadAction<ToolId>) => {
      const tool = action.payload;
      if (state.enabledTools.includes(tool)) {
        state.enabledTools = state.enabledTools.filter((x) => x !== tool);
      }
    },
    setCurrentMenu: (state, action: PayloadAction<MenuType>) => {
      state.currentMenu = action.payload;
    },
    setCurrentChannelId: (state, action: PayloadAction<number | undefined>) => {
      state.currentChannelId = action.payload;
    },
    resetToInitialState: (state, action: PayloadAction<void>) => {
      return initialState;
    },
    setDrawFeatures: (state, action: PayloadAction<FeatureCollection>) => {
      state.drawFeatures = action.payload;
    },
    setCurrentInfoPannel: (state, action: PayloadAction<InfoPannelType>) => {
      state.currentInfoPannel = action.payload;
    },
    clearDrawFeatures: (state, action: PayloadAction<void>) => {
      state.drawFeatures = EmptyFeatureCollection;
    },
  },
});
