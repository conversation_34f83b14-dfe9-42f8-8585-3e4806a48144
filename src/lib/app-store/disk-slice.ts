import { createAppSlice, getAppState } from "@/lib/app-store/async-thunk";
import { diverseApi } from "@/lib/apis/diverse/api";
import { type TreeDataNode } from "antd";
import path from "path";
import { type ListDiskResItem } from "@/app/api/disk/route";
import { useAppDispatch } from "@/lib/app-store/store";
import { useCallback } from "react";
import { getParentFolderDiskPath } from "@/lib/domain/disk/svc";
import { useHandleApiErrorDefault } from "@/lib/utils";

type ObjectInfo = ListDiskResItem & {
  isNewDir?: boolean;
};

export type DiskTreeId = "viewer" | "transfer" | "folder-selector";

export const DISK_ROOT_PATH = "/";
export const DISK_ROOT_NAME = "云盘";
export const DISK_NEW_DIR_PATH = "/new-dir";

type UpdateExpandedKeysPayload = {
  treeId: DiskTreeId;
  expandedKeys: string[];
};

type SetRenamingNodeKeyPayload = {
  treeId: DiskTreeId;
  renamingNodeKey?: string;
};

type SetTreeDataPayload = {
  treeId: DiskTreeId;
  treeData: DiskTreeNode[];
};

type SetActiveNodePayload = {
  treeId: DiskTreeId;
  activeNode?: DiskTreeNode;
};

export type DiskTreeNode = TreeDataNode & {
  key: string;
  children?: DiskTreeNode[];
  size?: number;
  isNewDir?: boolean;
};

type UpdateTreeDataPayload = {
  projectId: string;
  newNodePrefix?: string;
  generateUpdateKeys?: (expandedKeys: string[]) => {
    expandedKeys?: string[];
    refreshKeys?: string[];
  };
  treeId: DiskTreeId;
  isDir?: boolean;
};

type State = Record<
  DiskTreeId,
  {
    expandedKeys: string[];
    treeData: DiskTreeNode[];
    activeNode?: DiskTreeNode;
    renamingNodeKey?: string;
  }
>;

const initialState: State = {
  viewer: {
    expandedKeys: [],
    treeData: [],
  },
  transfer: {
    expandedKeys: [],
    treeData: [],
  },
  "folder-selector": {
    expandedKeys: [],
    treeData: [],
  },
};

export const diskSlice = createAppSlice({
  name: "disk",
  initialState,
  reducers: (create) => {
    return {
      updateExpandedKeys: create.reducer<UpdateExpandedKeysPayload>(
        (state, { payload: { treeId, expandedKeys } }) => {
          state[treeId].expandedKeys = expandedKeys;
        }
      ),
      setRenamingNodeKey: create.reducer<SetRenamingNodeKeyPayload>(
        (state, { payload: { treeId, renamingNodeKey } }) => {
          state[treeId].renamingNodeKey = renamingNodeKey;
        }
      ),
      setTreeData: create.reducer<SetTreeDataPayload>(
        (state, { payload: { treeId, treeData } }) => {
          state[treeId].treeData = treeData;
        }
      ),
      setActiveNode: create.reducer<SetActiveNodePayload>(
        (state, { payload: { treeId, activeNode } }) => {
          state[treeId].activeNode = activeNode;
        }
      ),
      updateTreeData: create.asyncThunk<void, UpdateTreeDataPayload>(
        async (
          { projectId, newNodePrefix, generateUpdateKeys, treeId, isDir },
          api
        ) => {
          const expandedKeys = diskSlice.selectors.selectExpandedKeys(
            getAppState(api),
            treeId
          );

          const updateKeys = generateUpdateKeys?.(expandedKeys) || {};

          const newExpandedKeys = Array.from(
            new Set(updateKeys.expandedKeys ?? expandedKeys)
          );

          const newRefreshKeys = Array.from(
            new Set(updateKeys.refreshKeys ?? [])
          );

          const prefixKeysWithRoot = [...newExpandedKeys, DISK_ROOT_PATH];

          try {
            const prefixDataList = await Promise.all(
              prefixKeysWithRoot.map(async (prefix) => {
                const forceRefetch = newRefreshKeys.includes(prefix);
                const prefixData = await api
                  .dispatch(
                    diverseApi.endpoints.listDisk.initiate(
                      { projectId, prefix, isDir },
                      { forceRefetch }
                    )
                  )
                  .unwrap();
                return { prefix, prefixData };
              })
            );

            const prefixDataMap = prefixDataList.reduce(
              (acc, { prefix, prefixData }) => {
                if (newNodePrefix && prefix === newNodePrefix) {
                  acc[prefix] = [
                    {
                      diskPath: path.join(prefix, DISK_NEW_DIR_PATH),
                      isDir: true,
                      isNewDir: true,
                    },
                    ...prefixData,
                  ];
                } else {
                  acc[prefix] = prefixData;
                }
                return acc;
              },
              {} as Record<string, ObjectInfo[]>
            );

            const treeNode = buildTreeNode(prefixDataMap);
            api.dispatch(
              diskSlice.actions.setTreeData({
                treeId,
                treeData: [treeNode],
              })
            );
            api.dispatch(
              diskSlice.actions.updateExpandedKeys({
                treeId,
                expandedKeys: newExpandedKeys,
              })
            );
          } catch (error) {
            return api.rejectWithValue(error);
          }
        }
      ),
    };
  },
  selectors: {
    selectExpandedKeys: (s, treeId: DiskTreeId) => s[treeId].expandedKeys,
    selectTreeData: (s, treeId: DiskTreeId) => s[treeId].treeData,
    selectActiveNode: (s, treeId: DiskTreeId) => s[treeId].activeNode,
    selectRenamingNodeKey: (s, treeId: DiskTreeId) => s[treeId].renamingNodeKey,
  },
});

function buildTreeNode(
  prefixData: Record<string, ObjectInfo[]>,
  current: ObjectInfo = { diskPath: DISK_ROOT_PATH, isDir: true }
) {
  const dataNode: DiskTreeNode = {
    key: current.diskPath,
    isLeaf: !current.isDir,
    title: getNameFromDiskPath(current.diskPath),
  };
  if (current.isDir) {
    if (current.isNewDir) {
      dataNode.isNewDir = true;
      dataNode.children = [];
    } else {
      dataNode.children =
        prefixData[current.diskPath]?.map((item) =>
          buildTreeNode(prefixData, item)
        ) ?? [];
    }
  } else {
    dataNode.size = current.size;
  }
  return dataNode;
}

export function useUpdateTreeData() {
  const dispatch = useAppDispatch();
  const handleApiErrorDefault = useHandleApiErrorDefault();

  const updateTreeData = useCallback(
    (params: UpdateTreeDataPayload) =>
      dispatch(diskSlice.actions.updateTreeData(params))
        .unwrap()
        .catch(handleApiErrorDefault),
    [dispatch, handleApiErrorDefault]
  );
  return updateTreeData;
}

export function getParentDiskPaths(diskPath: string) {
  const dirs = [];
  let cur = path.normalize(diskPath);
  if (cur.endsWith("/")) {
    dirs.push(cur);
  }
  while (cur !== DISK_ROOT_PATH) {
    cur = getParentFolderDiskPath(cur);
    dirs.push(cur);
  }
  return dirs;
}

export function getCurrentFolderDiskPath(diskPath: string) {
  if (diskPath.endsWith("/")) {
    return diskPath;
  } else {
    return getParentFolderDiskPath(diskPath);
  }
}

export function getOriginNameFromDiskPath(diskPath: string) {
  const baseName = path.basename(diskPath);
  if (diskPath.endsWith("/")) {
    return path.join(baseName, "/");
  } else {
    return baseName;
  }
}

export function isRootPath(diskPath: string) {
  return diskPath === DISK_ROOT_PATH;
}

export function getNameFromDiskPath(diskPath: string) {
  return isRootPath(diskPath) ? DISK_ROOT_NAME : path.basename(diskPath);
}

if (import.meta.vitest) {
  const { describe, it, expect } = import.meta.vitest;

  describe("buildTreeData", () => {
    it("should build tree data", () => {
      const prefixData = {
        "/": [
          {
            diskPath: "/file1",
            isDir: false,
            size: 100,
          },
          {
            diskPath: "/file2",
            isDir: false,
            size: 100,
          },
          {
            diskPath: "/dir1/",
            isDir: true,
            size: 100,
          },
          {
            diskPath: "/dir2/",
            isDir: true,
          },
        ],
        "/dir1/": [
          {
            diskPath: "/dir1/sd1/",
            isDir: true,
          },
          {
            diskPath: "/dir1/sd2",
            isDir: false,
          },
          {
            diskPath: "/dir1/sd3.txt",
            isDir: false,
            size: 100,
          },
        ],
      };

      const treeData = buildTreeNode(prefixData, {
        diskPath: "/",
        isDir: true,
      });

      expect(treeData).toMatchSnapshot();
    });
  });

  describe("getParentDiskPaths", () => {
    it("should get parent disk paths", () => {
      expect(getParentDiskPaths("/f4/f77/f8")).toEqual([
        "/f4/f77/",
        "/f4/",
        "/",
      ]);
      expect(getParentDiskPaths("/f4/f77/f8/")).toEqual([
        "/f4/f77/f8/",
        "/f4/f77/",
        "/f4/",
        "/",
      ]);
    });
  });

  describe("getOriginNameFromDiskPath", () => {
    it("should get name with /", () => {
      expect(getOriginNameFromDiskPath("test/f77/f6/")).toBe("f6/");
    });
    it("should get name without /", () => {
      expect(getOriginNameFromDiskPath("test/f77/f6")).toBe("f6");
    });
  });
}
