import { type SubItemsRes } from "@/app/api/item/subs/route";
import { diverseApi } from "@/lib/apis/diverse/api";
import { createAppSlice, getAppState } from "@/lib/app-store/async-thunk";
import { useAppDispatch } from "@/lib/app-store/store";
import { ItemTypeFlag } from "@/lib/domain/item/svc";
import { useHandleApiErrorDefault } from "@/lib/utils";
import type { TreeDataNode } from "antd";
import path from "path";
import { useCallback } from "react";

export type ItemTreeId = "viewer" | "transfer";

export const ITEM_NEW_FOLDER_ID = "new-folder";
export const ITEM_NEW_FOLDER_TITLE = "新数据源";
export const ITEM_ROOT_NAME = "数据";
export const ITEM_PATH_SEPARATOR = "/";
export const ITEM_ROOT_PATH = "/";

export type ItemTreeNode = TreeDataNode & {
  key: string;
  name: string;
  children?: ItemTreeNode[];
  size?: number;
  isNewFolder?: boolean;
  pid?: string;
  namePath?: string;
  pids?: string[];
};

type ItemInfo = (
  | SubItemsRes[number]
  | {
      isNewFolder: true;
      id: string;
    }
) & {
  pid: string;
};

type State = Record<
  ItemTreeId,
  {
    expandedKeys: string[];
    treeData: ItemTreeNode[];
    activeNode?: ItemTreeNode;
    renamingNodeKey?: string;
  }
>;

const initialState: State = {
  viewer: {
    expandedKeys: [],
    treeData: [],
  },
  transfer: {
    expandedKeys: [],
    treeData: [],
  },
};

type UpdateExpandedKeysPayload = {
  treeId: ItemTreeId;
  expandedKeys: string[];
};

type UpdateTreeDataPayload = {
  projectId: string;
  newNodePid?: string;
  generateUpdateKeys?: (expandedKeys: string[]) => {
    expandedKeys?: string[];
    refreshKeys?: string[];
  };
  treeId: ItemTreeId;
};

type SetRenamingNodeKeyPayload = {
  treeId: ItemTreeId;
  renamingNodeKey?: string;
};

type SetTreeDataPayload = {
  treeId: ItemTreeId;
  treeData: ItemTreeNode[];
};

type SetActiveNodePayload = {
  treeId: ItemTreeId;
  activeNode?: ItemTreeNode;
};

export const itemSlice = createAppSlice({
  name: "item",
  initialState,
  reducers: (create) => {
    return {
      updateExpandedKeys: create.reducer<UpdateExpandedKeysPayload>(
        (state, { payload: { treeId, expandedKeys } }) => {
          state[treeId].expandedKeys = expandedKeys;
        }
      ),
      setRenamingNodeKey: create.reducer<SetRenamingNodeKeyPayload>(
        (state, { payload: { treeId, renamingNodeKey } }) => {
          state[treeId].renamingNodeKey = renamingNodeKey;
        }
      ),
      setTreeData: create.reducer<SetTreeDataPayload>(
        (state, { payload: { treeId, treeData } }) => {
          state[treeId].treeData = treeData;
        }
      ),
      setActiveNode: create.reducer<SetActiveNodePayload>(
        (state, { payload: { treeId, activeNode } }) => {
          state[treeId].activeNode = activeNode;
        }
      ),
      updateTreeData: create.asyncThunk<void, UpdateTreeDataPayload>(
        async ({ projectId, newNodePid, generateUpdateKeys, treeId }, api) => {
          const expandedKeys = itemSlice.selectors.selectExpandedKeys(
            getAppState(api),
            treeId
          );

          const updateKeys = generateUpdateKeys?.(expandedKeys) || {};

          const newExpandedKeys = Array.from(
            new Set(updateKeys.expandedKeys ?? expandedKeys)
          );

          const newRefreshKeys = Array.from(
            new Set(updateKeys.refreshKeys ?? [])
          );

          const paramsArray: {
            pid?: string;
            projectId: string;
          }[] = [
            ...newExpandedKeys.map((key) => ({
              pid: key === projectId ? undefined : key,
              projectId,
            })),
            { projectId },
          ];

          try {
            const pidDataList = await Promise.all(
              paramsArray.map(async ({ pid, projectId }) => {
                const parentId = pid || projectId;
                const forceRefetch = newRefreshKeys.includes(parentId);
                const subData = await api
                  .dispatch(
                    diverseApi.endpoints.getSubItems.initiate(
                      { pid, prjId: projectId },
                      { forceRefetch }
                    )
                  )
                  .unwrap();
                return { pid: parentId, subData };
              })
            );

            const pidDataMap = pidDataList.reduce((acc, { pid, subData }) => {
              const prefixDataWithPid = subData.map((item) => ({
                ...item,
                pid,
              }));
              if (newNodePid && pid === newNodePid) {
                acc[pid] = [
                  {
                    pid,
                    isNewFolder: true,
                    id: ITEM_NEW_FOLDER_ID,
                  },
                  ...prefixDataWithPid,
                ];
              } else {
                acc[pid] = prefixDataWithPid;
              }
              return acc;
            }, {} as Record<string, ItemInfo[]>);

            const treeNode = buildTreeNode(pidDataMap, projectId);

            api.dispatch(
              itemSlice.actions.setTreeData({
                treeId,
                treeData: [treeNode],
              })
            );
            api.dispatch(
              itemSlice.actions.updateExpandedKeys({
                treeId,
                expandedKeys: newExpandedKeys,
              })
            );
          } catch (error) {
            return api.rejectWithValue(error);
          }
        }
      ),
    };
  },
  selectors: {
    selectExpandedKeys: (s, treeId: ItemTreeId) => s[treeId].expandedKeys,
    selectTreeData: (s, treeId: ItemTreeId) => s[treeId].treeData,
    selectActiveNode: (s, treeId: ItemTreeId) => s[treeId].activeNode,
    selectRenamingNodeKey: (s, treeId: ItemTreeId) => s[treeId].renamingNodeKey,
  },
});

export function useUpdateTreeData() {
  const dispatch = useAppDispatch();
  const handleApiErrorDefault = useHandleApiErrorDefault();

  const updateTreeData = useCallback(
    (params: UpdateTreeDataPayload) =>
      dispatch(itemSlice.actions.updateTreeData(params))
        .unwrap()
        .catch(handleApiErrorDefault),
    [dispatch, handleApiErrorDefault]
  );
  return updateTreeData;
}

function buildTreeNode(
  pidDataMap: Record<string, ItemInfo[]>,
  projectId: string,
  {
    parentNamePath,
    current,
    parentPids = [],
  }: {
    parentNamePath?: string;
    parentPids?: string[];
    current?: ItemInfo;
  } = {}
) {
  if (!current) {
    const dataNode: ItemTreeNode = {
      key: projectId,
      isLeaf: false,
      name: ITEM_ROOT_NAME,
      title: ITEM_ROOT_NAME,
      children:
        pidDataMap[projectId]?.map((item) =>
          buildTreeNode(pidDataMap, projectId, {
            current: item,
            parentPids: [projectId],
          })
        ) ?? [],
    };
    return dataNode;
  }

  if ("isNewFolder" in current) {
    const dataNode: ItemTreeNode = {
      key: current.id,
      isLeaf: false,
      name: "",
      children: [],
      isNewFolder: true,
      pid: current.pid,
      title: ITEM_NEW_FOLDER_TITLE,
      pids: parentPids,
    };
    return dataNode;
  }

  const isLeaf = current.type_flag === ItemTypeFlag.File;
  const namePath = getCurrentNamePath(current.name, parentNamePath);
  const children = isLeaf
    ? []
    : pidDataMap[current.id]?.map(
        (item) =>
          buildTreeNode(pidDataMap, projectId, {
            parentNamePath: namePath,
            current: item,
            parentPids: [...parentPids, current.id],
          }) ?? []
      );

  const dataNode: ItemTreeNode = {
    key: current.id,
    isLeaf,
    name: current.name,
    title: current.name,
    size: current.size ?? undefined,
    children,
    pid: current.pid,
    namePath,
    pids: parentPids,
  };

  return dataNode;
}

export function getCurrentNamePath(name: string, parentNamePath?: string) {
  return path.normalize(
    path.join(parentNamePath ?? ITEM_PATH_SEPARATOR, ITEM_PATH_SEPARATOR, name)
  );
}

if (import.meta.vitest) {
  const { describe, it, expect } = import.meta.vitest;
  describe("buildTreeData", () => {
    it("should build tree data correctly", () => {
      const prefixData: Record<string, ItemInfo[]> = {
        prjId: [
          {
            id: "aghah3",
            name: "肺癌9表10色",
            type_flag: 1,
            size: 325,
            pid: "prjId",
          },
          {
            id: "aghah1",
            name: "数据组",
            type_flag: 0,
            size: null,
            pid: "prjId",
          },
          {
            id: "unnni",
            name: "小鼠脑神经元St38901",
            type_flag: 0,
            size: null,
            pid: "prjId",
          },
        ],

        unnni: [
          {
            id: ITEM_NEW_FOLDER_ID,
            isNewFolder: true,
            pid: "unnni",
          },
          {
            id: "aghah5",
            name: "神经元1",
            type_flag: 0,
            size: null,
            pid: "unnni",
          },
          {
            id: "aghah6",
            name: "神经元2",
            type_flag: 0,
            size: null,
            pid: "unnni",
          },
        ],
      };

      const treeData = buildTreeNode(prefixData, "prjId");

      expect(treeData).toMatchSnapshot();
    });
  });
}
