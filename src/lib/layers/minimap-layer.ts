import { ViewportIds } from "@/app-ui/components/image/viewer/constants";
import { FetchTileFn } from "@/lib/apis/diverse/api";
import type { ImgMeta } from "@/lib/domain/img/query/svc";
import { ChannelInfo, createTileLayer } from "@/lib/layers/tile-layer";
import {
  CompositeLayer,
  LayersList,
  Viewport,
  type Color,
  type CompositeLayerProps,
  type Layer,
  type UpdateParameters,
} from "@deck.gl/core";
import { LineLayer, PolygonLayer } from "@deck.gl/layers";

type MinimapLayerProps = {
  imgMeta: ImgMeta;
  channels: ChannelInfo[];
  source: string;
  tileFetcher: FetchTileFn;
  gamma: number;
};

const defaultProps: {
  bboxFillColor: Color;
  bboxLineColor: Color;
  hvLineColor: Color;
} = {
  bboxFillColor: [255, 255, 255, 85],
  bboxLineColor: [254, 100, 100],
  hvLineColor: [254, 100, 100],
};

class MinimapLayer extends CompositeLayer<MinimapLayerProps> {
  shouldUpdateState(
    params: UpdateParameters<
      Layer<MinimapLayerProps & Required<CompositeLayerProps>>
    >
  ): boolean {
    const selfChanged = Boolean(params.changeFlags.propsChanged);
    if (selfChanged) {
      return true;
    }

    const mainViewport = this.getMainViewport();
    const viewSizeChanged =
      this.state.width != mainViewport?.width ||
      this.state.height != mainViewport?.height ||
      this.state.pixelUnprojectionMatrix !=
        mainViewport?.pixelUnprojectionMatrix;

    return viewSizeChanged;
  }

  private getMainViewport(): Viewport | undefined {
    return this.context.deck
      ?.getViewports()
      .find((x) => x.id == ViewportIds.main);
  }

  renderLayers(): LayersList {
    const imageLayer = createTileLayer(
      { ...this.props },
      {
        id: this.props.id + ":tile-layer",
      }
    );

    const mainViewport = this.getMainViewport();
    if (!mainViewport) {
      return [imageLayer];
    }

    // for next round check
    this.setState({
      width: mainViewport.width,
      height: mainViewport.height,
      pixelUnprojectionMatrix: mainViewport.pixelUnprojectionMatrix,
    });

    const [minX, minY, maxX, maxY] = mainViewport.getBounds();
    const boundingBoxOutline = new PolygonLayer({
      id: `${this.props.id}:bbox-outline`,
      data: [
        [
          [minX, minY],
          [minX, maxY],
          [maxX, maxY],
          [maxX, minY],
        ],
      ],
      getPolygon: (f) => f,
      getLineColor: defaultProps.bboxLineColor,
      getFillColor: defaultProps.bboxFillColor,
      lineWidthMinPixels: 1,
    });

    const width = this.props.imgMeta.base_x;
    const height = this.props.imgMeta.base_y;
    const minimapOutline = new PolygonLayer({
      id: `${this.props.id}:minimap-outline`,
      data: [
        [
          [0, 0],
          [width, 0],
          [width, height],
          [0, height],
        ],
      ],
      getPolygon: (f) => f,
      filled: false,
      getLineColor: [245, 222, 179],
      lineWidthMinPixels: 1,
    });

    const [cx, cy] = mainViewport.center;
    const hvline = new LineLayer({
      id: `${this.props.id}:hvline`,
      data: [
        { sourcePosition: [0, cy], targetPosition: [width, cy] },
        { sourcePosition: [cx, 0], targetPosition: [cx, height] },
      ],
      getColor: defaultProps.hvLineColor,
    });

    const layers = [imageLayer, minimapOutline, boundingBoxOutline, hvline];

    return layers;
  }
}

MinimapLayer.layerName = "MinimapLayer";

export default MinimapLayer;
