import { FetchTileFn } from "@/lib/apis/diverse/api";
import { ImgMeta } from "@/lib/domain/img/query/svc";
import IntensityLayer, {
  type MagFilter,
  type TileImage,
} from "@/lib/layers/intensity-layer";
import type { LayersList } from "@deck.gl/core";
import {
  TileLayer,
  type TileLayerProps,
  type _Tile2DHeader,
  type _TileLoadProps,
} from "@deck.gl/geo-layers";

export type TileData = { channelId: number; image: Promise<TileImage> }[];

async function getTileData(
  fetchTileFn: FetchTileFn,
  tile: _TileLoadProps,
  source: string,
  channelIds: number[],
  version: string
): Promise<TileData> {
  const index = tile.index;
  const images = channelIds.map((channelId) => {
    const req = {
      imgId: source,
      x: index.x,
      y: index.y,
      z: index.z,
      channel_id: channelId,
      version: version,
    };

    const image = fetchTileFn(req, { signal: tile.signal });

    return {
      channelId,
      image,
    };
  });

  if (images.length > 0) {
    await Promise.any(images.map((x) => x.image));
  }

  return images;
}

function renderSubLayers(
  ops: Options,
  {
    id,
    tile,
  }: {
    id: string;
    tile: _Tile2DHeader<TileData>;
  }
) {
  const layers: LayersList = [];

  const [[left, top], [right, bottom]] = tile.boundingBox;
  const bounds = [
    left,
    Math.min(ops.imgMeta.base_y, bottom),
    Math.min(ops.imgMeta.base_x, right),
    top,
  ];

  for (const channelByTile of tile.content ?? []) {
    const channelId = channelByTile.channelId;
    const channelInfo = ops.channels.find((x) => x.id === channelId)!;
    if (channelInfo) {
      const layer = new IntensityLayer({
        id:
          id +
          ":intensity-channel:" +
          channelId +
          ":" +
          ops.imgMeta.data_version,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any -- we use async props for this layer
        image: channelByTile.image as any,
        dtype: ops.imgMeta.dtype,
        contrastLimits: [channelInfo.min, channelInfo.max],
        color: channelInfo.color,
        pickable: ops.pickable,
        bounds: bounds,
        name: channelInfo.name,
        parameters: {
          blendColorSrcFactor: "src-alpha",
          blendColorDstFactor: "dst-alpha",
          blendColorOperation: "add",
        },
        gamma: ops.gamma,
        magFilter: ops.magFilter,
      });
      layers.push(layer);
    }
  }

  // layers.push(
  //   new PathLayer({
  //     id: id + ":path-layer",
  //     data: [
  //       {
  //         path: [
  //           [left, bottom],
  //           [right, bottom],
  //           [right, top],
  //           [left, top],
  //           [left, bottom],
  //         ],
  //       },
  //     ],
  //     widthMaxPixels: 1,
  //     getColor: [0, 255, 0, 255],
  //   })
  // );

  return layers;
}

export interface ChannelInfo {
  id: number;
  name: string;
  min: number;
  max: number;
  color: [number, number, number, number];
}

type Options = {
  imgMeta: ImgMeta;
  channels: ChannelInfo[];
  source: string;
  pickable?: boolean;
  tileFetcher: FetchTileFn;
  gamma: number;
  magFilter?: MagFilter;
};

export function createTileLayer(
  ops: Options,
  innerProps: Partial<TileLayerProps<TileData>>
) {
  const { max_level, min_level, base_x, base_y, tile_size, data_version } =
    ops.imgMeta;

  const minZoom = min_level - max_level;
  const extent = [0, 0, base_x, base_y];
  const channelIds = ops.channels.map((x) => x.id);
  const getTileDataTriggerKey = [...channelIds]
    .sort((a, b) => a - b)
    .join()
    .concat("-", data_version);
  const layer = new TileLayer<
    TileData,
    { channels: ChannelInfo[]; gamma: number; magFilter: MagFilter }
  >({
    refinementStrategy: "no-overlap",
    maxRequests: 25,
    minZoom: minZoom,
    maxZoom: 0,
    extent: extent,
    tileSize: tile_size,
    renderSubLayers: (x) => renderSubLayers(ops, { ...x }),
    getTileData: (x) =>
      getTileData(ops.tileFetcher, x, ops.source, channelIds, data_version),
    updateTriggers: {
      getTileData: [getTileDataTriggerKey],
    },
    channels: ops.channels,
    gamma: ops.gamma,
    magFilter: ops.magFilter,
    ...innerProps,
  });

  return layer;
}
