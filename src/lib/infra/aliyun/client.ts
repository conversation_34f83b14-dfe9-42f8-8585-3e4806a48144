import { type AppConfig } from "@/lib/app-config";
import { DomainError } from "@/lib/domain/common/error";
import { Client } from "aliyun-api-gateway";

export type IdVerifyResponse = {
  error_code: number;
  reason: string;
  result: {
    realname: string;
    idcard: string;
    isok: boolean;
    IdCardInfor: {
      province: string;
      city: string;
      district: string;
      area: string;
      sex: string;
      birthday: string;
    };
  };
};

export class AliyunClient {
  private client: InstanceType<typeof Client>;
  constructor(private appConfig: AppConfig) {
    this.client = new Client(
      this.appConfig.ALI_AUTH_APP_KEY,
      this.appConfig.ALI_AUTH_APP_SECRET
    );
  }

  async identityVerify(
    realName: string,
    cardNo: string
  ): Promise<IdVerifyResponse["result"]> {
    try {
      const res = await this.client.get(
        "https://zidv2.market.alicloudapi.com/idcard/VerifyIdcardv2",
        {
          headers: { accept: "application/json" },
          query: { cardNo, realName },
        }
      );

      const data = ensureStatusWithSucceed(JSON.parse(res));
      return data;
    } catch (err) {
      throw err instanceof DomainError
        ? err
        : new DomainError("id_verify_error", err);
    }
  }
}

function ensureStatusWithSucceed(res: IdVerifyResponse) {
  if (res.error_code === 0 && res.result.isok === true) {
    return res.result;
  }

  throw new DomainError("id_verify_error", res);
}

if (import.meta.vitest) {
  const { describe, it, expect } = await import("vitest");

  describe("identityVerify", () => {
    const client = new AliyunClient({
      ALI_AUTH_APP_KEY: "test",
      ALI_AUTH_APP_SECRET: "test",
    } as AppConfig);

    it("should return result when succeed", async () => {
      const result = await client.identityVerify("张三", "11010519491231001X");
      expect(result).toEqual({
        realname: "test**",
        idcard: "513123***********1",
        isok: true,
        IdCardInfor: {
          province: "xx省",
          city: "xx市",
          district: "xx县",
          area: "xxxx地区",
          sex: "男",
          birthday: "2001-4-1",
        },
      });
    });

    it("should throw DomainError when verification fails", async () => {
      const error = await client
        .identityVerify("错误姓名", "11010519491231001X")
        .catch((err) => err);

      expect(error).toBeInstanceOf(DomainError);
      expect(error.error_code).toBe("id_verify_error");
    });
  });
}
