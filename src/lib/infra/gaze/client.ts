import type { AppConfig } from "@/lib/app-config";
import wretch, { type Wretch } from "wretch";
import querystring, { QueryStringAddon } from "wretch/addons/queryString";
import z from "zod";

export type SaveImgMetaReq = {
  id: string;
  channels?:
    | {
        id: number;
        name?: string | null;
        color?: string | null;
        view_min?: number | null;
        view_max?: number | null;
        view_shown?: boolean | null;
      }[]
    | null;
  phys_x?: number | null;
  phys_x_unit?: string | null;
  phys_y?: number | null;
  phys_y_unit?: string | null;
  view_gamma?: number | null;
};

export type GetImgTileReq = {
  id: string;
  x: number;
  y: number;
  z: number;
  channel_id: number;
  version: string;
};

type GetMinMaxByQuantileReq = {
  id: string;
  channel_id: number;
  min_quantile?: number | null;
  max_quantile?: number | null;
  in_unique: boolean;
};

const GetImgInfoResSchema = z.object({
  id: z.string(),
  img_meta: z.object({
    tile_size: z.int(),
    base_x: z.int(),
    base_y: z.int(),
    min_level: z.int(),
    max_level: z.int(),
    dtype: z.string(),
    channels: z.array(
      z.object({
        id: z.int(),
        name: z.string(),
        min: z.number(),
        max: z.number(),
        color: z.string().nullish(),
        view_max: z.number().nullish(),
        view_min: z.number().nullish(),
        view_shown: z.boolean().nullish(),
      })
    ),
    axes: z.string(),
    shape: z.array(z.int()),
    phys_x: z.number().nullish(),
    phys_x_unit: z.string().nullish(),
    phys_y: z.number().nullish(),
    phys_y_unit: z.string().nullish(),
    phys_z: z.number().nullish(),
    phys_z_unit: z.string().nullish(),
    data_version: z.string(),
    view_gamma: z.number().nullish(),
  }),
});

const GetMinMaxByQuantileResSchema = z.object({
  min: z.number().nullish(),
  max: z.number().nullish(),
});

const GetHistogramResSchema = z.object({
  channels: z.array(
    z.object({
      id: z.number(),
      hist: z.array(z.number()),
      bins: z.array(z.number()),
    })
  ),
});

export class GazeClient {
  private readonly remoteApi: Wretch<QueryStringAddon> & QueryStringAddon;
  constructor(appConfig: AppConfig) {
    this.remoteApi = wretch(appConfig.GAZE_API_URL).addon(querystring);
  }

  async getImgInfo({ tdbGroupUri }: { tdbGroupUri: string }) {
    const remoteRes = await this.remoteApi
      .query({ id: tdbGroupUri })
      .get("/api/img/meta")
      .json();
    const parsedRes = GetImgInfoResSchema.parse(remoteRes);
    return parsedRes;
  }

  async getImgOriginalMeta({ tdbGroupUri }: { tdbGroupUri: string }) {
    const remoteRes = await this.remoteApi
      .query({ id: tdbGroupUri })
      .get("/api/img/original-meta")
      .res();
    return remoteRes;
  }

  async saveImgMeta(options: SaveImgMetaReq) {
    await this.remoteApi.url("/api/img/meta").put(options).res();
  }

  getImgTile(options: GetImgTileReq) {
    return this.remoteApi.query(options).get("/api/img/tile").res();
  }

  async getMinMaxByQuantile(options: GetMinMaxByQuantileReq) {
    const remoteRes = await this.remoteApi
      .query(options)
      .get("/api/img/min-max-by-quantile")
      .json();
    const parsedRes = GetMinMaxByQuantileResSchema.parse(remoteRes);
    return parsedRes;
  }

  async getHistogram({ tdbGroupUri }: { tdbGroupUri: string }) {
    const remoteRes = await this.remoteApi
      .query({ id: tdbGroupUri })
      .get("/api/img/histogram")
      .json();
    const parsedRes = GetHistogramResSchema.parse(remoteRes);
    return parsedRes;
  }
}
