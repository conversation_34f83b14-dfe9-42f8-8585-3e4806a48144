import type { AppConfig } from "@/lib/app-config";
import { GazeClient } from "@/lib/infra/gaze/client";
import { server } from "@/mocks/server";
import { http, HttpResponse } from "msw";
import { describe, expect, it } from "vitest";
import { WretchError } from "wretch/resolver";
import { ZodError } from "zod";

const GAZE_API_URL = "http://localhost:8099";
const client = new GazeClient({
  GAZE_API_URL,
} as AppConfig);

describe("getImgInfo", () => {
  const imgInfoRes = {
    id: "test-item-id",
    img_meta: {
      tile_size: 256,
      base_x: 1000,
      base_y: 1000,
      min_level: 0,
      max_level: 4,
      dtype: "uint8",
      channels: [],
      axes: "YX",
      shape: [1000, 1000],
      data_version: "1",
    },
  };

  it("should return img info", async () => {
    server.use(
      http.get(`${GAZE_API_URL}/api/img/meta`, () =>
        HttpResponse.json(imgInfoRes)
      )
    );

    const res = await client.getImgInfo({
      tdbGroupUri: "test-tdb-group-uri",
    });
    expect(res).toEqual(imgInfoRes);
  });

  it("should throw error when response structure is wrong", async () => {
    server.use(
      http.get(`${GAZE_API_URL}/api/img/meta`, () =>
        HttpResponse.json({ id: "11" })
      )
    );
    await expect(
      client.getImgInfo({ tdbGroupUri: "test-tdb-group-uri" })
    ).rejects.toThrowError(ZodError);
  });
});

describe("getImgOriginalMeta", () => {
  const imgOriginalMetaRes = {
    meta: {
      OME: {
        Creator: "OME Bio-Formats 8.1.1",
      },
    },
  };

  it("should return img original data", async () => {
    server.use(
      http.get(`${GAZE_API_URL}/api/img/original-meta`, () =>
        HttpResponse.json(imgOriginalMetaRes)
      )
    );
    const res = await client.getImgOriginalMeta({
      tdbGroupUri: "test-tdb-group-uri",
    });
    expect(await res.json()).toEqual(imgOriginalMetaRes);
  });
});

describe("saveImgMeta", () => {
  it("should save img meta", async () => {
    server.use(
      http.put(`${GAZE_API_URL}/api/img/meta`, () => HttpResponse.json({}))
    );
    await expect(
      client.saveImgMeta({
        id: "test-item-id",
        channels: [],
        phys_x: 1,
        phys_x_unit: "mm",
      })
    ).resolves.not.toThrow();
  });

  it("should throw error when backend failed", async () => {
    server.use(
      http.put(
        `${GAZE_API_URL}/api/img/meta`,
        () => new HttpResponse(null, { status: 500 })
      )
    );
    await expect(
      client.saveImgMeta({
        id: "test-item-id",
      })
    ).rejects.toThrowError(WretchError);
  });
});

describe("getMinMaxByQuantile", () => {
  const getMinMaxByQuantileRes = {
    min: 12,
    max: 98,
  };

  it("should return min and max", async () => {
    server.use(
      http.get(`${GAZE_API_URL}/api/img/min-max-by-quantile`, () =>
        HttpResponse.json(getMinMaxByQuantileRes)
      )
    );

    const res = await client.getMinMaxByQuantile({
      id: "test-tdb-group-uri",
      channel_id: 12,
      in_unique: true,
    });

    expect(res).toEqual(getMinMaxByQuantileRes);
  });

  it("should throw error when response structure is wrong", async () => {
    server.use(
      http.get(`${GAZE_API_URL}/api/img/min-max-by-quantile`, () =>
        HttpResponse.json({ min: "235" })
      )
    );
    await expect(
      client.getMinMaxByQuantile({
        id: "test-tdb-group-uri",
        channel_id: 12,
        in_unique: true,
      })
    ).rejects.toThrowError(ZodError);
  });
});

describe("getHistogram", () => {
  const getHistogramRes = {
    channels: [
      {
        id: 12,
        hist: [1, 2, 3],
        bins: [1, 2, 3],
      },
    ],
  };

  it("should return histogram", async () => {
    server.use(
      http.get(`${GAZE_API_URL}/api/img/histogram`, () =>
        HttpResponse.json(getHistogramRes)
      )
    );
    const res = await client.getHistogram({
      tdbGroupUri: "test-tdb-group-uri",
    });
    expect(res).toEqual(getHistogramRes);
  });

  it("should throw error when response structure is wrong", async () => {
    server.use(
      http.get(`${GAZE_API_URL}/api/img/histogram`, () =>
        HttpResponse.json({ channels: null })
      )
    );
    await expect(
      client.getHistogram({ tdbGroupUri: "test-tdb-group-uri" })
    ).rejects.toThrowError(ZodError);
  });
});
