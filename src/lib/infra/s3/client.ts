import type { AppConfig } from "@/lib/app-config";
import {
  S3Client as AWSS3Client,
  CopyObjectCommand,
  DeleteObjectCommand,
  DeleteObjectsCommand,
  ListObjectsV2Command,
  PutBucketLifecycleConfigurationCommand,
  PutObjectCommand,
  PutObjectCommandInput,
  S3ClientConfigType,
  type LifecycleRule,
} from "@aws-sdk/client-s3";
import OSS from "ali-oss";
import { createHash } from "crypto";
export class S3Client {
  private readonly s3Client: AWSS3Client;
  private readonly ossClient: OSS;

  readonly bucket: string;

  constructor(appConfig: AppConfig) {
    this.bucket = appConfig.OSS_BUCKET;
    this.s3Client = createS3ClientWithMD5({
      region: appConfig.OSS_REGION,
      endpoint: appConfig.OSS_ENDPOINT,
      credentials: {
        accessKeyId: appConfig.OSS_ACCESS_KEY_ID,
        secretAccessKey: appConfig.OSS_SECRET_ACCESS_KEY,
      },
      requestChecksumCalculation: "WHEN_REQUIRED",
      responseChecksumValidation: "WHEN_REQUIRED",
    });

    this.ossClient = new OSS({
      region: appConfig.OSS_REGION,
      bucket: appConfig.OSS_BUCKET,
      accessKeyId: appConfig.OSS_ACCESS_KEY_ID,
      accessKeySecret: appConfig.OSS_SECRET_ACCESS_KEY,
    });
  }

  /**
   * @param prefix should end with /, like: cm3aa/xxx/
   * @see https://help.aliyun.com/zh/oss/developer-reference/list-objects-5?spm=a2c4g.11186623.help-menu-31815.d_5_2_10_6_2_2.67fa45da8kCC9n&scm=20140722.H_111389._.OR_help-T_cn~zh-V_1#section-zq7-oi5-byh
   */
  async *listInfiniteObject(
    {
      prefix,
      delimiter,
    }: {
      prefix: string;
      delimiter?: string;
    },
    options?: HttpHandlerOptions
  ) {
    let nextContinuationToken: string | undefined;

    while (true) {
      const command = new ListObjectsV2Command({
        Bucket: this.bucket,
        Prefix: prefix,
        Delimiter: delimiter,
        MaxKeys: 1000,
        ContinuationToken: nextContinuationToken,
      });
      const result = await this.s3Client.send(command, options);

      yield {
        Contents: result.Contents ?? [],
        CommonPrefixes: result.CommonPrefixes ?? [],
      };

      if (result.NextContinuationToken) {
        nextContinuationToken = result.NextContinuationToken;
      } else {
        return;
      }
    }
  }

  async createDir(key: string) {
    if (!key.endsWith("/")) {
      key = key + "/";
    }
    const command = new PutObjectCommand({
      Bucket: this.bucket,
      Key: key,
      Body: Buffer.from(""),
    });
    const result = await this.s3Client.send(command);
    return result;
  }

  async copyObject(
    sourceKey: string,
    destinationKey: string,
    forbidOverwrite: boolean = false
  ) {
    if (forbidOverwrite) {
      const result = await this.ossClient.copy(destinationKey, sourceKey, {
        headers: {
          "x-oss-forbid-overwrite": "true",
        },
      });
      return result;
    }
    const command = new CopyObjectCommand({
      Bucket: this.bucket,
      CopySource: encodeURIComponent(`${this.bucket}/${sourceKey}`),
      Key: destinationKey,
    });
    const result = await this.s3Client.send(command);

    return result;
  }

  async deleteObject(key: string) {
    const command = new DeleteObjectCommand({
      Bucket: this.bucket,
      Key: key,
    });
    const result = await this.s3Client.send(command);

    return result;
  }

  async putObject(
    key: string,
    body: PutObjectCommandInput["Body"],
    contentType?: string
  ) {
    const command = new PutObjectCommand({
      Bucket: this.bucket,
      Key: key,
      Body: body,
      ContentType: contentType,
    });
    const result = await this.s3Client.send(command);

    return result;
  }

  async deleteObjects(keys: string[]) {
    const command = new DeleteObjectsCommand({
      Bucket: this.bucket,
      Delete: {
        Objects: keys.map((key) => ({ Key: key })),
      },
    });
    const res = await this.s3Client.send(command);
    return res;
  }

  async putLifecycle(rules: LifecycleRule[]) {
    const command = new PutBucketLifecycleConfigurationCommand({
      Bucket: this.bucket,
      LifecycleConfiguration: {
        Rules: rules,
      },
    });
    const res = await this.s3Client.send(command);
    return res;
  }

  async totalSizeInBytes(key: string, options?: HttpHandlerOptions) {
    let totalSizeInBytes = 0;
    for await (const { Contents } of this.listInfiniteObject(
      { prefix: key },
      options
    )) {
      for (const content of Contents) {
        totalSizeInBytes += content.Size ?? 0;
      }
    }

    return totalSizeInBytes;
  }
}

type HttpHandlerOptions = Parameters<AWSS3Client["send"]>[1];

/**
 * Creates an S3 client that uses MD5 checksums for DeleteObjects operations
 * https://github.com/aws/aws-sdk-js-v3/blob/main/supplemental-docs/MD5_FALLBACK.md
 * https://github.com/aws/aws-sdk-js-v3/issues/6920
 */
function createS3ClientWithMD5(config: S3ClientConfigType) {
  const client = new AWSS3Client(config);

  // Define the middleware function
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const md5Middleware = (next: any, context: any) => async (args: any) => {
    // Check if this is a DeleteObjects command
    const isDeleteObjects = context.commandName === "DeleteObjectsCommand";

    if (!isDeleteObjects) {
      return next(args);
    }

    const headers = args.request.headers;

    // Remove any checksum headers added by default middleware
    // This ensures our Content-MD5 is the primary integrity check
    Object.keys(headers).forEach((header) => {
      const lowerHeader = header.toLowerCase();
      if (
        lowerHeader.startsWith("x-amz-checksum-") ||
        lowerHeader.startsWith("x-amz-sdk-checksum-")
      ) {
        delete headers[header];
      }
    });

    // Add Content-MD5 header
    if (args.request.body) {
      const bodyContent = Buffer.from(args.request.body);
      headers["Content-MD5"] = createHash("md5")
        .update(bodyContent)
        .digest("base64");
    }

    return await next(args);
  };

  client.middlewareStack.add(md5Middleware, {
    step: "build",
  });

  return client;
}
