import { AppConfig } from "@/lib/app-config";
import * as rs from "@restatedev/restate-sdk";
import * as rsc from "@restatedev/restate-sdk-clients";
import { z } from "zod";

export class RestateClient {
  readonly ingress: rsc.Ingress;

  constructor(private appConfig: AppConfig) {
    this.ingress = rsc.connect({
      url: appConfig.RESTATE_API_URL,
    });
  }

  get itemSvc() {
    const c = this.ingress.serviceClient(ItemSvcName);
    const sc = this.ingress.serviceSendClient(ItemSvcName);
    return {
      client: c,
      sendClient: sc,
    };
  }

  get taskSvc() {
    const c = this.ingress.serviceClient(TaskSvcName);
    const sc = this.ingress.serviceSendClient(TaskSvcName);
    return {
      client: c,
      sendClient: sc,
    };
  }
}

export const DeleteItemReqSchema = z.object({
  itemIds: z.string().array(),
});
export const DeleteItemResSchema = z.unknown();

type RsHandler<I, O, C extends rs.Context = rs.Context> = (
  ctx: C,
  input: I
) => Promise<O>;

export type RsItemSvc = {
  deleteItems: RsHandler<
    z.infer<typeof DeleteItemReqSchema>,
    z.infer<typeof DeleteItemResSchema>
  >;
};

export const UpdateTaskStatusReqSchema = z.object({
  taskId: z.string(),
  remoteTaskId: z.string(),
});

export const UpdateTaskStatusResSchema = z.unknown();

export const ItemSvcName: rs.ServiceDefinition<"item", RsItemSvc> = {
  name: "item",
};

export const ProcessAfterTaskDoneReqSchema = z.object({
  taskId: z.string(),
});

export const ProcessAfterTaskDoneResSchema = z.unknown();

export type RsTaskSvc = {
  processAfterTaskDone: RsHandler<
    z.infer<typeof ProcessAfterTaskDoneReqSchema>,
    z.infer<typeof ProcessAfterTaskDoneResSchema>
  >;

  updateRemoteTaskStatus: RsHandler<
    z.infer<typeof UpdateTaskStatusReqSchema>,
    z.infer<typeof UpdateTaskStatusResSchema>
  >;
};

export const TaskSvcName: rs.ServiceDefinition<"task", RsTaskSvc> = {
  name: "task",
};
