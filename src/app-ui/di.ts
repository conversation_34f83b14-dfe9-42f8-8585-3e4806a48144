import { loadAppConfig } from "@/lib/app-config";
import { configNextAuth } from "@/lib/auth";
import { LLMSvc } from "@/lib/domain/ai/llm/svc";
import { LLMPvdSvc } from "@/lib/domain/ai/provider/svc";
import { ATPSvc } from "@/lib/domain/atp/svc";
import { DiskSvc } from "@/lib/domain/disk/svc";
import { EncryptionSvc } from "@/lib/domain/encryption/svc";
import { ImgQuerySvc } from "@/lib/domain/img/query/svc";
import { ImgSvc } from "@/lib/domain/img/svc";
import { ItemQuerySvc } from "@/lib/domain/item/query/svc";
import { ItemSvc } from "@/lib/domain/item/svc";
import { ProjectQuerySvc } from "@/lib/domain/project/query/svc";
import { ProjectSvc } from "@/lib/domain/project/svc";
import { SessionSvc } from "@/lib/domain/session/svc";
import { TaskQuerySvc } from "@/lib/domain/task/query/svc";
import { TaskSvc } from "@/lib/domain/task/svc";
import { ThreadSvc } from "@/lib/domain/thread/svc";
import { TusSvc } from "@/lib/domain/tus/svc";
import { UserSvc } from "@/lib/domain/user/svc";
import { AliyunClient } from "@/lib/infra/aliyun/client";
import { AuthingClient } from "@/lib/infra/authing/client";
import { GazeClient } from "@/lib/infra/gaze/client";
import { RestateClient } from "@/lib/infra/restate/client";
import { S3Client } from "@/lib/infra/s3/client";
import { WeChatPayClient } from "@/lib/infra/wechat/client";
import { cacheLazy } from "@/lib/lazy";
import { getPrisma as _getPrisma } from "@/lib/prisma";
import { QuerySvc } from "@/lib/query/svc";
import { cache } from "react";

const getPrisma = _getPrisma;

const getAppConfig = cacheLazy(loadAppConfig);

const notProduction = process.env.NODE_ENV != "production";

const getATPSvc = cacheLazy(async () => {
  const wechat_pay = await getWeChatPayClient();
  return new ATPSvc(wechat_pay);
});

const getUserSvc = cacheLazy(async () => {
  const aliyunClient = await getAliyunClient();
  const encryptionSvc = await getEncryptionSvc();
  return new UserSvc(aliyunClient, encryptionSvc);
});

const getThreadSvc = cacheLazy(async () => {
  const prisma = await getPrisma();
  const atpSvc = await getATPSvc();
  return new ThreadSvc(atpSvc, prisma);
});

const getEncryptionSvc = cacheLazy(async () => {
  const appConfig = await getAppConfig();
  return new EncryptionSvc(appConfig);
});

const getLLMPvdSvc = cacheLazy(async () => {
  const appConfig = await getAppConfig();
  return new LLMPvdSvc(appConfig);
});

const getLLMSvc = cacheLazy(async () => {
  const threadSvc = await getThreadSvc();
  const atpSvc = await getATPSvc();
  const llmPvdSvc = await getLLMPvdSvc();
  return new LLMSvc(threadSvc, atpSvc, llmPvdSvc);
});

const getProjectSvc = cacheLazy(async () => {
  return new ProjectSvc();
});

const getProjectQuerySvc = cacheLazy(async () => {
  const prisma = await getPrisma();
  return new ProjectQuerySvc(prisma);
});

const getWeChatPayClient = cacheLazy(async () => {
  const appConfig = await getAppConfig();
  const wechatPayClient = new WeChatPayClient(appConfig);

  if (notProduction) {
    wechatPayClient.generatePayQRCode = () => {
      return Promise.resolve("test code url");
    };
    wechatPayClient.queryOrderPayInfo = () => {
      return Promise.resolve({
        success_time: "2000-01-01T00:00:00+08:00",
        transaction_id: "test-id",
        trade_state: "SUCCESS",
      });
    };
  }

  return wechatPayClient;
});

const getGazeClient = cacheLazy(async () => {
  const appConfig = await getAppConfig();
  return new GazeClient(appConfig);
});

const getAliyunClient = cacheLazy(async () => {
  const appConfig = await getAppConfig();
  const client = new AliyunClient(appConfig);
  return client;
});

const getAuthingClient = cacheLazy(async () => {
  const appConfig = await getAppConfig();
  return new AuthingClient(appConfig);
});

const getS3Client = cacheLazy(async () => {
  const appConfig = await getAppConfig();
  return new S3Client(appConfig);
});

const getTusSvc = cacheLazy(async () => {
  const s3Client = await getS3Client();
  const appConfig = await getAppConfig();
  return new TusSvc(appConfig, s3Client);
});

const getDiskSvc = cacheLazy(async () => {
  const s3Client = await getS3Client();
  return new DiskSvc(s3Client);
});

const getNextAuth = cacheLazy(async () => {
  const appConfig = await getAppConfig();
  const prisma = await getPrisma();
  const authing = await getAuthingClient();

  return configNextAuth(appConfig, prisma, authing);
});

const getSessionSvc = cacheLazy(async () => {
  const nextAuth = await getNextAuth();
  return new SessionSvc(nextAuth);
});

const getQuerySvc = cache(async () => {
  const user = await ensureLogin();
  const prisma = await getPrisma();
  return new QuerySvc(user, prisma);
});

const ensureLogin = async (
  options: { redirect?: boolean } = { redirect: true }
) => {
  const sessionSvc = await getSessionSvc();
  const redirectPath = options.redirect ? "/login" : undefined;
  const user = await sessionSvc.ensureLogin({
    redirect: redirectPath,
  });
  return user;
};

const getRestateClient = cacheLazy(async () => {
  const appConfig = await getAppConfig();
  return new RestateClient(appConfig);
});

const getItemSvc = cacheLazy(async () => {
  const prisma = await getPrisma();
  const s3Client = await getS3Client();
  const restateClient = await getRestateClient();
  return new ItemSvc(prisma, s3Client, restateClient);
});

const getTaskSvc = cacheLazy(async () => {
  const itemSvc = await getItemSvc();
  const prisma = await getPrisma();
  const appConfig = await getAppConfig();
  const itemQuerySvc = await getItemQuerySvc();
  const restateClient = await getRestateClient();
  const s3Client = await getS3Client();
  const diskSvc = await getDiskSvc();
  const svc = new TaskSvc(
    itemQuerySvc,
    itemSvc,
    prisma,
    appConfig,
    restateClient,
    s3Client,
    diskSvc
  );
  return svc;
});

const getTaskQuerySvc = cacheLazy(async () => {
  const prisma = await getPrisma();
  const svc = new TaskQuerySvc(prisma);
  return svc;
});

const getImgSvc = cacheLazy(async () => {
  const prisma = await getPrisma();
  const itemQuerySvc = await getItemQuerySvc();
  const gazeClient = await getGazeClient();
  const svc = new ImgSvc(prisma, itemQuerySvc, gazeClient);
  return svc;
});

const getItemQuerySvc = cacheLazy(async () => {
  const prisma = await getPrisma();
  const s3Client = await getS3Client();
  const svc = new ItemQuerySvc(prisma, s3Client);
  return svc;
});

const getImgQuerySvc = cacheLazy(async () => {
  const prisma = await getPrisma();
  const gazeClient = await getGazeClient();
  const itemQuerySvc = await getItemQuerySvc();
  const svc = new ImgQuerySvc(prisma, gazeClient, itemQuerySvc);
  return svc;
});

export const di = {
  getUserSvc,
  getATPSvc,
  getThreadSvc,
  getPrisma,
  getAppConfig,
  getLLMSvc,
  getWeChatPayClient,
  getAuthingClient,
  getNextAuth,
  getSessionSvc,
  getQuerySvc,
  ensureLogin,
  getLLMPvdSvc,
  getProjectSvc,
  getTusSvc,
  getDiskSvc,
  getItemSvc,
  getTaskSvc,
  getImgSvc,
  getImgQuerySvc,
  getItemQuerySvc,
  getTaskQuerySvc,
  getProjectQuerySvc,
};
