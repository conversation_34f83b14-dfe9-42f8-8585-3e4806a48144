"use server";

import { authActionClient } from "@/app-ui/actions/client";
import { di } from "@/app-ui/di";
import { IrsModelId } from "@/lib/domain/ai/llm/models";
import {
  CardNoReg,
  UserNameMaxByteLength,
  UsernameReg,
} from "@/lib/domain/user/constants";
import { getByteLength } from "@/lib/utils";
import { z } from "zod";

const createOrderInputSchema = z.object({
  amountByFen: z.number().positive(),
});

const verifyIdCardInputSchema = z.object({
  realName: z.string(),
  cardNo: z.string().regex(CardNoReg),
});

const saveThreadConfigInputSchema = z.object({
  threadId: z.string(),
  config: z.object({
    modelId: z.nativeEnum(IrsModelId),
    maxTokens: z.number(),
    temperature: z.number(),
  }),
});

const updateUsernameInputSchema = z.object({
  username: z
    .string()
    .regex(UsernameReg)
    .refine((val) => getByteLength(val) <= UserNameMaxByteLength),
});

export const createOrder = authActionClient
  .inputSchema(createOrderInputSchema)
  .action(async ({ parsedInput, ctx }) => {
    const operator = ctx.session;
    const prisma = await di.getPrisma();
    const atpSvc = await di.getATPSvc();
    const res = await atpSvc.createRechargeOrder(prisma, operator, parsedInput);
    return res;
  });

export const verifyIdCard = authActionClient
  .inputSchema(verifyIdCardInputSchema)
  .action(async ({ parsedInput, ctx }) => {
    const operator = ctx.session;
    const prisma = await di.getPrisma();
    const userSvc = await di.getUserSvc();
    await userSvc.verifyIdCard(prisma, operator, parsedInput);
  });

export const saveThreadConfig = authActionClient
  .inputSchema(saveThreadConfigInputSchema)
  .action(async ({ parsedInput, ctx }) => {
    const operator = ctx.session;
    const prisma = await di.getPrisma();
    const threadSvc = await di.getThreadSvc();
    await threadSvc.updateThreadModelConfig(
      prisma,
      operator,
      parsedInput.threadId,
      parsedInput.config
    );
  });

export const updateUsername = authActionClient
  .inputSchema(updateUsernameInputSchema)
  .action(async ({ parsedInput, ctx }) => {
    const operator = ctx.session;
    const prisma = await di.getPrisma();
    const userSvc = await di.getUserSvc();
    await userSvc.updateUsername(prisma, operator, parsedInput.username);
  });

export const ensureATPSufficient = authActionClient.action(async ({ ctx }) => {
  const operator = ctx.session;
  const prisma = await di.getPrisma();
  const atpSvc = await di.getATPSvc();
  await atpSvc.ensureWithAtp(prisma, operator);
});
