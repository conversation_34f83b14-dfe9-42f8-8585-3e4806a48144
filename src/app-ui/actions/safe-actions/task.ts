"use server";

import { authActionClient } from "@/app-ui/actions/client";
import { di } from "@/app-ui/di";
import { TaskType } from "@/lib/domain/task/svc";
import z from "zod";

const XYRegionSchema = z.object({
  x: z.number(),
  y: z.number(),
  width: z.number(),
  height: z.number(),
});

const BaseChannelSchema = z.object({
  id: z.number(),
  name: z.string(),
});

const RescaleChannelSchema = BaseChannelSchema.extend({
  rescale_range: z.object({
    min: z.number(),
    max: z.number(),
  }),
});

const ClipChannelSchema = BaseChannelSchema.extend({
  clip_range: z.object({
    min: z.number(),
    max: z.number(),
  }),
});

const BaseSaveAsSettingSchema = z.object({
  channelChosenType: z.enum(["shown", "all"]),
  splitChannel: z.boolean().optional(),
  saveAsType: z.enum(["irs", "tiff", "jpg"]),
  selectedChannels: z.array(BaseChannelSchema),
  region: XYRegionSchema.optional(),
});

const BaseSaveAsArgsSchema = z.object({
  itemId: z.string(),
  setting: BaseSaveAsSettingSchema,
  output: z.object({
    folder_name: z.string(),
    tgt_folder_id: z.string().optional(),
  }),
});

const IngestTaskReqSchema = z.object({
  taskType: z.literal(TaskType.ingest),
  taskArgs: z.object({
    projectId: z.string(),
    sourcePath: z.string(),
    outputFolderId: z.string().optional(),
    outputName: z.string(),
  }),
});

const SaveAsTaskReqSchema = z.object({
  taskType: z.literal(TaskType.save_as),
  taskArgs: BaseSaveAsArgsSchema,
});

const RescaleIntensityTaskReqSchema = z.object({
  taskType: z.literal(TaskType.rescale_intensity),
  taskArgs: BaseSaveAsArgsSchema.extend({
    setting: BaseSaveAsSettingSchema.extend({
      selectedChannels: z.array(RescaleChannelSchema),
    }),
  }),
});

const ClipIntensityTaskReqSchema = z.object({
  taskType: z.literal(TaskType.clip_intensity),
  taskArgs: BaseSaveAsArgsSchema.extend({
    setting: BaseSaveAsSettingSchema.extend({
      selectedChannels: z.array(ClipChannelSchema),
    }),
  }),
});

const BitDepthConvertTaskReqSchema = z.object({
  taskType: z.literal(TaskType.bit_depth_convert),
  taskArgs: BaseSaveAsArgsSchema.extend({
    setting: BaseSaveAsSettingSchema.extend({
      tgt_bit_depth: z.enum(["uint8", "uint16", "float32"]),
    }),
  }),
});

const FlipTaskReqSchema = z.object({
  taskType: z.literal(TaskType.flip),
  taskArgs: BaseSaveAsArgsSchema.extend({
    setting: BaseSaveAsSettingSchema.extend({
      flip_dims: z.enum(["X", "Y", "XY"]),
    }),
  }),
});

const RegisterTaskReqSchema = z.object({
  taskType: z.literal(TaskType.register),
  // TODO: define args
  taskArgs: z.any(),
});

const MergeChannelsTaskReqSchema = z.object({
  taskType: z.literal(TaskType.merge_channels),
  // TODO: define args
  taskArgs: z.any(),
});

const TaskReqSchema = z.discriminatedUnion("taskType", [
  IngestTaskReqSchema,
  SaveAsTaskReqSchema,
  RescaleIntensityTaskReqSchema,
  ClipIntensityTaskReqSchema,
  BitDepthConvertTaskReqSchema,
  FlipTaskReqSchema,
  RegisterTaskReqSchema,
  MergeChannelsTaskReqSchema,
]);

export type TaskReq = z.infer<typeof TaskReqSchema>;

const AddTaskReqSchema = z.object({
  projectId: z.string(),
  taskReq: TaskReqSchema,
  snapShot: z.instanceof(Blob).optional(),
});
export type AddTaskReq = z.infer<typeof AddTaskReqSchema>;

const AddTaskResSchema = z.object({
  taskId: z.string(),
});
export const addTask = authActionClient
  .inputSchema(AddTaskReqSchema)
  .outputSchema(AddTaskResSchema)
  .action(async ({ parsedInput, ctx }) => {
    const operator = ctx.session;
    const taskSvc = await di.getTaskSvc();
    const task = await taskSvc.add_task_and_run(operator, parsedInput);
    return { taskId: task.id, test: 1 };
  });

const RerunTaskInputSchema = z.object({
  projectId: z.string(),
  taskId: z.string(),
});
export const rerunTask = authActionClient
  .inputSchema(RerunTaskInputSchema)
  .action(async ({ parsedInput, ctx }) => {
    const operator = ctx.session;
    const taskSvc = await di.getTaskSvc();
    await taskSvc.rerunTaskById(operator, parsedInput);
  });
