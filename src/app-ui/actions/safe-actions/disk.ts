"use server";

import { authActionClient } from "@/app-ui/actions/client";
import { di } from "@/app-ui/di";
import { DiskDirNameReg, DiskObjectNameReg } from "@/lib/domain/disk/constants";
import z from "zod";

const CreateDirInputSchema = z.object({
  projectId: z.string(),
  parentPath: z.string(),
  name: z.string().regex(DiskDirNameReg),
});

const RemoveInputSchema = z.object({
  projectId: z.string(),
  pathPart: z.string(),
});

const CopyToInputSchema = z.object({
  projectId: z.string(),
  sourcePath: z.string(),
  destinationPath: z.string(),
  forbidOverwrite: z.boolean().optional().default(false),
});

const MoveToInputSchema = z.object({
  projectId: z.string(),
  sourcePath: z.string(),
  destinationPath: z.string(),
});

const CreateCopyInputSchema = z.object({
  projectId: z.string(),
  sourcePath: z.string(),
});

const RenameInputSchema = z
  .object({
    projectId: z.string(),
    parentPath: z.string(),
    originName: z.string(),
    name: z.string(),
    isFile: z.boolean().optional(),
  })
  .superRefine(({ isFile, name }, ctx) => {
    const reg = isFile ? DiskObjectNameReg : DiskDirNameReg;
    if (!reg.test(name)) {
      ctx.addIssue({
        code: "custom",
        message: "invalid type of name",
      });
    }
  });

export const deleteDiskObject = authActionClient
  .inputSchema(RemoveInputSchema)
  .action(
    async ({ parsedInput }) => {
      const diskSvc = await di.getDiskSvc();
      await diskSvc.remove(parsedInput);
    },
    {
      throwServerError: false,
    }
  );

export const copyDiskObject = authActionClient
  .inputSchema(CopyToInputSchema)
  .action(async ({ parsedInput }) => {
    const diskSvc = await di.getDiskSvc();
    await diskSvc.copyTo(parsedInput);
  });

export const moveDiskObject = authActionClient
  .inputSchema(MoveToInputSchema)
  .action(async ({ parsedInput }) => {
    const diskSvc = await di.getDiskSvc();
    await diskSvc.moveTo(parsedInput);
  });

export const createDiskDir = authActionClient
  .inputSchema(CreateDirInputSchema)
  .action(async ({ parsedInput }) => {
    const diskSvc = await di.getDiskSvc();
    await diskSvc.createDir(parsedInput);
  });

export const createDiskObjectCopy = authActionClient
  .inputSchema(CreateCopyInputSchema)
  .action(async ({ parsedInput }) => {
    const diskSvc = await di.getDiskSvc();
    await diskSvc.createCopy(parsedInput);
  });

export const renameDiskObject = authActionClient
  .inputSchema(RenameInputSchema)
  .action(async ({ parsedInput }) => {
    const diskSvc = await di.getDiskSvc();
    await diskSvc.rename(parsedInput);
  });

if (import.meta.vitest) {
  const { describe, it, expect } = import.meta.vitest;

  describe("RenameInputSchema", () => {
    it("should throw err when file", () => {
      const testParseFunc = () =>
        RenameInputSchema.parse({
          projectId: "project-1",
          parentPath: "dirA",
          originName: "fileA.txt",
          name: "^^&.txt",
          isFile: true,
        });
      expect(testParseFunc).toThrow();
    });

    it("validate file normally", () => {
      const testParseFunc = () =>
        RenameInputSchema.parse({
          projectId: "project-1",
          parentPath: "dirA",
          originName: "fileA.txt",
          name: "3.txt",
          isFile: true,
        });
      expect(testParseFunc).not.toThrow();
    });

    it("should throw err when folder", () => {
      const testParseFunc = () =>
        RenameInputSchema.parse({
          projectId: "project-1",
          parentPath: "dirA",
          originName: "folderA",
          name: "3.txt",
        });
      expect(testParseFunc).toThrow();
    });

    it("validate folder normally", () => {
      const testParseFunc = () =>
        RenameInputSchema.parse({
          projectId: "project-1",
          parentPath: "dirA",
          originName: "folderA",
          name: "3Folder",
          isFile: true,
        });
      expect(testParseFunc).not.toThrow();
    });
  });
}
