"use server";

import { di } from "@/app-ui/di";
import { authActionClient } from "@/app-ui/actions/client";
import z from "zod";

const SaveImgMetaInputSchema = z.object({
  id: z.string(),
  channels: z
    .array(
      z.object({
        id: z.int(),
        name: z.string().nullish(),
        color: z.string().nullish(),
        view_min: z.number().nullish(),
        view_max: z.number().nullish(),
        view_shown: z.boolean().nullish(),
      })
    )
    .nullable()
    .optional(),
  phys_x: z.number().nullish(),
  phys_x_unit: z.string().nullish(),
  phys_y: z.number().nullish(),
  phys_y_unit: z.string().nullish(),
  view_gamma: z.number().nullish(),
});

// TODO: refreshData: img-info & img-original-meta
export const saveImgMeta = authActionClient
  .inputSchema(SaveImgMetaInputSchema)
  .action(async ({ parsedInput }) => {
    const imgSvc = await di.getImgSvc();
    await imgSvc.saveImgMeta(parsedInput);
  });
