import { DATE_FORMAT_SLASH, formatDate, TIME_FORMAT_BRIEF } from "@/lib/utils";
import { Row, Col, Flex } from "antd";
import Text from "antd/es/typography/Text";
import MBXSvg from "@/public/item-bmx.svg";

export type ItemData = {
  id: string;
  name: string;
  date: Date;
};

export function PrjOverviewItemList({
  title,
  data,
}: {
  title: string;
  data: ItemData[];
}) {
  return (
    <div className="mt-3">
      <div className="text-xs text-[#171f2b40]">{title}</div>

      <Row gutter={[12, 12]} className="mt-1">
        {data.map(({ id, name, date }) => (
          <Col key={id} xs={12} sm={8} lg={6}>
            <Flex align="center" gap={2}>
              <MBXSvg className="size-4 text-[#828282] shrink-0" />
              <Text ellipsis={{ tooltip: name }}>{name}</Text>
            </Flex>
            <div className="text-[#171f2b40] text-xs leading-5">
              {formatDate(date, DATE_FORMAT_SLASH + " " + TIME_FORMAT_BRIEF)}
            </div>
          </Col>
        ))}
      </Row>
    </div>
  );
}
