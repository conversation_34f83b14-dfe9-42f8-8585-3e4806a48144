import { Button, Divider } from "antd";
import { AiOutlineCarryOut } from "react-icons/ai";
import { PrjOverviewRunningTask } from "@/app-ui/components/project/task-running";
import { PrjOverviewDoneTask } from "@/app-ui/components/project/task-done";
import Link from "next/link";
import { paths } from "@/app/route-path";

export function PrjOverviewTaskSection({ projectId }: { projectId: string }) {
  return (
    <div className="mt-10">
      <Divider orientation="left" orientationMargin={0} plain>
        <div className="flex items-center gap-2">
          <AiOutlineCarryOut size={16} />
          任务
        </div>
      </Divider>

      <PrjOverviewRunningTask projectId={projectId} />
      <PrjOverviewDoneTask projectId={projectId} />

      <Link href={paths.project.projectId(projectId).task.path} target="_blank">
        <Button variant="link" color="primary" className="p-0 mt-3">
          查看
        </Button>
      </Link>
    </div>
  );
}
