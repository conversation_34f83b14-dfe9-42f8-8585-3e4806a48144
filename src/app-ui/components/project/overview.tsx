import { PrjOverviewSubscribe } from "@/app-ui/components/project/subscribe";
import { PrjOverviewTaskSection } from "@/app-ui/components/project/task-section";
import { PrjOverviewItemSection } from "@/app-ui/components/project/item-section";
import { PrjOverviewDiskSection } from "@/app-ui/components/project/disk-section";

export function PrjOverview({ projectId }: { projectId: string }) {
  return (
    <div className="m-6">
      <PrjOverviewSubscribe />
      <PrjOverviewTaskSection projectId={projectId} />
      <PrjOverviewItemSection projectId={projectId} />
      <PrjOverviewDiskSection projectId={projectId} />
    </div>
  );
}
