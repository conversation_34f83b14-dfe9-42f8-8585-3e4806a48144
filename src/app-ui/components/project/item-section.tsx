import { Divider } from "antd";
import DataSvg from "@/public/data-outlined.svg";
import { PrjOverviewHistoryItem } from "@/app-ui/components/project/item-history";
import { PrjOverviewRecentItem } from "@/app-ui/components/project/item-recent";

export function PrjOverviewItemSection({ projectId }: { projectId: string }) {
  return (
    <div className="mt-10">
      <Divider orientation="left" orientationMargin={0} plain>
        <div className="flex items-center gap-2">
          <DataSvg className="size-4" />
          数据
        </div>
      </Divider>

      <PrjOverviewHistoryItem projectId={projectId} />
      <PrjOverviewRecentItem projectId={projectId} />
    </div>
  );
}
