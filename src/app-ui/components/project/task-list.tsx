import { DATE_FORMAT_SLASH, formatDate, TIME_FORMAT_BRIEF } from "@/lib/utils";
import { Row, Col } from "antd";
import Text from "antd/es/typography/Text";

export function PrjOverviewTaskList({
  title,
  data,
}: {
  title: string;
  data: {
    id: string;
    taskType: string;
    title: string;
    taskDate: Date;
  }[];
}) {
  return (
    <div className="mt-3">
      <div className="text-xs text-[#171f2b40]">{title}</div>

      <Row gutter={[12, 12]} className="mt-1">
        {data.map(({ id, taskType, title, taskDate }) => (
          <Col key={id} xs={12} sm={8} lg={6}>
            <Text ellipsis={{ tooltip: taskType }} className="block">
              {taskType}
            </Text>
            <Text
              className="text-[#171f2b40] text-xs leading-5"
              ellipsis={{ tooltip: title }}
            >
              {title}
            </Text>
            <div className="text-[#171f2b40] text-xs leading-5">
              {formatDate(
                taskDate,
                DATE_FORMAT_SLASH + " " + TIME_FORMAT_BRIEF
              )}
            </div>
          </Col>
        ))}
      </Row>
    </div>
  );
}
