import { DATE_FORMAT_SLASH, formatDate, TIME_FORMAT_BRIEF } from "@/lib/utils";
import { Row, Col, Flex } from "antd";
import Text from "antd/es/typography/Text";
import { AiOutlinePicture } from "react-icons/ai";

export function PrjOverviewDiskList({ title }: { title: string }) {
  const data = [
    {
      id: 1,
      name: "图像_24915-r1_01",
      lastModifiedAt: new Date(),
      path: "/xx./1.png",
    },
    {
      id: 2,
      name: "示例图-阈值60%.png示例图-阈值60%.png",
      lastModifiedAt: new Date(),
      path: "/xx./",
    },
    {
      id: 3,
      name: "示例图-阈值60%.png",
      lastModifiedAt: new Date(),
      path: "/xx./1.tif",
    },
    {
      id: 4,
      name: "PRAD-1-2-R3-left",
      lastModifiedAt: new Date(),
      path: "/xx./1.png",
    },
  ];
  return (
    <div className="mt-3">
      <div className="text-xs text-[#171f2b40]">{title}</div>

      <Row gutter={[12, 12]} className="mt-1">
        {data.map(({ id, name, lastModifiedAt }) => (
          <Col key={id} xs={12} sm={8} lg={6}>
            <Flex align="center" gap={2}>
              <AiOutlinePicture size={16} className="shrink-0" />
              <Text ellipsis={{ tooltip: name }}>{name}</Text>
            </Flex>
            <div className="text-[#171f2b40] text-xs leading-5">
              {formatDate(
                lastModifiedAt,
                DATE_FORMAT_SLASH + " " + TIME_FORMAT_BRIEF
              )}
            </div>
          </Col>
        ))}
      </Row>
    </div>
  );
}
