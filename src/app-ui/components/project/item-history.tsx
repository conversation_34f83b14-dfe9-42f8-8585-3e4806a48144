"use client";

import { PrjOverviewItemList } from "@/app-ui/components/project/item-list";
import { diverseApi } from "@/lib/apis/diverse/api";
import { Result, Skeleton } from "antd";

export function PrjOverviewHistoryItem({ projectId }: { projectId: string }) {
  const {
    data = [],
    isLoading,
    error,
  } = diverseApi.useGetHistoryItemsQuery({
    prjId: projectId,
    length: 4,
  });

  if (error) {
    return <Result status="error" title="something wrong" />;
  }

  if (isLoading) {
    return <Skeleton />;
  }

  return (
    <PrjOverviewItemList
      title="历史记录"
      data={data.map(({ id, name, created_at }) => ({
        id,
        name,
        date: created_at,
      }))}
    />
  );
}
