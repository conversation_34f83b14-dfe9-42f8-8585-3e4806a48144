"use client";

import { PrjOverviewTaskList } from "@/app-ui/components/project/task-list";
import { diverseApi } from "@/lib/apis/diverse/api";
import { Result, Skeleton } from "antd";

export function PrjOverviewRunningTask({ projectId }: { projectId: string }) {
  const {
    data = [],
    isLoading,
    error,
  } = diverseApi.useGetRunningTasksQuery({
    prjId: projectId,
    length: 4,
  });

  if (error) {
    return <Result status="error" title="something wrong" />;
  }

  if (isLoading) {
    return <Skeleton active />;
  }

  return (
    <PrjOverviewTaskList
      title="进行中"
      data={data.map((task) => ({
        id: task.id,
        title: "xxxxx",
        taskType: task.task_type,
        taskDate: task.start_at,
      }))}
    />
  );
}
