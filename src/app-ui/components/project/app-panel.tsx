import styles from "@/app-ui/components/project/resource.module.css";
import { paths } from "@/app/route-path";
import AppChannelToolIcon from "@/public/app-channel-tool.webp";
import AppIngestIcon from "@/public/app-ingest.webp";
import AppRegisterIcon from "@/public/app-register.webp";
import AppIcon from "@/public/<EMAIL>";
import { Tabs, type TabsProps } from "antd";
import Text from "antd/es/typography/Text";
import clsx from "clsx";
import Image from "next/image";
import Link from "next/link";

function AppLink({
  linkPath,
  name,
  desc,
  icon,
}: {
  linkPath: string;
  name: string;
  desc: string;
  icon: React.ReactNode;
}) {
  return (
    <>
      <Link href={linkPath} className="p-2">
        <div className="flex gap-2">
          {icon}
          <div className="flex flex-col gap-1 min-w-0">
            <Text>{name}</Text>
            <Text
              type="secondary"
              ellipsis={{ tooltip: { mouseEnterDelay: 1 } }}
            >
              {desc}
            </Text>
          </div>
        </div>
      </Link>
    </>
  );
}

export function PrjAppPanel({ projectId }: { projectId: string }) {
  const items: TabsProps["items"] = [
    {
      key: "apps",
      label: (
        <div className="flex-center gap-1 ">
          <Image src={AppIcon} alt="apps" className="size-4" />
          应用
        </div>
      ),
      children: (
        <>
          <div className="flex flex-col gap-2">
            <AppLink
              linkPath={paths.project.projectId(projectId).app.ingest.path}
              name="解码"
              desc="将原始图像解码为BMX格式，支持多种格式的解码，包括但不限于TIFF、OME-TIFF、BMP、PNG、JPEG、GIF、BIOFORMATS等。"
              icon={
                <Image src={AppIngestIcon} alt="ingest" className="size-10" />
              }
            />
            <AppLink
              linkPath={paths.project.projectId(projectId).app.register.path}
              name="配准"
              desc="提供一系列高度灵活解决图像配准问题的算法 (Elastix, SIFT,
              ORB)，支持二维多通道刚性（平移、欧拉）配准，为复杂的生物图像分析提供高效解决方案。"
              icon={
                <Image
                  src={AppRegisterIcon}
                  alt="register"
                  className="size-10"
                />
              }
            />
            <AppLink
              linkPath={paths.project.projectId(projectId).app.channelTool.path}
              name="通道管理"
              desc="将若干相同尺寸的图像，按需进行无损合成等操作，得到一张新的多通道图像。"
              icon={
                <Image
                  src={AppChannelToolIcon}
                  alt="channel-tool"
                  className="size-10"
                />
              }
            />
          </div>
        </>
      ),
    },
  ];

  return (
    <Tabs
      defaultActiveKey="apps"
      tabPosition="left"
      items={items}
      className={clsx(styles.rscTabs, "h-full")}
      indicator={{ size: 14 }}
    />
  );
}
