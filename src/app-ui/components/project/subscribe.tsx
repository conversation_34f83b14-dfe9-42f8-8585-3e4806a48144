import { Divider, Row, Col } from "antd";
import { AiOutlineStar } from "react-icons/ai";

export function PrjOverviewSubscribe() {
  const data = [
    {
      key: "disk",
      label: "云盘",
    },
    {
      key: "item",
      label: "数据",
    },
    {
      key: "transcode",
      label: "IRS转码",
    },
    {
      key: "viewer",
      label: "IRS编辑器",
    },
    {
      key: "registration",
      label: "配准",
    },
  ];
  return (
    <div>
      <Divider orientation="left" orientationMargin={0} plain>
        <div className="flex items-center gap-2">
          <AiOutlineStar size={16} />
          订阅
        </div>
      </Divider>

      <Row gutter={[12, 12]}>
        {data.map((item) => (
          <Col key={item.key} xs={12} sm={8} lg={6}>
            <div>{item.label}</div>
          </Col>
        ))}
      </Row>
    </div>
  );
}
