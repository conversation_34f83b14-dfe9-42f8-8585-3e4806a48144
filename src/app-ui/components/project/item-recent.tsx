"use client";

import { PrjOverviewItemList } from "@/app-ui/components/project/item-list";
import { diverseApi } from "@/lib/apis/diverse/api";
import { Result, Skeleton } from "antd";

export function PrjOverviewRecentItem({ projectId }: { projectId: string }) {
  const {
    data = [],
    isLoading,
    error,
  } = diverseApi.useGetRecentItemsQuery({
    prjId: projectId,
    length: 4,
  });

  if (error) {
    return <Result status="error" title="something wrong" />;
  }

  if (isLoading) {
    return <Skeleton />;
  }

  return (
    <PrjOverviewItemList
      title="最近更新"
      data={data.map(({ id, name, updated_at }) => ({
        id,
        name,
        date: updated_at,
      }))}
    />
  );
}
