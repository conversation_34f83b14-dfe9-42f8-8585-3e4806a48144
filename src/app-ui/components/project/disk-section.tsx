import { Divider } from "antd";
import CloudDiskSvg from "@/public/cloud-disk-outlined.svg";
import { PrjOverviewDiskList } from "@/app-ui/components/project/disk-list";

function PrjOverviewHistoryItem() {
  return <PrjOverviewDiskList title="历史记录" />;
}

function PrjOverviewLastModifiedItem() {
  return <PrjOverviewDiskList title="最近更新" />;
}

export function PrjOverviewDiskSection({ projectId }: { projectId: string }) {
  console.log(projectId);

  return (
    <div className="mt-10">
      <Divider orientation="left" orientationMargin={0} plain>
        <div className="flex items-center gap-2">
          <CloudDiskSvg className="size-4" />
          云盘
        </div>
      </Divider>

      <PrjOverviewHistoryItem />
      <PrjOverviewLastModifiedItem />
    </div>
  );
}
