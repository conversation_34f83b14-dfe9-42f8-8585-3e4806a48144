"use client";

import { durationDisplay, formatDate } from "@/lib/utils";
import { Descriptions, Typography } from "antd";

type TaskDetailRes = {
  id: string;
  project_id: string;
  task_type: string;
  task_status: string;
  start_at: Date;
  finished_at: Date | null;
  error: string | null;
};

export function TaskDetail({ taskInfo }: { taskInfo: TaskDetailRes }) {
  console.log(taskInfo);
  return (
    <div>
      <div>任务进度</div>

      <Descriptions
        bordered
        items={[
          {
            label: "任务类型",
            children: taskInfo.task_type,
          },
          {
            label: "任务状态",
            children: taskInfo.task_status,
            span: 2,
          },
          {
            label: "提交时间",
            children: <div>{formatDate(taskInfo.start_at)}</div>,
          },
          {
            label: "结束时间",
            children: <div>{formatDate(taskInfo.finished_at)}</div>,
          },
          {
            label: "耗时",
            children: durationDisplay(taskInfo.start_at, taskInfo.finished_at),
          },
        ]}
      />
      <TaskError taskInfo={taskInfo} />
      <TaskSubmitInfo taskInfo={taskInfo} />
    </div>
  );
}
function TaskError({ taskInfo }: { taskInfo: TaskDetailRes }) {
  if (!taskInfo.error) {
    return;
  }

  return (
    <div>
      <div>错误</div>
      <Typography.Paragraph
        className="whitespace-pre"
        copyable
        ellipsis={{ expandable: true, rows: 3 }}
      >
        {taskInfo.error}
      </Typography.Paragraph>
    </div>
  );
}

function TaskSubmitInfo({ taskInfo }: { taskInfo: TaskDetailRes }) {
  return <div>show submit screen shot</div>;
}
