import {
  DISK_ROOT_PATH,
  diskSlice,
  DiskTreeId,
  DiskTreeNode,
  useUpdateTreeData,
} from "@/lib/app-store/disk-slice";
import { useAppSelector } from "@/lib/app-store/store";
import { dropTargetForElements } from "@atlaskit/pragmatic-drag-and-drop/element/adapter";
import { TreeSelect, TreeSelectProps } from "antd";
import { useEffect, useRef } from "react";

type Props = TreeSelectProps & {
  projectId: string;
  treeId: DiskTreeId;
  isDir?: boolean;
};

export function DiskTreeSelector({
  projectId,
  treeId,
  isDir,
  ...resProps
}: Props) {
  const ref = useRef<HTMLInputElement>(null);

  const updateTreeData = useUpdateTreeData();

  const treeData = useAppSelector((s) =>
    diskSlice.selectors.selectTreeData(s, treeId)
  );

  const updateExpandedKeys = (keys: string[]) => {
    updateTreeData({
      projectId,
      treeId,
      generateUpdateKeys: () => ({ expandedKeys: keys }),
      isDir,
    });
  };

  const onOpenChange = (open: boolean) => {
    if (open) {
      updateTreeData({
        projectId,
        treeId,
        generateUpdateKeys: (expandedKeys) => ({
          expandedKeys: [...expandedKeys, DISK_ROOT_PATH],
        }),
        isDir,
      });
    }
  };

  const onChange = resProps.onChange;

  useEffect(() => {
    if (!ref.current) {
      return;
    }

    const cleanup = dropTargetForElements({
      element: ref.current,
      onDrop(args) {
        const node = args.source.data.node as DiskTreeNode;
        const diskPath = node.key;
        if (diskPath) {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          onChange?.(diskPath, [], {} as any);
        }
      },
    });
    return cleanup;
  }, [onChange]);

  return (
    <div ref={ref}>
      <TreeSelect
        allowClear
        onOpenChange={onOpenChange}
        treeData={treeData[0]?.children ?? []}
        fieldNames={{ value: "key" }}
        onTreeExpand={(keys) => updateExpandedKeys(keys as string[])}
        {...resProps}
      />
    </div>
  );
}
