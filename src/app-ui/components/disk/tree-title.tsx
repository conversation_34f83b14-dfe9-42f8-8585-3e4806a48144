import { DiskDeleteBtn } from "@/app-ui/components/disk/delete-btn";
import { DiskCopyBtn } from "@/app-ui/components/disk/copy-btn";
import { DiskMoveBtn } from "@/app-ui/components/disk/move-btn";
import {
  diskSlice,
  getNameFromDiskPath,
  isRootPath,
  type DiskTreeId,
  type DiskTreeNode,
} from "@/lib/app-store/disk-slice";
import { Dropdown, Typography } from "antd";
import { AiOutlineEllipsis } from "react-icons/ai";
import { DiskCopyPathBtn } from "@/app-ui/components/disk/copy-path-btn";
import type { ItemType } from "antd/es/menu/interface";
import { humanFileSize } from "@/lib/utils";
import { DiskCreateDirBtn } from "@/app-ui/components/disk/create-dir-btn";
import { DiskCreateDirInput } from "@/app-ui/components/disk/create-dir-input";
import { DiskCreateCopyBtn } from "@/app-ui/components/disk/create-copy-btn";
import { DiskRenameBtn } from "@/app-ui/components/disk/rename-btn";
import { useAppDispatch, useAppSelector } from "@/lib/app-store/store";
import { DiskRenameInput } from "@/app-ui/components/disk/rename-input";
import { Uploader } from "@/app-ui/components/upload/uploader";
import { draggable } from "@atlaskit/pragmatic-drag-and-drop/element/adapter";
import { useEffect, useRef } from "react";
export type DiskTreeTitleProps = {
  node: DiskTreeNode;
  projectId: string;
  treeId: DiskTreeId;
  showMenu?: boolean;
};

function TitleMenus({
  node,
  projectId,
  treeId,
}: {
  node: DiskTreeNode;
  projectId: string;
  treeId: DiskTreeId;
}) {
  const dispatch = useAppDispatch();

  const { key: diskPath, size, isLeaf: isFile } = node;
  const baseInfo = { diskPath, projectId, treeId };

  const showItemIf = (item: ItemType, show?: boolean) => {
    return show ? item : undefined;
  };

  const items: ItemType[] = [
    {
      key: "copy-path",
      label: <DiskCopyPathBtn diskPath={diskPath} />,
    },
    showItemIf(
      {
        key: "upload",
        label: "上传",
        children: [
          {
            key: "upload-file",
            label: (
              <Uploader projectId={projectId} treeId={treeId}>
                上传文件
              </Uploader>
            ),
          },
          {
            key: "upload-folder",
            label: (
              <Uploader projectId={projectId} treeId={treeId} type="folder">
                上传文件夹
              </Uploader>
            ),
          },
        ],
      },
      !isFile
    ),
    showItemIf(
      {
        key: "mkdir",
        label: <DiskCreateDirBtn {...baseInfo} />,
      },
      !isFile
    ),
    {
      key: "rename",
      label: <DiskRenameBtn diskPath={diskPath} treeId={treeId} />,
    },
    {
      key: "download",
      label: "下载",
    },
    {
      type: "divider",
    } as ItemType,
    {
      key: "createCopy",
      label: <DiskCreateCopyBtn {...baseInfo} />,
    },
    {
      key: "move",
      label: <DiskMoveBtn {...baseInfo} />,
    },
    {
      key: "moreAction",
      label: "更多操作",
      children: [
        {
          key: "copy",
          label: <DiskCopyBtn {...baseInfo} />,
        },
      ],
    },
    {
      type: "divider",
    } as ItemType,
    showItemIf(
      {
        key: "size",
        label: `大小 ${humanFileSize(size)}`,
        disabled: true,
      },
      isFile
    ),
    showItemIf(
      {
        type: "divider",
      } as ItemType,
      isFile
    ),
    {
      key: "delete",
      label: <DiskDeleteBtn {...baseInfo} />,
    },
  ].filter((v) => v !== undefined);

  return (
    <Dropdown
      overlayClassName="w-40 [&_.ant-dropdown-menu-item-disabled]:cursor-text"
      menu={{
        items,
        onClick: ({ domEvent }) => {
          domEvent.stopPropagation();
          dispatch(
            diskSlice.actions.setActiveNode({
              treeId,
              activeNode: node,
            })
          );
        },
      }}
    >
      <AiOutlineEllipsis size={20} className="text-black/45" />
    </Dropdown>
  );
}

export function DiskTreeTitle({
  node,
  projectId,
  treeId,
  showMenu,
}: DiskTreeTitleProps) {
  const { key, isNewDir, isLeaf: isFile } = node;
  const renamingNodeKey = useAppSelector((s) =>
    diskSlice.selectors.selectRenamingNodeKey(s, treeId)
  );

  if (!showMenu || isRootPath(key)) {
    return (
      <div className="flex items-center justify-between">
        <DragableNode node={node} projectId={projectId} />
      </div>
    );
  } else if (isNewDir) {
    return (
      <DiskCreateDirInput
        diskPath={key}
        treeId={treeId}
        projectId={projectId}
      />
    );
  } else if (renamingNodeKey === key) {
    return (
      <DiskRenameInput
        treeId={treeId}
        diskPath={key}
        projectId={projectId}
        isFile={isFile}
      />
    );
  } else {
    return (
      <div className="flex items-center justify-between group">
        <DragableNode node={node} projectId={projectId} />
        <span className="hidden group-hover:block">
          <TitleMenus node={node} projectId={projectId} treeId={treeId} />
        </span>
      </div>
    );
  }
}

function DragableNode({
  node,
  projectId,
}: {
  node: DiskTreeNode;
  projectId: string;
}) {
  const ref = useRef<HTMLDivElement | null>(null);
  const name = getNameFromDiskPath(node.key);

  useEffect(() => {
    if (!ref.current) {
      return;
    }

    const cleanup = draggable({
      element: ref.current,
      getInitialData() {
        return {
          type: "disk-node",
          node,
          projectId,
        };
      },
      canDrag() {
        return !isRootPath(node.key);
      },
    });
    return cleanup;
  }, [node, projectId]);

  return (
    <div ref={ref} className="flex-auto w-0">
      <Typography.Text ellipsis>{name}</Typography.Text>
    </div>
  );
}
