"use client";

import { Tree } from "antd";
import {
  DISK_ROOT_PATH,
  diskSlice,
  type DiskTreeId,
  type DiskTreeNode,
  useUpdateTreeData,
} from "@/lib/app-store/disk-slice";
import { useAppDispatch, useAppSelector } from "@/lib/app-store/store";
import { useEffect } from "react";
import { DiskTreeTitle } from "@/app-ui/components/disk/tree-title";
import { DirectoryTreeProps } from "antd/lib/tree";
import styles from "@/app-ui/components/disk/viewer.module.css";
import { AiOutlineDown, AiOutlineRight } from "react-icons/ai";
import { DiskTreeIcon } from "@/app-ui/components/disk/tree-icon";

const { DirectoryTree } = Tree;

export type DiskViewerProps = {
  projectId: string;
  treeId: DiskTreeId;
  showMenu?: boolean;
};

export function DiskViewer({ projectId, treeId, showMenu }: DiskViewerProps) {
  const dispatch = useAppDispatch();

  const expandedKeys = useAppSelector((s) =>
    diskSlice.selectors.selectExpandedKeys(s, treeId)
  );
  const treeData = useAppSelector((s) =>
    diskSlice.selectors.selectTreeData(s, treeId)
  );
  const updateTreeData = useUpdateTreeData();

  useEffect(() => {
    updateTreeData({
      projectId,
      treeId,
      generateUpdateKeys: () => ({
        expandedKeys: [DISK_ROOT_PATH],
      }),
    });
  }, [updateTreeData, projectId, treeId]);

  const updateExpandedKeys = (keys: string[]) => {
    updateTreeData({
      projectId,
      treeId,
      generateUpdateKeys: () => ({ expandedKeys: keys }),
    });
  };

  const handleSelect: DirectoryTreeProps<DiskTreeNode>["onSelect"] = (
    _,
    { selectedNodes }
  ) => {
    dispatch(
      diskSlice.actions.setActiveNode({ treeId, activeNode: selectedNodes[0] })
    );
  };

  const titleRender = (node: DiskTreeNode) => {
    return (
      <DiskTreeTitle
        node={node}
        projectId={projectId}
        treeId={treeId}
        showMenu={showMenu}
      />
    );
  };

  return (
    <DirectoryTree<DiskTreeNode>
      treeData={treeData}
      expandedKeys={expandedKeys}
      titleRender={titleRender}
      onSelect={handleSelect}
      onExpand={(keys) => updateExpandedKeys(keys as string[])}
      className={styles.tree}
      switcherIcon={({ expanded }) =>
        expanded ? <AiOutlineDown size={12} /> : <AiOutlineRight size={12} />
      }
      icon={(props) => (
        <DiskTreeIcon nodeKey={props.eventKey} isLeaf={props.isLeaf} />
      )}
    />
  );
}
