import { App } from "antd";
import { moveDiskObject } from "@/app-ui/actions/safe-actions/disk";
import {
  DISK_ROOT_PATH,
  getCurrentFolderDiskPath,
  getParentDiskPaths,
  useUpdateTreeData,
  type DiskTreeId,
} from "@/lib/app-store/disk-slice";
import { throwIfSafeActionError, useHandleApiErrorDefault } from "@/lib/utils";
import { useShowDiskTransfer } from "@/app-ui/components/disk/show-transfer";
import { getParentFolderDiskPath } from "@/lib/domain/disk/svc";
import { useState } from "react";
import { getUppy } from "@/lib/uppy";
import { useUppyState } from "@uppy/react";

export type DiskMoveBtnProps = {
  diskPath: string;
  projectId: string;
  treeId: DiskTreeId;
};

export function DiskMoveBtn({ diskPath, projectId, treeId }: DiskMoveBtnProps) {
  const { message } = App.useApp();
  const [uppy] = useState(getUppy);

  const updateTreeData = useUpdateTreeData();
  const handleApiError = useHandleApiErrorDefault();

  const uploadedFileIds = useUppyState(uppy, (state) =>
    Object.values(state.files)
      .filter(
        (item) =>
          (item.meta.relativePath as string).startsWith(diskPath) &&
          item.progress.uploadComplete
      )
      .map((item) => item.id)
  );

  const showTransfer = useShowDiskTransfer({
    title: "移动至",
    projectId,
    doSubmit: async ({ destinationNode }) => {
      await doMoveDiskObject(destinationNode?.key ?? DISK_ROOT_PATH);
    },
  });

  const doMoveDiskObject = async (destinationPath: string) => {
    await moveDiskObject({
      projectId,
      sourcePath: diskPath,
      destinationPath: getCurrentFolderDiskPath(destinationPath),
    })
      .then(throwIfSafeActionError)
      .catch(handleApiError);
    message.success("移动成功");
    if (uploadedFileIds.length > 0) {
      uppy.removeFiles(uploadedFileIds);
    }
    updateTreeData({
      projectId,
      treeId,
      generateUpdateKeys(expandedKeys) {
        const desExpandedKeys = getParentDiskPaths(destinationPath);
        return {
          expandedKeys: [...desExpandedKeys, ...expandedKeys],
          refreshKeys: [
            getCurrentFolderDiskPath(destinationPath),
            getParentFolderDiskPath(diskPath),
          ],
        };
      },
    });
  };

  return (
    <div
      onClick={() => {
        showTransfer();
      }}
    >
      移动至
    </div>
  );
}
