"use client";

import { InfoPickerWidget } from "@/app-ui/components/image/picker/info-picker-widget";
import { ScaleBarWidget } from "@/app-ui/components/image/scale-bar/scale-bar-widget";
import { ViewportIds } from "@/app-ui/components/image/viewer/constants";
import { fetchTile } from "@/lib/apis/diverse/api";
import {
  clampToBBox,
  imageSlice,
  selectDrawFeatures,
  selectEnabledTools,
  selectGamma,
  selectMagFilter,
  selectSetScaleEvent,
  ToolId,
  useStateShownChannels,
} from "@/lib/app-store/image-slice";
import { useAppDispatch, useAppSelector } from "@/lib/app-store/store";
import type { ImgMeta } from "@/lib/domain/img/query/svc";
import MinimapLayer from "@/lib/layers/minimap-layer";
import { createTileLayer } from "@/lib/layers/tile-layer";
import {
  DrawRectangleMode,
  EditableGeoJsonLayer,
  TranslateMode,
  ViewMode,
  type EditableGeoJsonLayerProps,
  type FeatureCollection,
  type ModeProps,
  type Position,
} from "@deck.gl-community/editable-layers";
import {
  FilterContext,
  OrthographicView,
  OrthographicViewState,
  PickingInfo,
  type Viewport,
  type ViewStateChangeParameters,
  type ViewStateMap,
} from "@deck.gl/core";
import DeckGL, { DeckGLProps, type DeckGLRef } from "@deck.gl/react";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";

function layerFilter(context: FilterContext): boolean {
  const { layer, viewport } = context;
  return layer.id.startsWith(viewport.id);
}

interface ViewSize {
  width: number;
  height: number;
}

type ModeConfig = {
  baseX: number;
  baseY: number;
  screenSpace: boolean;
  viewport: Viewport | undefined;
}

class DrawRectangleModeHack extends DrawRectangleMode {
  getTwoClickPolygon(coord1: Position, coord2: Position, modeConfig: ModeConfig) {
    const feature = super.getTwoClickPolygon(coord1, coord2, modeConfig);
    clampToBBox(feature.geometry, modeConfig.baseX, modeConfig.baseY);
    return feature;
  }
}

class TranslateModeHack extends TranslateMode {
  getTranslateAction(
    startDragPoint: Position,
    currentPoint: Position,
    editType: string,
    props: ModeProps<FeatureCollection>
  ) {
    const res = super.getTranslateAction(
      startDragPoint,
      currentPoint,
      editType,
      props
    );

    if (res?.updatedData) {
      res.updatedData.features.forEach((x) => {
        clampToBBox(x.geometry, props.modeConfig.baseX, props.modeConfig.baseY);
      });
    }

    return res;
  }
}

export default function Viewer({
  imgId,
  imgMeta,
  style,
}: {
  imgId: string;
  imgMeta: ImgMeta;
  style?: Partial<CSSStyleDeclaration>;
}) {
  const [isViewStateInitialized, setIsViewStateInitialized] = useState(false);
  const deckRef = useRef<DeckGLRef>(null);
  const tileFetcher = fetchTile;
  const dispatch = useAppDispatch();
  const gamma = useAppSelector(selectGamma);
  const magFilter = useAppSelector(selectMagFilter);
  const setScaleEvent = useAppSelector(selectSetScaleEvent);
  const enabledTools = useAppSelector(selectEnabledTools);
  const enabledMiniMap = enabledTools.includes("mini-map");
  const enabledDrawRect = enabledTools.includes("draw-rect");
  const enabledSelectDraw = enabledTools.includes("select-draw");
  let editMode = ViewMode;
  if (enabledDrawRect) {
    editMode = DrawRectangleModeHack;
  }
  if (enabledSelectDraw) {
    editMode = TranslateModeHack;
  }

  // const enabledInfoPicker = enabledTools.includes("info-picker");
  const shownChannels = useStateShownChannels(imgMeta.channels);

  const [viewState, setViewState] = useState<ViewStateMap<OrthographicView[]>>(
    {}
  );
  const [viewSize, setViewSize] = useState<ViewSize>();
  const drawFeatures = useAppSelector(selectDrawFeatures);
  const cursorRef = useRef<string | null>(undefined);
  const onDrag = useCallback((info: PickingInfo) => {
    if (info.viewport?.id == ViewportIds.minimap) {
      if (info.coordinate) {
        setViewState((preState) => {
          return {
            ...preState,
            [ViewportIds.main]: {
              ...preState[ViewportIds.main],
              target: info.coordinate as [number, number],
            },
          };
        });
      }
    }
  }, []);

  const setScaleForShow = useCallback(
    (zoom: number) => {
      const scale = Math.pow(2, zoom);
      dispatch(imageSlice.actions.setScaleForShow(scale));
    },
    [dispatch]
  );

  // this is for react scale/canvas change, need to set zoom
  useEffect(() => {
    const scale = setScaleEvent?.scale || "fit-screen";
    if (!setScaleEvent && isViewStateInitialized) {
      return;
    }

    const { base_x, base_y } = imgMeta;
    const baseZoom = 0;

    let zoom: number | undefined;
    if (scale == "fit-screen") {
      if (viewSize) {
        zoom = calculateZoomForViewSize(
          base_x,
          base_y,
          baseZoom,
          0.8,
          viewSize
        );
      }
    } else {
      zoom = calculateZoomFromScale(scale, baseZoom);
    }

    if (zoom != undefined) {
      setScaleForShow(zoom);

      setViewState((preState) => {
        return {
          [ViewportIds.main]: {
            target: preState[ViewportIds.main]?.target || [
              base_x / 2,
              base_y / 2,
              0,
            ],
            zoom: zoom,
            minZoom: Math.min(imgMeta.min_level - imgMeta.max_level, -6),
            maxZoom: 10,
          } as OrthographicViewState,
        };
      });

      if (!isViewStateInitialized) {
        setIsViewStateInitialized(true);
      }
    }
  }, [
    imgMeta,
    isViewStateInitialized,
    setScaleEvent,
    setScaleForShow,
    viewSize,
  ]);

  const onDeckClick: DeckGLProps["onClick"] = (info, event) => {
    if (info.viewport?.id === ViewportIds.minimap) {
      centerToMiniMapClickPos(info.viewport, event.offsetCenter);
    }
  };

  const onViewStateChange = useCallback(
    (state: ViewStateChangeParameters<OrthographicViewState>) => {
      if (state.viewId == ViewportIds.main) {
        const target = state.viewState.target;
        if (target && imgMeta) {
          clampToImgEdge(target, imgMeta.base_x, imgMeta.base_y);
        }

        if (state.viewState.zoom) {
          setScaleForShow(state.viewState.zoom as number);
        }
      }

      setViewState((preState) => {
        return { ...preState, [state.viewId]: state.viewState };
      });
    },
    [setScaleForShow, imgMeta]
  );

  // need to change layer when shown channel or img-meta changed
  const layers = useMemo(() => {
    class EditableGeoJsonLayerHack extends EditableGeoJsonLayer {
      static layerName = "EditableGeoJsonLayerHack";
      getMapCoords(screenCoords: Position): Position {
        return this.unproject(screenCoords);
      }

      getModeProps(props: EditableGeoJsonLayerProps) {
        if (props.modeConfig) {
          props.modeConfig.viewport = this.context.viewport;
        }
        const res = super.getModeProps(props);

        res.onUpdateCursor = (cursor) => {
          cursorRef.current = cursor;
        };
        return res;
      }
    }

    const editableLayer = new EditableGeoJsonLayerHack({
      id: `${ViewportIds.main}:editable-layer:${imgId}`,
      data: drawFeatures,
      mode: editMode,
      modeConfig: {
        baseX: imgMeta.base_x,
        baseY: imgMeta.base_y,
        screenSpace: true,
        viewport: undefined,
      } as ModeConfig,
      onEdit(info) {
        // console.log(info);
        dispatch(imageSlice.actions.setDrawFeatures(info.updatedData));

        if (info.editType == "addFeature") {
          dispatch(imageSlice.actions.disableTool("draw-rect"));
          dispatch(imageSlice.actions.enableTool("select-draw"));
        } else if (info.editType == "updateTentativeFeature") {
          if (info.updatedData.features.length > 0) {
            dispatch(imageSlice.actions.clearDrawFeatures());
          }
        }
      },
      getFillColor(feature, isSelected, mode) {
        return [0, 0, 0, 0];
      },
      getLineColor(feature, isSelected, mode) {
        return [255, 255, 0, 255];
      },
      selectedFeatureIndexes: [0],
    });

    const layers = [
      createTileLayer(
        {
          source: imgId,
          channels: shownChannels,
          imgMeta: imgMeta,
          tileFetcher,
          pickable: true,
          gamma: gamma,
          magFilter: magFilter,
        },
        {
          id: `${ViewportIds.main}:tile-layer:${imgId}`,
          // TilesetClass: HackTileset2D,
        }
      ),
      enabledMiniMap &&
      new MinimapLayer({
        id: `${ViewportIds.minimap}:minimap-layer:${imgId}`,
        channels: shownChannels,
        source: imgId,
        imgMeta: imgMeta,
        tileFetcher: tileFetcher,
        visible: enabledMiniMap,
        gamma: gamma,
      }),
      editableLayer,
    ];
    return layers;
  }, [
    imgId,
    drawFeatures,
    editMode,
    imgMeta,
    shownChannels,
    tileFetcher,
    gamma,
    magFilter,
    enabledMiniMap,
    dispatch,
  ]);

  const widgets = useMemo(() => {
    const { phys_x: physicalSize, phys_x_unit: physicalUnit } = imgMeta;
    const widgets = [
      new ScaleBarWidget({
        id: "scale-bar",
        viewId: ViewportIds.main,
        placement: "bottom-right",
        physicalSize: physicalSize,
        physicalUnit: physicalUnit,
      }),

      new InfoPickerWidget({
        id: "info-picker",
        viewId: ViewportIds.main,
        placement: "top-right",
      }),
    ].filter((x) => enabledTools.includes(x.id as ToolId));

    return widgets;
  }, [imgMeta, enabledTools]);

  // need to change minimap size when viewsize changed
  const views = useMemo(() => {
    if (!viewSize) {
      return [];
    }

    const { base_x, base_y } = imgMeta;
    const center: [number, number] = [base_x / 2, base_y / 2];
    const minimapSize = calculateMinimapSize(base_x, base_y, viewSize, 0.2, 25);

    let views = [
      new OrthographicView({
        id: ViewportIds.main,
        controller: { scrollZoom: { speed: 0.004, smooth: true } },
      }),
    ];

    if (!isViewStateInitialized) {
      views = [];
    }

    if (enabledMiniMap) {
      views.push(
        new OrthographicView({
          id: ViewportIds.minimap,
          // use below to set bg for mini-map
          // clear: { color: [100, 100, 100, 255] },
          clear: true,
          y: minimapSize.y,
          x: minimapSize.x,
          width: minimapSize.width,
          height: minimapSize.height,
          controller: {
            scrollZoom: false,
            dragPan: false,
            doubleClickZoom: false,
            dragRotate: false,
          },
          viewState: {
            id: ViewportIds.minimap,
            zoom: minimapSize.zoom,
            target: center,
          },
        })
      );
    }
    return views;
  }, [viewSize, imgMeta, isViewStateInitialized, enabledMiniMap]);

  return (
    <>
      <DeckGL
        ref={deckRef}
        // getTooltip={tooltip}
        getCursor={(x) =>
          cursorRef.current || (x.isDragging ? "grabbing" : "pointer")
        }
        layerFilter={layerFilter}
        viewState={viewState}
        onResize={setViewSize}
        onDrag={onDrag}
        onClick={onDeckClick}
        onViewStateChange={onViewStateChange}
        views={views}
        // _onMetrics={console.log}
        style={{ backgroundColor: "black", ...style }}
        layers={layers}
        widgets={widgets}
      />
    </>
  );

  function centerToMiniMapClickPos(
    minimap: Viewport,
    pos: {
      x: number;
      y: number;
    }
  ) {
    const coordinate = minimap.unproject([
      pos.x - minimap.x,
      pos.y - minimap.y,
    ]);

    setViewState((preState) => {
      return {
        ...preState,
        [ViewportIds.main]: {
          ...preState[ViewportIds.main],
          target: coordinate as [number, number],
        },
      };
    });
  }
}

function clampToImgEdge(
  target: [number, number, number] | [number, number],
  baseX: number,
  baseY: number
) {
  target[0] = Math.max(0, Math.min(target[0], baseX));
  target[1] = Math.max(0, Math.min(target[1], baseY));
}

/**
 * 适配 viewsize 的 zoom
 */
function calculateZoomForViewSize(
  base_x: number,
  base_y: number,
  baseZoom: number,
  viewSizePct: number,
  viewSize: ViewSize
) {
  const x_scale = (viewSize.width * viewSizePct) / base_x;
  const y_scale = (viewSize.height * viewSizePct) / base_y;
  const zoom = baseZoom + Math.log2(Math.min(x_scale, y_scale));
  return zoom;
}

/**
 *
 * @param scale 1 means same as the base, and 1 means the largest resolution
 * @param scaleBaseZoom base zoom for scale
 * @returns
 */
function calculateZoomFromScale(scale: number, scaleBaseZoom: number) {
  if (scale > 0) {
    const zoomLevel = Math.log2(scale);
    return scaleBaseZoom + zoomLevel;
  } else {
    return scaleBaseZoom;
  }
}

function calculateMinimapSize(
  base_x: number,
  base_y: number,
  viewSize: ViewSize,
  scale: number,
  margin: number
): { width: number; height: number; zoom: number; x: number; y: number } {
  const widthHeightRatio = base_x / base_y;
  const xScale = (viewSize.width * scale) / base_x;
  const yScale = (viewSize.height * scale) / base_y;
  const chosenScale = Math.min(xScale, yScale);
  const zoom = Math.log2(chosenScale);

  let width, height;
  if (chosenScale == xScale) {
    width = base_x * chosenScale;
    height = width / widthHeightRatio;
  } else {
    height = base_y * chosenScale;
    width = height * widthHeightRatio;
  }

  const x = viewSize.width - margin - width;
  const y = margin;

  return { x, y, width, height, zoom };
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars -- might be used for debug
function tooltip(info: PickingInfo) {
  // console.log(info);
  const { zoom, scale, width, height } = info.viewport || {};
  const show = {
    screen_x: info.x,
    screen_y: info.y,
    coor: info.coordinate,
    zoom: zoom,
    scale: scale,
    viewport_width: width,
    viewport_height: height,
  };
  const show_str = JSON.stringify(show, null, 2);
  return `${show_str}`;
}
