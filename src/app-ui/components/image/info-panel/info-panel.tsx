"use client";

import ChannelSwitcher from "@/app-ui/components/image/channel-switcher/channel-switcher";
import OriginalMeta from "@/app-ui/components/image/meta/original-meta";
import { DrawInfo } from "@/app-ui/components/image/operation/draw-info";
import {
  imageSlice,
  InfoPannelType,
  selectCurrentInfoPannel,
} from "@/lib/app-store/image-slice";
import { useAppDispatch, useAppSelector } from "@/lib/app-store/store";

import styles from "@/app-ui/components/image/info-panel/info-panel.module.css";

import type { ImgMeta } from "@/lib/domain/img/query/svc";
import { Tabs, TabsProps } from "antd";

interface InfoPanelProps {
  imgMeta: ImgMeta;
  imgId: string;
  defaultShownChannelIds?: number[];
}

export default function InfoPanel({
  imgId,
  imgMeta,
  defaultShownChannelIds,
}: InfoPanelProps) {
  const dispatch = useAppDispatch();
  const currentInfoPannel = useAppSelector(selectCurrentInfoPannel);
  const items: TabsProps["items"] = [
    {
      key: "channel",
      label: "通道",
      children: (
        <ChannelSwitcher
          imgId={imgId}
          imgMeta={imgMeta}
          defaultShownChannelIds={defaultShownChannelIds}
        />
      ),
    },
    {
      key: "operation",
      label: "操作",
      children: <DrawInfo baseX={imgMeta.base_x} baseY={imgMeta.base_y} />,
    },
    {
      key: "original-meta",
      label: "元数据",
      children: <OriginalMeta imgId={imgId} />,
    },
  ];

  const onTabClick = (tabKey: string) => {
    dispatch(imageSlice.actions.setCurrentInfoPannel(tabKey as InfoPannelType));
  };

  return (
    <Tabs
      className={styles.infoPanel}
      activeKey={currentInfoPannel}
      onTabClick={(tabkey) => onTabClick(tabkey)}
      items={items}
    />
  );
}
