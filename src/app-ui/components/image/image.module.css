.btnActive {
  background: #efe5ff !important;
}

.btnEnabled {
  background: rgba(0, 0, 0, 0.12) !important;
}

.onlyShowIcon {
  visibility: hidden;
}

.channelSwitcher :global(.ant-table-cell-row-hover) .onlyShowIcon {
  visibility: visible;
}

.markedRow :global(.ant-table-cell) {
  background-color: #738eb440;
}

.channelSlider input {
  text-align: center;
}

.leftTopMenu :global(.ant-dropdown-menu-item) {
  padding: 0;
}
.leftTopMenu :global(.ant-btn) {
  padding: 15px 12px;
  width: 100%;
  justify-content: start;
  font-size: 14px;
}
