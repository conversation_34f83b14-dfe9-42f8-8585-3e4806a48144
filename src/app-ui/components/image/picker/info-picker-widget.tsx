import InfoPicker, {
  InfoPickerProps,
} from "@/app-ui/components/image/picker/info-picker";
import WidgetReactBase from "@/lib/widget/widget-react-base";
import { PickingInfo } from "@deck.gl/core";
import { FC } from "react";

export class InfoPickerWidget extends WidgetReactBase<InfoPickerProps> {
  inner: FC<InfoPickerProps> = InfoPicker;

  onHover(info: PickingInfo) {
    this.setProps({ pickingInfo: info as InfoPickerProps["pickingInfo"] });
    this.update();
  }
}
