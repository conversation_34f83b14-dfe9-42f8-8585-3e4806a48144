import IntensityLayer from "@/lib/layers/intensity-layer";
import { TileData } from "@/lib/layers/tile-layer";
import { TileLayerPickingInfo } from "@deck.gl/geo-layers";
import { useMemo } from "react";

export interface InfoPickerProps {
  pickingInfo?: TileLayerPickingInfo<TileData>;
}

interface DisplayInfo {
  x: number;
  y: number;
  intensity: number[];
}

function calculateInfo(
  pickingInfo: TileLayerPickingInfo<TileData>
): DisplayInfo | undefined {
  const { tile, coordinate, picked } = pickingInfo;
  if (picked && tile && coordinate) {
    const [x, y] = coordinate;
    const scale = Math.pow(2, tile.zoom);
    const [[minX, minY]] = tile.boundingBox;
    const xInRawByTile = Math.floor((x - minX) * scale);
    const yInRawByTile = Math.floor((y - minY) * scale);
    const xInRaw = Math.floor(x);
    const yInRaw = Math.floor(y);

    const intensity =
      tile.layers?.reduce<number[]>((acc, cur) => {
        if (cur instanceof IntensityLayer) {
          const intensity = cur.getRawValue(xInRawByTile, yInRawByTile);
          acc.push(intensity);
        }
        return acc;
      }, []) || [];
    return { x: xInRaw, y: yInRaw, intensity };
  } else {
    return;
  }
}

export default function InfoPicker({ pickingInfo }: InfoPickerProps) {
  const info = useMemo(() => {
    if (!pickingInfo) {
      return;
    }

    const info = calculateInfo(pickingInfo);
    return info;
  }, [pickingInfo]);

  return (
    <>
      <div
        style={{
          fontSize: "15px",
          color: "white",
        }}
      >
        {JSON.stringify(info)}
      </div>
    </>
  );
}
