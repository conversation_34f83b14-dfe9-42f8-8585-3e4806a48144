import type { Viewport } from "@deck.gl/core";
import { useMemo } from "react";

export interface ScaleBarProps {
  physicalSize?: number | null;
  physicalUnit?: string | null;
  viewport?: Viewport;
}

export default function ScaleBar(props: ScaleBarProps) {
  const [barLengthInScreenPixel, displayText] = useMemo(
    () => {
      const { physicalSize, physicalUnit, viewport } = props;
      let barLengthInScreenPixel, displayText;

      if (viewport) {
        const scale = viewport.scale;
        const widthInViewport = viewport.width;

        [barLengthInScreenPixel, displayText] = generateBarInfo(
          widthInViewport,
          scale,
          physicalSize,
          physicalUnit
        );
      }

      if (!barLengthInScreenPixel || isNaN(barLengthInScreenPixel)) {
        barLengthInScreenPixel = 100;
        displayText = "100 px";
      }
      return [barLengthInScreenPixel, displayText];
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      props.physicalSize,
      props.physicalUnit,
      props.viewport?.width,
      props.viewport?.scale,
    ]
  );

  return (
    <>
      <div
        style={{
          width: barLengthInScreenPixel + "px",
          height: "2px",
          background: "white",
          marginBottom: "30px",
          marginRight: "20px",
          fontSize: "15px",
          color: "white",
          textAlign: "end",
          textWrap: "nowrap",
        }}
      >
        {displayText}
      </div>
    </>
  );
}

const TARGETS = [1, 2, 2.5, 5, 10, 20, 25, 50, 100, 200, 250, 500];
const MIN_TARGET = TARGETS[0];
const MAX_TARGET = TARGETS[TARGETS.length - 1];

const SI_PREFIXES = [
  { symbol: "Y", exponent: 24 },
  { symbol: "Z", exponent: 21 },
  { symbol: "E", exponent: 18 },
  { symbol: "P", exponent: 15 },
  { symbol: "T", exponent: 12 },
  { symbol: "G", exponent: 9 },
  { symbol: "M", exponent: 6 },
  { symbol: "k", exponent: 3 },
  { symbol: "h", exponent: 2 },
  { symbol: "da", exponent: 1 },
  { symbol: "", exponent: 0 },
  { symbol: "d", exponent: -1 },
  { symbol: "c", exponent: -2 },
  { symbol: "m", exponent: -3 },
  { symbol: "µ", exponent: -6 },
  { symbol: "n", exponent: -9 },
  { symbol: "p", exponent: -12 },
  { symbol: "f", exponent: -15 },
  { symbol: "a", exponent: -18 },
  { symbol: "z", exponent: -21 },
  { symbol: "y", exponent: -24 },
];

function sizeToMeters(size: number, unit: string): number {
  if (!unit || unit === "m") {
    // Already in meters.
    return size;
  }
  if (unit.length > 1) {
    // We remove the trailing 'm' from the unit, so 'cm' becomes 'c' and 'dam' becomes 'da'.
    let unitPrefix = unit.substring(0, unit.length - 1);
    // Support 'u' as a prefix for micrometers.
    if (unitPrefix === "u") {
      unitPrefix = "µ";
    }
    const unitObj = SI_PREFIXES.find((p) => p.symbol === unitPrefix);
    if (unitObj) {
      return size * 10 ** unitObj.exponent;
    }
  }
  throw new Error("Received unknown unit");
}

function snapValue(value: number): [number, number, string] {
  let magnitude = 0;

  // for cm, we use m for cm which greater then 0.5
  if ((value < MIN_TARGET && value <= 0.5) || value > MAX_TARGET) {
    magnitude = Math.floor(Math.log10(value));
  }

  const snappedUnit =
    SI_PREFIXES.find(
      (p) =>
        p.exponent != 1 &&
        p.exponent != 2 &&
        p.exponent != -1 &&
        p.exponent <= magnitude
    ) || SI_PREFIXES[SI_PREFIXES.length - 1];

  const adjustedValue = value / 10 ** snappedUnit.exponent;

  const targetNewUnits = TARGETS.find((t) => t >= adjustedValue) || MAX_TARGET;

  const targetInMeters = targetNewUnits * 10 ** snappedUnit.exponent;

  return [targetInMeters, targetNewUnits, snappedUnit.symbol];
}

function generateBarInfo(
  widthInViewport: number,
  scale: number,
  physicalSize?: number | null,
  physicalUnit?: string | null
): [number, string] {
  const physicalSizeInMeters = sizeToMeters(
    physicalSize || 1,
    physicalUnit || "m"
  );
  const barLengthInScreen = widthInViewport * 0.05;
  const barLengthInWorld = barLengthInScreen / scale;
  const barNumberInMeters = barLengthInWorld * physicalSizeInMeters;

  const [barNumberSnappedInMeters, barNumberSnappedInNewUnit, newUnit] =
    snapValue(barNumberInMeters);

  const unitBasis = physicalUnit ? "m" : " pixel";

  const barLengthInScreenPixel =
    (barNumberSnappedInMeters / physicalSizeInMeters) * scale;
  const displayText = `${barNumberSnappedInNewUnit} ${newUnit}${unitBasis}`;
  return [barLengthInScreenPixel, displayText];
}
