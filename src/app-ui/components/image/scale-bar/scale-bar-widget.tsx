import ScaleBar, {
  ScaleBarProps,
} from "@/app-ui/components/image/scale-bar/scale-bar";
import WidgetReactBase from "@/lib/widget/widget-react-base";

import { Viewport } from "@deck.gl/core";
import { FC } from "react";

export class ScaleBarWidget extends WidgetReactBase<ScaleBarProps> {
  inner: FC<ScaleBarProps> = ScaleBar;
  onViewportChange(viewport: Viewport) {
    this.setProps({ viewport: viewport });
    this.update();
  }
}
