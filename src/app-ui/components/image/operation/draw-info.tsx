import {
  clampToBBox,
  getBBox,
  imageSlice,
  selectDrawFeatures,
} from "@/lib/app-store/image-slice";
import { useAppDispatch, useAppSelector } from "@/lib/app-store/store";
import { ImmutableFeatureCollection } from "@deck.gl-community/editable-layers";

import bboxPolygon from "@turf/bbox-polygon";
import { Button, Flex, InputNumber, Space, Typography } from "antd";
import * as _ from "es-toolkit";
import { useEffect, useState } from "react";

interface BBoxInput {
  x: number | null;
  y: number | null;
  w: number | null;
  h: number | null;
}

const EmptyBBox = { x: null, y: null, w: null, h: null };

export function DrawInfo({ baseX, baseY }: { baseX: number; baseY: number }) {
  const [bboxInput, setBBoxInput] = useState<BBoxInput>(EmptyBBox);
  const drawFeatures = useAppSelector(selectDrawFeatures);
  const dispatch = useAppDispatch();

  const hasFeature = drawFeatures.features.length > 0;

  useEffect(() => {
    const bboxFromFeature = getBBox(drawFeatures) || EmptyBBox;
    setBBoxInput(bboxFromFeature);
  }, [drawFeatures]);

  function changeFeatureByBBox(v: number | null, src: "x" | "y" | "w" | "h") {
    const copiedInput = { ...bboxInput };
    copiedInput[src] = v;
    const { x, y, w, h } = copiedInput;
    const bbox: [number, number, number, number] = [
      Number(x),
      Number(y),
      Number(x) + Number(w),
      Number(y) + Number(h),
    ];

    if (bbox.every((x) => !isNaN(x))) {
      const changedFeature = bboxPolygon(bbox);
      clampToBBox(changedFeature.geometry, baseX, baseY);
      if (hasFeature) {
        const updatedDrawFeature = new ImmutableFeatureCollection(drawFeatures)
          .replaceGeometry(0, changedFeature.geometry)
          .getObject();
        dispatch(imageSlice.actions.setDrawFeatures(updatedDrawFeature));
      }
    }
  }

  const cancelDrawInfo = () => {
    dispatch(imageSlice.actions.clearDrawFeatures());
  };

  const changeBBox = _.debounce(changeFeatureByBBox, 500);

  if (!hasFeature) {
    return <></>;
  }

  return (
    <>
      <Space direction="vertical">
        <Flex justify="space-between" align="center">
          <Typography.Text type="secondary">选区</Typography.Text>
          <Button type="link" onClick={() => cancelDrawInfo()}>
            取消
          </Button>
        </Flex>
        <Space align="end">
          <Space direction="vertical">
            <Space>
              <InputNumber
                addonBefore={"X"}
                value={bboxInput.x}
                onChange={(v) => changeBBox(v, "x")}
                controls={false}
              />
              <InputNumber
                addonBefore={"Y"}
                value={bboxInput.y}
                onChange={(v) => changeBBox(v, "y")}
                controls={false}
              />
            </Space>
            <Space>
              <InputNumber
                addonBefore={"W"}
                onChange={(v) => changeBBox(v, "w")}
                value={bboxInput.w}
                controls={false}
              />
              <InputNumber
                addonBefore={"H"}
                onChange={(v) => changeBBox(v, "h")}
                value={bboxInput.h}
                controls={false}
              />
            </Space>
          </Space>
          {/* <AiOutlineLock /> */}
        </Space>
      </Space>
    </>
  );
}
