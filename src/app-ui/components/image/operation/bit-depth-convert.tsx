import {
  getStepSettingFormInfo as getSaveAsStepSettingFormInfo,
  getStepOutputFormInfo,
  OutputFormNextTrigger,
  OutputFormFields as SaveAsOutputFormFields,
  SettingF<PERSON>Fields as SaveAsSettingFormFields,
  SaveAsStore,
  SettingFormNextTrigger,
} from "@/app-ui/components/image/operation/save-as";
import { getTaskResultStepGen } from "@/app-ui/components/image/operation/task-result-step";
import {
  FormItemInfo,
  FormSelect,
  IrsFormInfo,
} from "@/app-ui/components/utils/form";
import { ModalTrigger } from "@/app-ui/components/utils/modal-trigger";
import {
  createFormStepGen,
  IrsSteps,
  type StepFormUtils,
} from "@/app-ui/components/utils/step-form";
import {
  BitDepth,
  SupportedBitDepth,
} from "@/lib/apis/diverse/image-endpoints";
import { HelpLinks } from "@/lib/domain/common/constants";
import type { ImgInfo } from "@/lib/domain/img/query/svc";
import { TaskType } from "@/lib/domain/task/svc";
import IconBitDepthConvert from "@/public/<EMAIL>";
import IconQuestionMark from "@/public/<EMAIL>";
import { Button, Flex, Tooltip } from "antd";
import Image from "next/image";
import Link from "next/link";
import { ReactNode } from "react";

type OutputFormFields = SaveAsOutputFormFields & {};

type SettingFormFields = SaveAsSettingFormFields & {
  tgt_bit_depth: BitDepth;
};

function getStepSettingFormInfo({
  imgName,
  srcBitDepth,
}: {
  srcBitDepth: string;
  imgName: string;
}) {
  const saveAsStepFormInfo = getSaveAsStepSettingFormInfo<SettingFormFields>({
    imgName: imgName,
  });

  const formItems: FormItemInfo<SettingFormFields>[] = [
    ...saveAsStepFormInfo.formItems,
    {
      nameForLayout: "srcBitDepth",
      label: { name: "Current type", desc: "当前位深类型" },
      formItemProps: {
        children: srcBitDepth,
      },
    },
    {
      name: "tgt_bit_depth",
      label: { name: "Target type", desc: "目标位深类型" },
      formItemProps: {
        children: (
          <FormSelect
            options={SupportedBitDepth.map((x) => {
              return { value: x };
            })}
          />
        ),
      },
    },
  ];

  const stepFormInfo: IrsFormInfo<SettingFormFields> = {
    ...saveAsStepFormInfo,
    formItems: formItems,
    formProps: {
      ...saveAsStepFormInfo.formProps,
      initialValues: {
        ...saveAsStepFormInfo.formProps?.initialValues,
        tgt_bit_depth: "uint8",
      },
    },
  };

  return stepFormInfo;
}

function getSettingStepGen({ imgInfo }: { imgInfo: ImgInfo }) {
  const stepFormInfo = getStepSettingFormInfo({
    imgName: imgInfo.name,
    srcBitDepth: imgInfo.imgMeta.dtype,
  });

  const stepGen = createFormStepGen<SettingFormFields, SaveAsStore>({
    title: "参数设置",
    irsFormInfo: stepFormInfo,
    nextTrigger(options) {
      return <SettingFormNextTrigger {...options} imgInfo={imgInfo} />;
    },
  });

  return stepGen;
}

function getOutputStepGen({
  imgName,
  projectId,
}: {
  imgName: string;
  projectId: string;
}) {
  const stepFormInfo = (utils: StepFormUtils<SaveAsStore>) =>
    getStepOutputFormInfo({
      projectId: projectId,
      imgName: imgName,
      setting: utils.storeRef.current.setting,
    });

  const step = createFormStepGen<OutputFormFields, SaveAsStore>({
    title: "输出设置",
    irsFormInfo: stepFormInfo,
    nextTrigger(options) {
      return (
        <OutputFormNextTrigger
          {...options}
          projectId={projectId}
          taskType={TaskType.bit_depth_convert}
        />
      );
    },
  });

  return step;
}

function BitDepthConvertSteps({ imgInfo }: { imgInfo: ImgInfo }) {
  const { name: imgName, prjId: projectId } = imgInfo;

  const settingStepGen = getSettingStepGen({
    imgInfo: imgInfo,
  });

  const outputStepGen = getOutputStepGen({
    projectId: projectId,
    imgName: imgName,
  });

  const resultStepGen = getTaskResultStepGen({
    projectId: projectId,
  });

  const stepGens = [settingStepGen, outputStepGen, resultStepGen];

  return (
    <>
      <IrsSteps stepGens={stepGens} />
    </>
  );
}

function BitDepthConvertModalTrigger({
  children,
  imgInfo,
}: {
  children: ReactNode;
  imgInfo: ImgInfo;
}) {
  return (
    <ModalTrigger
      modalProps={{
        title: "Bit depth convert",
        children: <BitDepthConvertSteps imgInfo={imgInfo} />,
      }}
      trigger={children}
    />
  );
}

export function BitDepthConvertIcon({ imgInfo }: { imgInfo: ImgInfo }) {
  return (
    <Flex vertical align="center" justify="center">
      <BitDepthConvertModalTrigger imgInfo={imgInfo}>
        <Button
          color="default"
          variant="text"
          style={{ width: 54, height: 54 }}
          className="group content-center relative p-0"
        >
          <Image src={IconBitDepthConvert} alt="saturate" className="m-auto" />
          <Tooltip
            color="white"
            title={
              <Link
                href={HelpLinks.ImageOps}
                target="_blank"
                onClick={(e) => {
                  e.stopPropagation();
                }}
              >
                转换图像位深
              </Link>
            }
          >
            <Image
              src={IconQuestionMark}
              alt="question"
              className="absolute top-0 right-0 invisible group-hover:visible"
            />
          </Tooltip>
        </Button>
      </BitDepthConvertModalTrigger>

      <span className="text-wrap text-xs text-center" style={{ width: 54 }}>
        Bit depth convert
      </span>
    </Flex>
  );
}
