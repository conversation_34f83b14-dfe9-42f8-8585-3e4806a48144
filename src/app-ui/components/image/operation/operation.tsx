import { BitDepthConvertIcon } from "@/app-ui/components/image/operation/bit-depth-convert";
import { SaveAsIcon } from "@/app-ui/components/image/operation/save-as";
import { ImgInfo } from "@/lib/domain/img/query/svc";
import { Flex, Typography } from "antd";
import type { ReactNode } from "react";

interface Props {
  imgInfo: ImgInfo;
}

export default function ImageOperations({ imgInfo }: Props) {
  return (
    <Flex vertical className="w-full">
      <OperationSection sectionName={"Selection"}>
        <SaveAsIcon imgInfo={imgInfo} />
      </OperationSection>
      <OperationSection sectionName={"Image"}>
        <BitDepthConvertIcon imgInfo={imgInfo} />
      </OperationSection>
    </Flex>
  );
}

function OperationSection({
  children,
  sectionName,
}: {
  sectionName: string;
  children: ReactNode;
}) {
  return (
    <Flex
      vertical
      className="py-2 border-b border-solid last:border-b-0"
      gap={5}
    >
      <Typography.Text className="font-medium">{sectionName}</Typography.Text>
      <Flex wrap justify="start" gap={10}>
        {children}
      </Flex>
    </Flex>
  );
}
