import { StepGen } from "@/app-ui/components/utils/step-form";
import { paths } from "@/app/route-path";
import { Flex, StepProps, Typography } from "antd";
import Link from "next/link";

export function getTaskResultStepGen({
  projectId,
}: {
  projectId: string;
}): StepGen<{ taskId: string }> {
  return {
    stepPropsGen(stepUtils, stepIdx) {
      const renderDetail = stepUtils.currentStepIdx >= stepIdx;
      const taskId = stepUtils.storeRef.current.taskId;
      const taskLinkPath =
        taskId && paths.project.projectId(projectId).task.taskId(taskId).path;

      const stepProps: StepProps = {
        title: "执行",
        description: renderDetail && (
          <Flex justify="center" align="center" className="h-40">
            <Typography.Text type="secondary">
              执行中，完成后可在目标位置下查看转换结果，
            </Typography.Text>
            <Link
              href={{
                pathname: taskLinkPath,
              }}
              target="_blank"
            >
              查看任务详情
            </Link>
          </Flex>
        ),
      };

      return stepProps;
    },
  };
}
