"use client";
import InfoPanel from "@/app-ui/components/image/info-panel/info-panel";
import Viewer from "@/app-ui/components/image/viewer/viewer";
import Zoomer from "@/app-ui/components/image/zoomer";
import { paths } from "@/app/route-path";
import { diverseApi } from "@/lib/apis/diverse/api";
import { imageSlice } from "@/lib/app-store/image-slice";
import { useAppDispatch } from "@/lib/app-store/store";
import { App, Result, Skeleton, Typography } from "antd";
import Link from "next/link";
import { useEffect } from "react";

interface Props {
  imgId: string;
  channelId?: number;
}
export function ImagePreviewer({ imgId, channelId }: Props) {
  const { currentData: imgInfo, isError } =
    diverseApi.useGetImgMetaQuery(imgId);
  const dispatch = useAppDispatch();
  useEffect(() => {
    return () => {
      dispatch(imageSlice.actions.resetToInitialState());
    };
  }, [dispatch]);

  const defaultShownChannelIds =
    channelId === undefined ? channelId : [channelId];

  if (isError) {
    return (
      <Result
        className="w-full"
        title={"failed to open file"}
        status={"error"}
      />
    );
  }

  if (!imgInfo) {
    return <Skeleton active />;
  }
  const { name } = imgInfo;

  return (
    <>
      <div className="h-[70vh] flex flex-col">
        <div className="flex-none flex">
          <div className="flex-auto">
            <Typography.Text ellipsis={{ tooltip: true }} type="secondary">
              {name}
            </Typography.Text>
          </div>
          <div>
            <Link
              target="_blank"
              href={{ pathname: paths.bmx.itemId(imgId).path }}
            >
              弹出
            </Link>
          </div>
        </div>
        <div className="flex h-full overflow-auto">
          <div className="flex-auto min-w-3xl border-l relative m-2">
            <Viewer imgMeta={imgInfo.imgMeta} imgId={imgInfo.id} />
          </div>
          <div className="flex-none basis-[260px] border-l flex flex-col overflow-auto">
            <div className="p-2 flex justify-between">
              <Zoomer />
            </div>
            <div className="flex-auto border-t p-2 flex">
              <InfoPanel
                imgMeta={imgInfo.imgMeta}
                imgId={imgInfo.id}
                defaultShownChannelIds={defaultShownChannelIds}
              />
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export function PreviewBtn(props: Props) {
  const app = App.useApp();

  const preview = () => {
    app.modal.info({
      icon: null,
      footer: null,
      maskClosable: true,
      content: <ImagePreviewer {...props} />,
      width: "70%",
    });
  };

  return <Typography.Link onClick={preview}>预览</Typography.Link>;
}
