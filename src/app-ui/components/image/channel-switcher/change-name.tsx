import { useShowFormModal } from "@/app-ui/components/utils/show-form-modal";
import { type FormProps, Form, Input } from "antd";
import { throwIfSafeActionError, useHandleApiErrorDefault } from "@/lib/utils";
import { saveImgMeta } from "@/app-ui/actions/safe-actions/image";

interface FieldType {
  id: number;
  name: string;
}

export function useShowChangeChannelModal({ imgId }: { imgId: string }) {
  const handleApiErrorDefault = useHandleApiErrorDefault();
  const showModal = useShowFormModal<FieldType>({
    formFC: ChangeChannelMetaForm,
    modelConfig: { title: "修改 Channel 名称" },
    async doSubmit(formValues) {
      await saveImgMeta({
        id: imgId,
        channels: [{ ...formValues }],
      })
        .then(throwIfSafeActionError)
        .catch(handleApiErrorDefault);
    },
  });

  return showModal;
}

function ChangeChannelMetaForm(formProps: FormProps) {
  return (
    <Form<FieldType> className="p-4" name="change-channel-meta" {...formProps}>
      <Form.Item
        style={{ marginBottom: 2 }}
        name="name"
        rules={[
          {
            required: true,
            message: "支持英文大小写、数字、横杠、下划线，1~30位",
          },
        ]}
      >
        <Input
          placeholder={formProps?.initialValues?.name}
          maxLength={30}
          showCount
        />
      </Form.Item>
    </Form>
  );
}
