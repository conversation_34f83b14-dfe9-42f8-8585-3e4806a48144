import {
  Histogram,
  LogSwitcher,
} from "@/app-ui/components/image/channel-switcher/histogram";
import { RatioBtn } from "@/app-ui/components/image/channel-switcher/ratio";
import styles from "@/app-ui/components/image/image.module.css";
import { imageSlice, type ChannelView } from "@/lib/app-store/image-slice";
import { useAppDispatch } from "@/lib/app-store/store";
import type { ImgMeta } from "@/lib/domain/img/query/svc";
import { dtypeToRange } from "@/lib/utils";
import {
  Button,
  Flex,
  InputNumber,
  Slider,
  Space,
  Tooltip,
  Typography,
} from "antd";
import classnames from "clsx";
import { useState } from "react";

interface Props {
  channelView: ImgMeta["channels"][number];
  channelMeta: ImgMeta["channels"][number];
  imgMeta: ImgMeta;
  disabled: boolean;
  imgId: string;
}

export default function IntensitySlider({
  channelMeta,
  channelView,
  disabled,
  imgMeta,
  imgId,
}: Props) {
  const dispatch = useAppDispatch();
  const dtypeRange = dtypeToRange(imgMeta.dtype);
  const [showLogScale, setShowLogScale] = useState(false);

  const setChannelMinMax = (minMax: { min?: number; max?: number }) => {
    const payload: ChannelView = {
      id: channelMeta.id,
    };

    if (minMax.min != undefined) {
      payload.min = minMax.min;
    }
    if (minMax.max != undefined) {
      payload.max = minMax.max;
    }
    dispatch(imageSlice.actions.setChannelView(payload));
  };

  const setMin = (v: number | null) => {
    if (v != null && v < channelView.max) {
      setChannelMinMax({ min: v });
    }
  };

  const setMax = (v: number | null) => {
    if (v != null && v > channelView.min) {
      setChannelMinMax({ max: v });
    }
  };

  const setToDType = () => {
    const max = dtypeRange.max > 255 ? channelMeta.max : dtypeRange.max;
    setChannelMinMax({ min: dtypeRange.min, max: max });
  };

  const setToAuto = () => {
    setChannelMinMax({ min: channelMeta.min, max: channelMeta.max });
  };

  const sliderRangeMin = dtypeRange.min;
  const sliderRangeMax = channelMeta.max;
  const numberStep = dtypeRange.max > 1 ? 1 : 0.01;

  return (
    <Flex vertical className={classnames("w-full", styles.channelSlider)}>
      <Flex justify="space-between">
        <Typography.Text strong>
          {channelMeta.name} {disabled && "(hidden)"}
        </Typography.Text>
        <LogSwitcher setShowLogScale={setShowLogScale} />
      </Flex>
      <Histogram
        showLogScale={showLogScale}
        imgId={imgId}
        channelId={channelMeta.id}
        min={channelView.min}
        max={channelView.max}
        range={[channelMeta.min, channelMeta.max]}
      />

      <Slider
        disabled={disabled}
        className="mx-4"
        range
        tooltip={{ formatter: null }}
        styles={{ handle: { color: "red" } }}
        min={sliderRangeMin}
        max={sliderRangeMax}
        onChange={(range) =>
          setChannelMinMax({ min: range.at(0), max: range.at(1) })
        }
        value={[channelView.min, channelView.max]}
        step={numberStep}
      />

      <Flex justify="space-between" className="mx-1">
        <Space direction="vertical" align="center" style={{ rowGap: 0 }}>
          <InputNumber
            disabled={disabled}
            style={{ width: "80px" }}
            value={channelView.min}
            step={numberStep}
            onChange={setMin}
          />
          <span style={{ opacity: 0.6, color: "rgba(0, 0, 0, 0.45)" }}>
            min
          </span>
        </Space>
        <Space direction="vertical" align="center" style={{ rowGap: 0 }}>
          <InputNumber
            disabled={disabled}
            style={{ width: "80px" }}
            value={channelView.max}
            step={numberStep}
            onChange={setMax}
          />
          <span style={{ opacity: 0.6, color: "rgba(0, 0, 0, 0.45)" }}>
            max
          </span>
        </Space>
      </Flex>
      <Flex justify="space-between" className="mx-1">
        <RatioBtn
          btnProps={{ disabled: disabled }}
          afterGetMinMax={setChannelMinMax}
          imgMeta={imgMeta}
          imgId={imgId}
          channel_id={channelView.id}
          channel_name={channelView.name}
        />
        <Tooltip title={"设定强度值范围为 [min~max]"}>
          <Button disabled={disabled} onClick={setToAuto}>
            Auto
          </Button>
        </Tooltip>
        <Tooltip title={"设定强度值范围为 [0~max]"}>
          <Button disabled={disabled} onClick={setToDType}>
            Reset
          </Button>
        </Tooltip>
      </Flex>
    </Flex>
  );
}
