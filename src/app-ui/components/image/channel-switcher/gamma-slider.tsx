import { imageSlice, selectGamma } from "@/lib/app-store/image-slice";
import { useAppDispatch, useAppSelector } from "@/lib/app-store/store";
import type { ImgMeta } from "@/lib/domain/img/query/svc";
import { Flex, InputNumber, Slider, Typography } from "antd";
import { useCallback, useEffect } from "react";

export default function GammaSlider({ imgMeta }: { imgMeta: ImgMeta }) {
  const gamma = useAppSelector(selectGamma);
  const dispatch = useAppDispatch();
  const setGamma = useCallback(
    (value: number) => {
      if (Number.isNaN(value)) {
        return;
      }

      dispatch(imageSlice.actions.setGamma(value as number));
    },
    [dispatch]
  );

  useEffect(() => {
    setGamma(imgMeta.view_gamma ?? 1);
  }, [imgMeta.view_gamma, setGamma]);

  return (
    <>
      <Flex vertical className="w-full">
        <Typography.Text strong>Gamma</Typography.Text>
        <Flex>
          <Slider
            className="mx-4 w-full"
            min={0}
            max={5}
            tooltip={{ formatter: null }}
            onChange={setGamma}
            value={gamma}
            step={0.01}
          />
          <InputNumber
            min={0}
            max={5}
            step={0.01}
            value={gamma}
            controls={false}
            onChange={(v) => {
              if (v != null) {
                setGamma(v);
              }
            }}
          />
        </Flex>
      </Flex>
    </>
  );
}
