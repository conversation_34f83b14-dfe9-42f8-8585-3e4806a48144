"use client";

import { useShowChangeChannelModal } from "@/app-ui/components/image/channel-switcher/change-name";
import GammaSlider from "@/app-ui/components/image/channel-switcher/gamma-slider";
import IntensitySlider from "@/app-ui/components/image/channel-switcher/intensity-slider";
import styles from "@/app-ui/components/image/image.module.css";
import { IrsIcon } from "@/app-ui/components/utils/icon";
import {
  COLOR_PRESETS,
  imageSlice,
  selectGreyMode,
  selectMagFilter,
  selectShownChannelIds,
  useChannelMetaWithDefaultColor,
  useStateCurrentChannelMeta,
  useStateCurrentChannelView,
} from "@/lib/app-store/image-slice";
import { useAppDispatch, useAppSelector } from "@/lib/app-store/store";
import type { ImgMeta } from "@/lib/domain/img/query/svc";
import type { MagFilter } from "@/lib/layers/intensity-layer";
import { ColorPicker, Flex, Select, Space, Switch, Table } from "antd";
import { ColumnsType } from "antd/es/table";
import { RowSelectionType, TableRowSelection } from "antd/es/table/interface";
import React, { useCallback, useEffect, useState } from "react";

interface ChannelSwitcherProp {
  imgId: string;
  imgMeta: ImgMeta;
  defaultShownChannelIds?: number[];
}

type TableRecord = ImgMeta["channels"][number];

export default function ChannelSwitcher({
  imgMeta,
  imgId,
  defaultShownChannelIds,
}: ChannelSwitcherProp) {
  const dispatch = useAppDispatch();
  const showChangeChannelModal = useShowChangeChannelModal({
    imgId: imgId,
  });
  const channelMetas = imgMeta.channels;
  const selectedChannelView = useStateCurrentChannelView(channelMetas);
  const selectedChannelMeta = useStateCurrentChannelMeta(channelMetas);
  const selectedChannelId = selectedChannelMeta?.id;
  const channelMetasWithDefaultColor =
    useChannelMetaWithDefaultColor(channelMetas);

  const setSelectedChannelId = useCallback(
    (channelId?: number) => {
      dispatch(imageSlice.actions.setCurrentChannelId(channelId));
    },
    [dispatch]
  );

  const [selectionType, setSelectionType] =
    useState<RowSelectionType>("checkbox");

  const shownChannelIds = useAppSelector(selectShownChannelIds);
  const magFilter = useAppSelector(selectMagFilter);
  const [previousShownChannelIds, setPreviousShownChannelIds] = useState<
    React.Key[]
  >([]);

  const greyMode = useAppSelector(selectGreyMode);

  const setGreyMode = (greyMode: boolean) => {
    dispatch(imageSlice.actions.setGreyMode(greyMode));
  };

  const changeShownChannels = useCallback(
    (newSelectedRowKeys: React.Key[]) => {
      const sortedChannelIds = [...(newSelectedRowKeys as number[])].sort(
        (a, b) => a - b
      );
      dispatch(imageSlice.actions.setShownChannelIds(sortedChannelIds));
    },
    [dispatch]
  );

  const switchGreyScaleMode = (checked: boolean) => {
    const selectionType = checked ? "radio" : "checkbox";
    setSelectionType(selectionType);
    if (selectionType === "radio") {
      setPreviousShownChannelIds(shownChannelIds);
      let signleKeys: React.Key[];
      if (selectedChannelId !== undefined) {
        signleKeys = [selectedChannelId];
      } else {
        signleKeys = shownChannelIds.slice(0, 1);
        if (signleKeys.length === 0) {
          signleKeys = channelMetas.slice(0, 1).map((x) => x.id);
        }
      }
      changeShownChannels(signleKeys);
    } else {
      changeShownChannels(previousShownChannelIds);
    }
    setGreyMode(checked);
  };

  const setChannelColor = (id: number, hex: string) => {
    dispatch(imageSlice.actions.setChannelView({ id: id, color: hex }));
  };

  const setMagFilter = (magFilterValue: MagFilter) => {
    dispatch(imageSlice.actions.setMagFilter(magFilterValue));
  };

  // show all channels by-default,select the first 1
  useEffect(() => {
    const shownChannalIds =
      defaultShownChannelIds ||
      channelMetas.filter((x) => x.view_shown ?? true).map((x) => x.id);

    changeShownChannels(shownChannalIds);
    setSelectedChannelId(shownChannalIds.at(0));
  }, [
    changeShownChannels,
    setSelectedChannelId,
    channelMetas,
    defaultShownChannelIds,
  ]);

  const rowSelection: TableRowSelection<TableRecord> = {
    columnWidth: 60,
    type: selectionType,
    selectedRowKeys: shownChannelIds,
    columnTitle(checkboxNode) {
      return (
        <Space align="center">
          {checkboxNode}
          <p style={{ width: 16 }} />
        </Space>
      );
    },
    onChange: changeShownChannels,
    renderCell: (v, r, i, o) => {
      return (
        <Space align="center">
          {o}
          {greyMode || (
            <IrsIcon
              name="OnlyShow"
              className={styles.onlyShowIcon + " text-lg"}
              onClick={() => changeShownChannels([r.id])}
              style={{ cursor: "pointer" }}
            />
          )}
        </Space>
      );
    },
  };

  const columns: ColumnsType<TableRecord> = [
    {
      title: "Channel",
      dataIndex: "name",
      ellipsis: true,
      render: (v, r) => {
        return (
          <span
            onDoubleClick={() => {
              showChangeChannelModal({ name: r.name, id: r.id });
            }}
          >
            {v}
          </span>
        );
      },
    },
    {
      title: "Color",
      dataIndex: "color",
      align: "right",
      width: 60,
      render: (v, r) => {
        return (
          <ColorPicker
            defaultFormat="rgb"
            defaultValue={r.color}
            size="small"
            presets={[
              {
                label: "preset",
                colors: COLOR_PRESETS,
                defaultOpen: true,
              },
            ]}
            onChange={(_, hex) => setChannelColor(r.id, hex)}
          />
        );
      },
    },
  ];

  return (
    <Flex vertical className="h-full">
      <Flex className="pb-4" justify="space-between" rootClassName="opacity-45">
        <Space>
          渲染
          <Select
            size="small"
            value={magFilter}
            onChange={setMagFilter}
            options={[
              { value: "nearest", label: "Nearest" },
              { value: "linear", label: "Linear" },
            ]}
          />
        </Space>
        <Space>
          灰度
          <Switch size="small" onChange={switchGreyScaleMode} />
        </Space>
      </Flex>

      <Flex flex={"1 1 auto"} className="min-h-60 overflow-auto">
        <Table
          className={styles.channelSwitcher}
          columns={columns}
          rowSelection={rowSelection}
          dataSource={channelMetasWithDefaultColor}
          pagination={false}
          size="small"
          rowKey={"id"}
          rowClassName={(r) => {
            return r.id === selectedChannelId ? styles.markedRow : "";
          }}
          onRow={(r) => {
            return {
              onClick: () => {
                setSelectedChannelId(r.id);
              },
            };
          }}
        />
      </Flex>

      <Flex className="border-t border-solid py-2">
        <GammaSlider imgMeta={imgMeta} />
      </Flex>

      <Flex className="border-t border-solid py-2">
        {selectedChannelView && selectedChannelMeta && (
          <IntensitySlider
            imgId={imgId}
            channelMeta={selectedChannelMeta}
            channelView={selectedChannelView}
            disabled={!shownChannelIds.includes(selectedChannelView.id)}
            imgMeta={imgMeta}
          />
        )}
      </Flex>
    </Flex>
  );
}
