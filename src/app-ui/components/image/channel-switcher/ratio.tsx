import { FormLabel } from "@/app-ui/components/utils/form";
import { useShowFormModal } from "@/app-ui/components/utils/show-form-modal";
import { diverseApi } from "@/lib/apis/diverse/api";
import {
  GetMinMaxByQuantileReq,
  GetMinMaxByQuantileRes,
  RatioType,
} from "@/lib/apis/diverse/image-endpoints";
import type { ImgMeta } from "@/lib/domain/img/query/svc";
import {
  Button,
  Form,
  InputNumber,
  Radio,
  Space,
  Tooltip,
  Typography,
  type ButtonProps,
  type FormProps,
} from "antd";
import type { Rule } from "antd/es/form";

interface RatioFormFields {
  id: string;
  dtype: string;
  channel_id: number;
  channel_name: string;
  pixel_count: number;
  ratio_type: RatioType;
  min_ratio?: number;
  max_ratio?: number;
}

function RatioSetForm(formProps: FormProps) {
  const initialValues = formProps.initialValues as RatioFormFields;
  const ratioRule: Rule = ({ getFieldValue }) => ({
    async validator() {
      const min = getFieldValue("min_ratio");
      const max = getFieldValue("max_ratio");
      if (min == undefined && max == undefined) {
        return Promise.reject(new Error("至少填写一个区间比例"));
      }
    },
  });

  return (
    <Form<RatioFormFields>
      requiredMark={false}
      colon={false}
      name="ratio-set"
      labelCol={{ flex: "0 0 160px" }}
      labelAlign="left"
      labelWrap
      wrapperCol={{ flex: "auto" }}
      {...formProps}
    >
      <Form.Item<RatioFormFields>
        label={<FormLabel name={"Channel name"} desc="通道名称" />}
      >
        {initialValues?.channel_name}
      </Form.Item>
      <Form.Item<RatioFormFields>
        label={<FormLabel name={"Bit depth"} desc="位深" />}
      >
        {initialValues?.dtype}
      </Form.Item>
      <Form.Item<RatioFormFields>
        label={<FormLabel name={"Pixel count"} desc="像素总数" />}
      >
        {initialValues?.pixel_count}
      </Form.Item>

      <Form.Item<RatioFormFields>
        label={<FormLabel name={"Ratio type"} desc="设置比例类型" />}
        name="ratio_type"
        rules={[{ required: true, message: "设置比例类型" }]}
      >
        <Radio.Group>
          <Space direction="vertical">
            <Space direction="vertical">
              <Radio value={"intensity"}>Effective intensity</Radio>
              <Typography.Text type="secondary">
                使用有效强度比例，设置强度值
              </Typography.Text>
            </Space>
            <Space direction="vertical">
              <Radio value={"pixel"}>Pixels</Radio>
              <Typography.Text type="secondary">
                使用像素数量比例，设置强度值
              </Typography.Text>
            </Space>
          </Space>
        </Radio.Group>
      </Form.Item>

      <Form.Item<RatioFormFields>
        label={<FormLabel name={"Min range ratio"} desc="暗区间比例" />}
        name="min_ratio"
        dependencies={["max_ratio"]}
        rules={[ratioRule]}
      >
        <InputNumber addonAfter={"%"} min={0} max={100} />
      </Form.Item>

      <Form.Item<RatioFormFields>
        label={<FormLabel name={"Max range ratio"} desc="亮区间比例" />}
        name="max_ratio"
        dependencies={["min_ratio"]}
        rules={[ratioRule]}
      >
        <InputNumber addonAfter={"%"} min={0} max={100} />
      </Form.Item>
    </Form>
  );
}

function useShowRatioSetModal({
  initialValues,
  afterGetMinMax,
}: {
  initialValues: Partial<RatioFormFields>;
  afterGetMinMax: (value: GetMinMaxByQuantileRes) => void;
}) {
  const [getMinMaxByQuantile] = diverseApi.useLazyGetMinMaxByQuantileQuery();
  let canceled = false;
  const showModal = useShowFormModal<RatioFormFields>({
    formFC: RatioSetForm,
    formProps: { initialValues: initialValues },
    modelConfig: {
      title: "Ratio",
      maskClosable: true,
      width: 580,
      onCancel() {
        canceled = true;
      },
    },
    async doSubmit(formValues) {
      const req: GetMinMaxByQuantileReq = {
        id: formValues.id,
        channel_id: formValues.channel_id,
        ratio_type: formValues.ratio_type,
      };

      const min_quantile = formValues.min_ratio && formValues.min_ratio / 100;
      const max_quantile = formValues.max_ratio && formValues.max_ratio / 100;
      if (min_quantile != undefined) {
        req.min_quantile = min_quantile;
      }
      if (max_quantile != undefined) {
        req.max_quantile = max_quantile;
      }
      const res = await getMinMaxByQuantile(req).unwrap();

      if (!canceled) {
        afterGetMinMax(res);
      }
    },
  });

  return showModal;
}

export function RatioBtn({
  btnProps,
  afterGetMinMax,
  channel_name,
  imgMeta,
  channel_id,
  imgId,
}: {
  btnProps?: ButtonProps;
  imgMeta: ImgMeta;
  imgId: string;
  channel_id: number;
  channel_name: string;
  afterGetMinMax: (value: GetMinMaxByQuantileRes) => void;
}) {
  const showModal = useShowRatioSetModal({
    initialValues: {
      id: imgId,
      pixel_count: imgMeta.base_x * imgMeta.base_y,
      dtype: imgMeta.dtype,
      channel_id: channel_id,
      channel_name: channel_name,
      ratio_type: "intensity",
    },
    afterGetMinMax: afterGetMinMax,
  });

  return (
    <Tooltip title={"按比例自定义强度值范围"}>
      <Button
        onClick={() => {
          showModal();
        }}
        {...btnProps}
      >
        Ratio
      </Button>
    </Tooltip>
  );
}
