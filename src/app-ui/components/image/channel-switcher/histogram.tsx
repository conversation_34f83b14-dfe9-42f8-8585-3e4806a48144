import { IrsIcon } from "@/app-ui/components/utils/icon";
import { diverseApi } from "@/lib/apis/diverse/api";
import { Flex, Switch } from "antd";
import dynamic from "next/dynamic";

const Plot = dynamic(() => import("react-plotly.js"), { ssr: false });

export function Histogram({
  imgId,
  channelId,
  min,
  max,
  range,
  showLogScale,
}: {
  imgId: string;
  channelId: number;
  min: number;
  max: number;
  range: number[];
  showLogScale: boolean;
}) {
  const { data: getHistogramRes } = diverseApi.useGetHistogramQuery({
    itemId: imgId,
  });

  const histogram_by_channel = getHistogramRes?.channels.find(
    (x) => x.id == channelId
  );

  const bins = histogram_by_channel?.bins;
  const hist = histogram_by_channel?.hist;

  const plotData: Plotly.Data[] = [
    {
      type: "scatter",
      x: bins,
      y: hist,
      fill: "tonexty",
      fillcolor: "rgba(0, 0, 0, 0.25)",
      line: {
        color: "rgba(0, 0, 0, 0.25)",
      },
    },
  ];

  const plotLayout: Partial<Plotly.Layout> = {
    shapes: [
      {
        type: "line",
        x0: min,
        y0: 0,
        x1: min,
        y1: 1,
        yref: "paper",
        line: {
          color: "rgba(255, 77, 79, 1)",
          width: 1,
          dash: "dot",
        },
      },
      {
        type: "line",
        x0: max,
        y0: 0,
        x1: max,
        y1: 1,
        yref: "paper",
        line: {
          color: "rgba(255, 77, 79, 1)",
          width: 1,
          dash: "dot",
        },
      },
    ],
    margin: { t: 0, l: 0, r: 0, b: 0 },
    yaxis: {
      visible: false,
      ticklabelposition: "inside",
    },
    xaxis: {
      visible: false,
      // ticklabelposition: "inside",
      range: range,
    },
    showlegend: false,
  };

  if (showLogScale) {
    plotData.push({
      type: "scatter",
      x: bins,
      y: hist,
      fill: "tonexty",
      yaxis: "y2",
      fillcolor: "rgba(145, 202, 255, 0.5)",
      line: {
        color: "rgba(145, 202, 255, 0.5)",
      },
    });
    plotLayout.yaxis2 = {
      type: "log",
      visible: false,
      ticklabelposition: "inside",
      overlaying: "y",
    };
  }

  return (
    <>
      <Flex vertical className="mx-1">
        <Plot
          className="h-48 w-full"
          data={plotData}
          layout={plotLayout}
          config={{
            displayModeBar: false,
            staticPlot: true,
            responsive: true,
          }}
        />
      </Flex>
    </>
  );
}

export function LogSwitcher({
  setShowLogScale,
}: {
  setShowLogScale: (show: boolean) => void;
}) {
  const switchShowLog = (showLog: boolean) => {
    setShowLogScale(showLog);
  };
  return (
    <div className="flex flex-row items-center">
      <IrsIcon
        name="Log"
        style={{
          width: 22,
          fontSize: 22,
          lineHeight: "22px",
        }}
      />
      <Switch size="small" onChange={switchShowLog} className="opacity-45" />
    </div>
  );
}
