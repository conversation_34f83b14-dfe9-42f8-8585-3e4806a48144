import type { ImgMeta } from "@/lib/domain/img/query/svc";
import { dtypeToBit, humanFileSize } from "@/lib/utils";
import { Table, Typography } from "antd";
import { ColumnsType } from "antd/es/table";

type Axes = "X" | "Y" | "Z" | "C" | "T";

interface ImageMetaProps {
  imgMeta: ImgMeta;
  name: string;
}

interface TableRecord {
  name: string;
  value: string;
  copyable?: boolean;
}

function format_with_unit(size: number | undefined | null, unit: string | undefined | null) {
  unit = unit || "";
  if (!size) {
    return "";
  } else {
    return size + ` ${unit}`;
  }
}

function getAxesShape(imgMeta: ImgMeta, axis: Axes) {
  const axes = imgMeta.axes;
  const shape = imgMeta.shape;
  const axis_idx = axes.indexOf(axis);
  if (axis_idx === -1) {
    return;
  }

  return shape.at(axis_idx);
}

function formatAxesShape(imgMeta: ImgMeta, axis: Axes) {
  const shape = getAxesShape(imgMeta, axis);
  if (!shape) {
    return "1";
  }
  let phys_size: number | undefined;
  let phys_size_unit: string | undefined | null;
  if (axis === "X") {
    if (imgMeta.phys_x) {
      phys_size = imgMeta.phys_x * shape;
      phys_size_unit = imgMeta.phys_x_unit;
    }
  }

  if (axis === "Y") {
    if (imgMeta.phys_x) {
      phys_size = imgMeta.phys_x * shape;
      phys_size_unit = imgMeta.phys_y_unit;
    }
  }

  if (phys_size) {
    return `${shape} px (${phys_size}${phys_size_unit || ""})`;
  }
  return shape.toString();
}

function calculateUncompressedSize(imgMeta: ImgMeta) {
  const bitsPerPixel = dtypeToBit(imgMeta.dtype);
  const shapeProduct = imgMeta.shape.reduce((p, c) => p * c);

  return (shapeProduct * bitsPerPixel) / 8;
}

export default function ImageMeta({ imgMeta, name }: ImageMetaProps) {
  const columns: ColumnsType<TableRecord> = [
    { title: "Name", dataIndex: "name", ellipsis: true, width: 108 },
    {
      title: "Value",
      dataIndex: "value",
      ellipsis: true,
      render(v, r: TableRecord) {
        if (r.copyable && r.value) {
          return (
            <Typography.Text copyable ellipsis>
              {r.value}
            </Typography.Text>
          );
        }
        return r.value;
      },
    },
  ];

  const sizeReadable = humanFileSize(calculateUncompressedSize(imgMeta));
  const bitsPerPixel = dtypeToBit(imgMeta.dtype);

  const tbDs: TableRecord[] = [
    {
      name: "Title",
      value: name,
    },
    {
      name: "Size (Uncompressed)",
      value: sizeReadable,
    },
    {
      name: "Bits per pixel",
      value: String(bitsPerPixel),
    },
    {
      name: "Pixel type",
      value: imgMeta.dtype,
    },
    {
      name: "Dimension",
      value: imgMeta.axes,
    },
    {
      name: "Size X",
      value: formatAxesShape(imgMeta, "X"),
    },
    {
      name: "Size Y",
      value: formatAxesShape(imgMeta, "Y"),
    },
    {
      name: "Size C",
      value: formatAxesShape(imgMeta, "C"),
    },
    {
      name: "Size Z",
      value: formatAxesShape(imgMeta, "Z"),
    },
    {
      name: "Size T",
      value: formatAxesShape(imgMeta, "T"),
    },
    {
      name: "Pixel Width",
      copyable: true,
      value: format_with_unit(imgMeta.phys_x, imgMeta.phys_x_unit),
    },
    {
      name: "Pixel Height",
      copyable: true,
      value: format_with_unit(imgMeta.phys_y, imgMeta.phys_y_unit),
    },
    {
      name: "Voxel Depth",
      copyable: true,
      value: format_with_unit(imgMeta.phys_z, imgMeta.phys_z_unit),
    },
  ];

  return (
    <Table
      columns={columns}
      dataSource={tbDs}
      pagination={false}
      size="small"
      style={{ width: "100%" }}
      rowKey={"name"}
    />
  );
}
