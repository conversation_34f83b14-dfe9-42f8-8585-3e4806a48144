import { diverseApi } from "@/lib/apis/diverse/api";
import JsonView from "react18-json-view";
import "react18-json-view/src/style.css";

interface OriginalMetaProps {
  imgId: string;
}

export default function OriginalMeta({ imgId }: OriginalMetaProps) {
  const { currentData: imgOriginalMetaRes } =
    diverseApi.useGetImgOriginalMetaQuery(imgId);
  const meta = imgOriginalMetaRes?.meta || {};
  return (
    <JsonView
      src={meta}
      ignoreLargeArray
      collapsed={1}
      collapseStringMode="address"
      collapseStringsAfterLength={10}
      style={{ width: "max-content" }}
    />
  );
}
