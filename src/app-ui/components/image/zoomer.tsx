"use client";

import { imageSlice, selectScaleForShow } from "@/lib/app-store/image-slice";
import { useAppDispatch, useAppSelector } from "@/lib/app-store/store";
import { Button, ConfigProvider, Select } from "antd";
import { AiOutlineFullscreen, AiOutlineOneToOne } from "react-icons/ai";

const DefaultScaleList = [0.25, 0.5, 1, 2, 4];

function formatScale(scale: number) {
  return (
    "缩放至 " + Intl.NumberFormat("en-US", { style: "percent" }).format(scale)
  );
}

export default function Zoomer() {
  const dispatch = useAppDispatch();
  const scaleForShow = useAppSelector(selectScaleForShow);
  const scaleOptions = DefaultScaleList.map((scale) => {
    const option = {
      value: scale,
      label: formatScale(scale),
    };
    return option;
  });

  const onScaleSelect = (scale: number | "fit-screen") => {
    dispatch(imageSlice.actions.triggerSetScaleEvent({ scale: scale }));
  };

  return (
    <ConfigProvider theme={{ components: { Button: { fontSize: 24 } } }}>
      <Select
        showSearch
        value={scaleForShow}
        filterOption={false}
        style={{ width: 120 }}
        options={scaleOptions}
        onSelect={onScaleSelect}
        // this is only for selected lable
        labelRender={({ value }) => {
          const pct = formatScale(Number(value));
          return pct;
        }}
      />
      <Button
        onClick={() => onScaleSelect("fit-screen")}
        title="适配屏幕"
        type="text"
        icon={<AiOutlineFullscreen />}
      />
      <Button
        onClick={() => onScaleSelect(1)}
        title="原尺寸"
        type="text"
        icon={<AiOutlineOneToOne />}
      />
    </ConfigProvider>
  );
}
