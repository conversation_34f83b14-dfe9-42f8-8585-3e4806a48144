import {
  getStepSettingFormInfo as getSaveAsStepSettingFormInfo,
  getStepOutputFormInfo,
  OutputFormNextTrigger,
  SettingFormNextTrigger,
  type OutputFormFields as SaveAsOutputFormFields,
  type Setting<PERSON><PERSON>Fields as SaveAsSettingFormFields,
  type SaveAsStore,
} from "@/app-ui/components/image/operation/save-as";
import { getTaskResultStepGen } from "@/app-ui/components/image/operation/task-result-step";
import {
  FormSelect,
  type FormItemInfo,
  type IrsFormInfo,
} from "@/app-ui/components/utils/form";
import { ModalTrigger } from "@/app-ui/components/utils/modal-trigger";
import {
  createFormStepGen,
  IrsSteps,
  type StepFormUtils,
} from "@/app-ui/components/utils/step-form";
import { type ImgInfo } from "@/lib/domain/img/query/svc";
import { TaskType } from "@/lib/domain/task/svc";
import IconFlip from "@/public/<EMAIL>";
import { Button, Flex } from "antd";
import Image from "next/image";
import { type ReactNode } from "react";

type FlipDims = "X" | "Y" | "XY";

type OutputFormFields = SaveAsOutputFormFields & {};

type SettingFormFields = SaveAsSettingFormFields & {
  flip_dims: FlipDims;
};

function getStepSettingFormInfo({ imgName }: { imgName: string }) {
  const saveAsStepFormInfo = getSaveAsStepSettingFormInfo<SettingFormFields>({
    imgName: imgName,
  });

  const formItems: FormItemInfo<SettingFormFields>[] = [
    ...saveAsStepFormInfo.formItems,
    {
      name: "flip_dims",
      label: {
        name: "Flip type",
        desc: "设置翻转类型",
      },
      formItemProps: {
        children: (
          <FormSelect
            options={[
              {
                value: "X",
                label: "Horizontally",
                desc: "将图像左右镜像翻转",
              },
              {
                value: "Y",
                label: "Vertically",
                desc: "将图像上下镜像翻转",
              },
              {
                value: "XY",
                label: "Both",
                desc: "将图像上下左右镜像翻转",
              },
            ]}
          />
        ),
      },
    },
  ];

  const stepFormInfo: IrsFormInfo<SettingFormFields> = {
    ...saveAsStepFormInfo,
    formItems: formItems,
    formProps: {
      ...saveAsStepFormInfo.formProps,
      initialValues: {
        ...saveAsStepFormInfo.formProps?.initialValues,
        flip_dims: "X",
      },
    },
  };

  return stepFormInfo;
}

function getSettingStepGen({ imgInfo }: { imgInfo: ImgInfo }) {
  const stepFormInfo = getStepSettingFormInfo({
    imgName: imgInfo.name,
  });

  const stepGen = createFormStepGen<SettingFormFields, SaveAsStore>({
    title: "参数设置",
    irsFormInfo: stepFormInfo,
    nextTrigger(options) {
      return <SettingFormNextTrigger {...options} imgInfo={imgInfo} />;
    },
  });

  return stepGen;
}

function getOutputStepGen({
  imgName,
  projectId,
}: {
  imgName: string;
  projectId: string;
}) {
  const stepFormInfo = (utils: StepFormUtils<SaveAsStore>) =>
    getStepOutputFormInfo({
      projectId: projectId,
      imgName: imgName,
      setting: utils.storeRef.current.setting,
    });

  const stepGen = createFormStepGen<OutputFormFields, SaveAsStore>({
    title: "输出设置",
    irsFormInfo: stepFormInfo,
    nextTrigger(options) {
      return (
        <OutputFormNextTrigger
          {...options}
          projectId={projectId}
          taskType={TaskType.flip}
        />
      );
    },
  });

  return stepGen;
}

function FlipSteps({ imgInfo }: { imgInfo: ImgInfo }) {
  const { name: imgName, prjId: projectId } = imgInfo;

  const settingsStepGen = getSettingStepGen({
    imgInfo: imgInfo,
  });

  const outputStepGen = getOutputStepGen({
    imgName: imgName,
    projectId: projectId,
  });

  const resultStepGen = getTaskResultStepGen({
    projectId: projectId,
  });

  const stepGens = [settingsStepGen, outputStepGen, resultStepGen];

  return <IrsSteps stepGens={stepGens} />;
}

function FlipModalTrigger({
  children,
  imgInfo,
}: {
  children: ReactNode;
  imgInfo: ImgInfo;
}) {
  return (
    <ModalTrigger
      modalProps={{
        title: "Flip",
        children: <FlipSteps imgInfo={imgInfo} />,
      }}
      trigger={children}
    />
  );
}

export function FlipTransIcon({ imgInfo }: { imgInfo: ImgInfo }) {
  return (
    <Flex vertical align="center" justify="center">
      <FlipModalTrigger imgInfo={imgInfo}>
        <Button
          color="default"
          variant="text"
          style={{ width: 54, height: 54 }}
          className="group content-center relative p-0"
        >
          <Image src={IconFlip} alt="flip" className="m-auto" />
        </Button>
      </FlipModalTrigger>

      <span className="text-wrap text-xs text-center" style={{ width: 54 }}>
        Flip
      </span>
    </Flex>
  );
}
