import { ApplyTransIcon } from "@/app-ui/components/image/transformer/apply";
import { FlipTransIcon } from "@/app-ui/components/image/transformer/flip";
import { SaturateTransIcon } from "@/app-ui/components/image/transformer/saturate";
import { ImgInfo } from "@/lib/domain/img/query/svc";
import { Flex, Typography } from "antd";
import type { ReactNode } from "react";

interface ImageTransformerProps {
  imgInfo: ImgInfo;
}

export default function ImageTransformers({
  imgInfo: imgMetaRes,
}: ImageTransformerProps) {
  return (
    <Flex vertical className="w-full" gap={20}>
      <TransformerSection sectionName="Adjust contrast">
        <SaturateTransIcon imgInfo={imgMetaRes} />
        <ApplyTransIcon imgInfo={imgMetaRes} />
      </TransformerSection>

      <TransformerSection sectionName="Transform">
        <FlipTransIcon imgInfo={imgMetaRes} />
      </TransformerSection>
    </Flex>
  );
}
function TransformerSection({
  children,
  sectionName,
}: {
  sectionName: string;
  children: ReactNode;
}) {
  return (
    <Flex
      vertical
      className="py-2 border-b border-solid last:border-b-0"
      gap={5}
    >
      <Typography.Text className="font-medium">{sectionName}</Typography.Text>
      <Flex wrap justify="start" gap={10}>
        {children}
      </Flex>
    </Flex>
  );
}
