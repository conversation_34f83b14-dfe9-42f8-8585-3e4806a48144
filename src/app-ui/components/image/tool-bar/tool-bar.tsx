import styles from "@/app-ui/components/image/image.module.css";
import { SaveImageViewBtn } from "@/app-ui/components/image/tool-bar/save-btn";
import { ToolIcon } from "@/app-ui/components/image/tool-bar/tool-icon";
import { IrsIcon } from "@/app-ui/components/utils/icon";
import { ImgInfo } from "@/lib/domain/img/query/svc";
import { Button, Divider } from "antd";
import Text from "antd/es/typography/Text";
import { CiPickerEmpty } from "react-icons/ci";

export function ToolBar({ imgInfo }: { imgInfo: ImgInfo }) {
  return (
    <div className="flex h-full">
      <div className="flex flex-none items-center justify-center basis-[128px]">
        <LeftTopMenus imgInfo={imgInfo} />
      </div>
      <ToolsWithInfo imgId={imgInfo.id} name={imgInfo.name} />
    </div>
  );
}

function Tools() {
  return (
    <>
      <Button
        type="text"
        className={styles.btnActive}
        icon={<IrsIcon name="Hand" className="text-xl" />}
      />
      {/* <ToolIcon
        toolId="select-draw"
        icon={<IrsIcon name="Arrow" className="text-xl" />}
      /> */}
      <ToolIcon
        toolId="draw-rect"
        icon={<IrsIcon name="Square" className="text-xl" />}
      />
      <Divider type="vertical" style={{ borderColor: "rgba(5,5,5,0.2)" }} />
      <ToolIcon
        toolId="scale-bar"
        icon={<IrsIcon name="Ruler" className="text-xl" />}
      />
      <ToolIcon toolId="info-picker" icon={<CiPickerEmpty size={20} />} />
      <ToolIcon
        toolId="mini-map"
        icon={<IrsIcon name="Aim" className="text-xl" />}
      />
    </>
  );
}

export function ToolsWithInfo({
  name,
  imgId,
}: {
  name: string;
  imgId: string;
}) {
  return (
    <>
      <div className="flex-auto flex items-center">
        <div className="flex-none max-w-[270px] flex flex-col">
          <Text ellipsis={{ tooltip: true }} type="secondary">
            {name}
          </Text>
          <Text type="secondary" copyable>
            {imgId}
          </Text>
        </div>
        <div className="flex-auto flex justify-center items-center gap-2">
          <Tools />
        </div>
      </div>
    </>
  );
}

function LeftTopMenus({ imgInfo }: { imgInfo: ImgInfo }) {
  // const items: MenuProps["items"] = [
  //   {
  //     key: "save-as",
  //     label: <SaveAsMenu imgInfo={imgInfo} />,
  //   },
  // ];
  return (
    <>
      {/* <Dropdown
        menu={{ items }}
        trigger={["click"]}
        overlayClassName={styles.leftTopMenu}
      >
        <Button type="text" icon={<AiOutlineMenu size={20} />} />
      </Dropdown> */}

      <SaveImageViewBtn imgId={imgInfo.id} img_meta={imgInfo.imgMeta} />
    </>
  );
}
