"use client";

import styles from "@/app-ui/components/image/image.module.css";
import {
  ToolId,
  imageSlice,
  selectEnabledTools,
} from "@/lib/app-store/image-slice";
import { useAppDispatch, useAppSelector } from "@/lib/app-store/store";
import { Button } from "antd";
import { ReactNode } from "react";

export function ToolIcon({
  toolId,
  icon,
}: {
  toolId: ToolId;
  icon: ReactNode;
}) {
  const dispatch = useAppDispatch();
  const enabledTools = useAppSelector(selectEnabledTools);

  const onClick = () => {
    if (enabledTools.includes(toolId)) {
      dispatch(imageSlice.actions.disableTool(toolId));
    } else {
      dispatch(imageSlice.actions.enableTool(toolId));
    }
  };

  const classNmae = enabledTools.includes(toolId) ? styles.btnEnabled : "";

  return (
    <Button type="text" className={classNmae} icon={icon} onClick={onClick} />
  );
}
