"use client";

import InfoPanel from "@/app-ui/components/image/info-panel/info-panel";
import ImageMenuArea from "@/app-ui/components/image/menu-area";
import Viewer from "@/app-ui/components/image/viewer/viewer";
import Zoomer from "@/app-ui/components/image/zoomer";
import { ImgInfo } from "@/lib/domain/img/query/svc";

export function ImageEditor({ imgInfo }: { imgInfo: ImgInfo }) {
  return (
    <>
      <div className="flex h-full overflow-auto">
        <div className="flex-none basis-[270px] p-2">
          <ImageMenuArea imgInfo={imgInfo} />
        </div>
        <div className="flex-auto min-w-3xl border-l relative m-2">
          <Viewer imgMeta={imgInfo.imgMeta} imgId={imgInfo.id} />
        </div>
        <div className="flex-none basis-[260px] border-l flex flex-col overflow-auto">
          <div className="p-2 flex justify-between">
            <Zoomer />
          </div>
          <div className="flex-auto border-t p-2 flex">
            <InfoPanel imgMeta={imgInfo.imgMeta} imgId={imgInfo.id} />
          </div>
        </div>
      </div>
    </>
  );
}
