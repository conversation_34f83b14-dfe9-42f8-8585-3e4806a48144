"use client";

import ImageMeta from "@/app-ui/components/image/meta/image-meta";
import ImageOperations from "@/app-ui/components/image/operation/operation";
import ImageTransformers from "@/app-ui/components/image/transformer/transformer";
import { selectCurrentMenu } from "@/lib/app-store/image-slice";
import { useAppSelector } from "@/lib/app-store/store";
import { ImgInfo } from "@/lib/domain/img/query/svc";

interface ImageLeftSideProps {
  imgInfo: ImgInfo;
}

export default function ImageMenuArea({ imgInfo }: ImageLeftSideProps) {
  const currentMenu = useAppSelector(selectCurrentMenu);

  switch (currentMenu) {
    case "image-operation":
      return <ImageOperations imgInfo={imgInfo} />;
    case "image-edit":
      return <ImageTransformers imgInfo={imgInfo} />;
    case "image-meta":
      return <ImageMeta imgMeta={imgInfo.imgMeta} name={imgInfo.name} />;
  }
}
