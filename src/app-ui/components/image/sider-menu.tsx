"use client";

import { IrsIcon } from "@/app-ui/components/utils/icon";
import {
  imageSlice,
  MenuType,
  selectCurrentMenu,
} from "@/lib/app-store/image-slice";
import { useAppDispatch, useAppSelector } from "@/lib/app-store/store";

import { Menu } from "antd";
import { ItemType } from "antd/es/menu/interface";
import { MenuProps } from "antd/lib";
import { AiOutlineInfoCircle } from "react-icons/ai";

export function SiderMenu() {
  const dispatch = useAppDispatch();
  const currentMenu = useAppSelector(selectCurrentMenu);

  const items: ItemType[] = [
    {
      key: "image-operation",
      label: "功能",
      icon: <IrsIcon name="Operation" className="text-base" />,
    },
    {
      key: "image-edit",
      label: "编辑",
      icon: <IrsIcon name="Edit" className="text-base" />,
    },
    {
      key: "image-meta",
      label: "属性",
      icon: <AiOutlineInfoCircle className="text-base" />,
    },
  ];

  const onMenuSelect: MenuProps["onSelect"] = (info) => {
    dispatch(imageSlice.actions.setCurrentMenu(info.key as MenuType));
  };

  return (
    <div className="flex flex-auto">
      <Menu
        className="w-full"
        selectedKeys={[currentMenu]}
        items={items}
        onSelect={onMenuSelect}
      />
    </div>
  );
}
