"use client";

import { useRef, useState } from "react";
import { clsx } from "clsx";
import { App } from "antd";
import path from "path";
import Text from "antd/es/typography/Text";
import {
  diskSlice,
  getCurrentFolderDiskPath,
  useUpdateTreeData,
  type DiskTreeId,
} from "@/lib/app-store/disk-slice";
import { getUppy } from "@/lib/uppy";
import { useUppyEvent, useUppyState } from "@uppy/react";
import { useAppSelector } from "@/lib/app-store/store";
import { UploadListModal } from "@/app-ui/components/upload/upload-list-modal";
import { getParentFolderDiskPath } from "@/lib/domain/disk/svc";
import Uppy, { type Meta } from "@uppy/core";

export type UploaderType = "file" | "folder";

export type UploaderProps = {
  children: React.ReactNode;
  className?: string;
  rootClassName?: string;
  type?: UploaderType;
  projectId: string;
  treeId: DiskTreeId;
};

/**
 * @description
 * uppy has: State & LocalStorage
 * when uploading a file:
 * => if hit in state => do nothing
 * => if hit in localStorage => send a HEAD request to check
 * => else => send a POST request to upload
 */

export function Uploader({
  children,
  className,
  rootClassName,
  type = "file",
  projectId,
  treeId,
}: UploaderProps) {
  const inputRef = useRef<HTMLInputElement>(null);

  const [uppy] = useState(getUppy);
  const [modalOpen, setModalOpen] = useState(false);

  const activeNode = useAppSelector((s) =>
    diskSlice.selectors.selectActiveNode(s, treeId)
  );

  const updateTreeData = useUpdateTreeData();

  useUppyEvent(uppy, "upload-success", (file) => {
    if (file) {
      const relativePath = file.meta.relativePath as string;
      const baseFolderPath = getParentFolderDiskPath(relativePath);
      updateTreeData({
        projectId,
        treeId,
        generateUpdateKeys: (expandedKeys) => ({
          expandedKeys: [...expandedKeys, baseFolderPath],
          refreshKeys: [baseFolderPath],
        }),
      });
    }
  });

  const uploadingFileIDs = useUppyState(uppy, (state) => {
    const fileIds = Object.values(state.currentUploads).reduce(
      (pre: string[], { fileIDs }) => {
        return [...pre, ...fileIDs];
      },
      []
    );
    return Array.from(new Set(fileIds));
  });

  const isUploading = uploadingFileIDs.length > 0;

  const cancelUpload = useCancelUpload({ isUploading });

  const handleFilesInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files) return;
    uploadFiles([...files]);
  };

  const uploadFiles = (files: File[]) => {
    const basePathInPrj = activeNode
      ? getCurrentFolderDiskPath(activeNode.key)
      : "/";
    /**
     * ref: https://github.com/transloadit/uppy/blob/main/packages/%40uppy/utils/src/generateFileID.ts#L22
     * generate `meta.relativePath` to make sure fileId is unique in different folders.
     * if not, uppy will not add file and upload it because the fileId is already exists.
     */
    uppy.addFiles(
      files.map((file) => ({
        name: file.name,
        data: file,
        meta: {
          relativePath: path.join(
            basePathInPrj,
            file.webkitRelativePath || file.name
          ),
          projectId,
        },
      }))
    );

    uppy.upload();
    setModalOpen(true);

    if (inputRef.current) {
      inputRef.current.value = "";
    }
  };

  const handleCancel = () => {
    cancelUpload({
      uppy,
      closeModal: () => setModalOpen(false),
    });
  };

  return (
    <div className={rootClassName}>
      <div
        className={clsx("cursor-pointer", className)}
        onClick={() => inputRef.current?.click()}
      >
        {children}
      </div>
      <input
        ref={inputRef}
        type="file"
        multiple
        className="hidden"
        onChange={handleFilesInput}
        {...(type === "folder" && {
          webkitdirectory: "true",
          directory: "true",
        })}
      />

      <UploadListModal
        open={modalOpen}
        onCancel={handleCancel}
        onOk={() => setModalOpen(false)}
      />
    </div>
  );
}

export function useCancelUpload({ isUploading }: { isUploading: boolean }) {
  const app = App.useApp();

  const handleCancel = ({
    uppy,
    closeModal,
  }: {
    uppy: Uppy<Meta, Record<string, never>>;
    closeModal: () => void;
  }) => {
    if (isUploading) {
      app.modal.confirm({
        title: "取消上传",
        content: (
          <Text>
            文件上传中，若<Text type="danger"> 取消 </Text>
            上传，未上传成功的文件将会停止上传。请确认是否取消？
          </Text>
        ),
        onOk: () => {
          if (isUploading) {
            // check if upload completed when click
            uppy.pauseAll();
            uppy.clear();
            uppy.setState({
              currentUploads: {},
              files: {},
            });
          }
          closeModal();
        },
      });
    } else {
      closeModal();
    }
  };

  return handleCancel;
}
