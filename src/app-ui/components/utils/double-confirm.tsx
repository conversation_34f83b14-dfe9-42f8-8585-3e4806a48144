import { App, type ModalFuncProps } from "antd";

interface Config {
  first: ModalFuncProps;
  second: ModalFuncProps;
}

export function useDoubleConfirm({ first, second }: Config) {
  const app = App.useApp();
  return () => {
    app.modal.confirm({
      onOk: () => {
        app.modal.confirm({
          footer(_, { OkBtn, CancelBtn }) {
            return (
              <>
                <OkBtn />
                <CancelBtn />
              </>
            );
          },
          ...second,
        });
      },
      ...first,
    });
  };
}
