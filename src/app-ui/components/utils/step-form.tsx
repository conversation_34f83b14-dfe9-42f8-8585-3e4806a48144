import { Button, Form, FormInstance, StepProps, Steps } from "antd";
import { FormItemProps, FormProps } from "antd/lib";
import { ReactNode, RefObject, useRef, useState } from "react";

import {
  FormLabel,
  IrsForm,
  IrsFormInfo,
} from "@/app-ui/components/utils/form";
import { Typography } from "antd";
import { NamePath } from "antd/es/form/interface";
import { range } from "es-toolkit";

export type StepFormItemInfo<FormValues = unknown> = {
  name?: NamePath<FormValues>;
  label?: { name: string; desc?: string };
  formItemProps: FormItemProps<FormValues>;
  nameForLayout?: string;
};

export type StepFormInfo<FormValues = unknown> = {
  stepName: string;
  subTitle?: ReactNode;
  form: FormInstance<FormValues>;
  formProps?: FormProps<FormValues>;
  formItems: StepFormItemInfo<FormValues>[];
};

export function StepForm<FormValues>({
  subTitle,
  formItems,
  form,
  formProps,
  stepName,
}: StepFormInfo<FormValues>) {
  return (
    <>
      <Typography.Text type="secondary" className="text-wrap">
        {subTitle}
      </Typography.Text>
      <Form
        requiredMark={(node) => node}
        colon={false}
        className="py-4"
        name={stepName}
        labelCol={{ flex: "0 0 340px" }}
        labelAlign="left"
        labelWrap
        wrapperCol={{ flex: "1 0 400px" }}
        {...formProps}
        form={form}
      >
        {formItems.map((item) => {
          const keyForReact = item.name ? `${item.name}` : item.nameForLayout;
          if (!keyForReact) {
            throw new Error("name or nameForLayout is required");
          }

          const isFieldValue = !!item.name;

          return (
            <Form.Item
              key={keyForReact}
              name={item.name}
              label={item.label && <FormLabel {...item.label} />}
              rules={
                item.formItemProps.rules || [
                  {
                    required: isFieldValue,
                    message: item.label?.desc ?? item.label?.name,
                  },
                ]
              }
              {...item.formItemProps}
            />
          );
        })}
      </Form>
    </>
  );
}

export type StepUtils<Store = unknown> = {
  totalSteps: number;
  toPrevStep(): void;
  toNextStep(): void;
  storeRef: RefObject<Partial<Store>>;
  currentStepIdx: number;
  stepsDomRef: RefObject<HTMLDivElement | null>;
};

export type StepFormUtils<Store> = StepUtils<Store> & {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  getForm<Fields = any>(stepIdx: number): FormInstance<Fields>;
};

function useStepUtils<Store>({ totalSteps }: { totalSteps: number }) {
  const [currentStepIdx, setCurrentStepIdx] = useState(0);
  const storeRef = useRef<Partial<Store>>({});
  const stepsDomRef = useRef<HTMLDivElement>(null);

  const toPrevStep = () => {
    setCurrentStepIdx((step) => {
      return step - 1;
    });
  };

  const toNextStep = () => {
    setCurrentStepIdx((step) => {
      return step + 1;
    });
  };

  const utils: StepUtils<Store> = {
    totalSteps,
    toNextStep,
    toPrevStep,
    storeRef,
    currentStepIdx,
    stepsDomRef,
  };

  return utils;
}

function useStepFormUtils<Store>({ totalSteps }: { totalSteps: number }) {
  const stepUtils = useStepUtils<Store>({ totalSteps });
  const forms = range(totalSteps).map(() => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    return Form.useForm()[0];
  });

  const utils: StepFormUtils<Store> = {
    ...stepUtils,
    getForm(stepIdx) {
      return forms[stepIdx];
    },
  };

  return utils;
}

export type StepGen<Store> = {
  stepPropsGen: (
    stepFormUtils: StepFormUtils<Store>,
    stepIdx: number
  ) => StepProps;
  stepTriggerGen?: (
    stepFormUtils: StepFormUtils<Store>,
    stepIdx: number
  ) => ReactNode;
};

export function IrsSteps<Store>({ stepGens }: { stepGens: StepGen<Store>[] }) {
  const stepUtils = useStepFormUtils<Store>({ totalSteps: stepGens.length });
  const currentStepIdx = stepUtils.currentStepIdx;
  const currentStepGen = stepGens.at(currentStepIdx);
  const stepItems = stepGens.map((stepGen, stepIdx) =>
    stepGen.stepPropsGen(stepUtils, stepIdx)
  );
  const stepTrigger = currentStepGen?.stepTriggerGen?.(
    stepUtils,
    currentStepIdx
  );

  return (
    <>
      <div className="flex flex-col gap-5 min-h-0">
        <div className="flex-auto overflow-y-auto">
          <div ref={stepUtils.stepsDomRef}>
            <Steps
              direction="vertical"
              current={currentStepIdx}
              items={stepItems}
            />
          </div>
        </div>
        <div className="flex justify-end gap-4">{stepTrigger}</div>
      </div>
    </>
  );
}

export function NextStepBtn({
  stepIdx,
  stepUtils,
  onNext,
}: {
  stepIdx: number;
  stepUtils: StepUtils;
  onNext(): void | Promise<void>;
}) {
  const isSubmitStep = stepIdx == stepUtils.totalSteps - 2;
  const nextStepText = isSubmitStep ? "启动" : "下一步";

  return (
    <>
      <Button
        type="primary"
        onClick={async () => {
          await onNext();
          stepUtils.toNextStep();
        }}
      >
        {nextStepText}
      </Button>
    </>
  );
}

export function createFormStepGen<FormValues, Store>({
  title,
  irsFormInfo,
  nextTrigger,
}: {
  title: ReactNode;
  irsFormInfo:
    | IrsFormInfo<FormValues>
    | ((stepFormUtils: StepFormUtils<Store>) => IrsFormInfo<FormValues>);
  nextTrigger(options: {
    stepFormUtils: StepFormUtils<Store>;
    stepIdx: number;
  }): ReactNode;
}): StepGen<Store> {
  return {
    stepPropsGen(stepFormUtils, stepIdx) {
      const form = stepFormUtils.getForm(stepIdx);
      const showDetail = stepFormUtils.currentStepIdx >= stepIdx;
      const formDisabled = stepFormUtils.currentStepIdx !== stepIdx;
      const formInfo =
        typeof irsFormInfo === "function"
          ? irsFormInfo(stepFormUtils)
          : irsFormInfo;

      return {
        title: title,
        description: showDetail && (
          <IrsForm
            {...formInfo}
            formProps={{
              ...formInfo.formProps,
              disabled: formDisabled,
              form: form,
              name: `form-step-${stepIdx}`,
            }}
          />
        ),
      };
    },
    stepTriggerGen(stepFormUtils, stepIdx) {
      const form = stepFormUtils.getForm(stepIdx);
      const isFirstStep = stepIdx == 0;
      const showPrevStep = !isFirstStep;
      return (
        <>
          {showPrevStep && (
            <div
              onClick={async () => {
                form.resetFields();
                stepFormUtils.toPrevStep();
              }}
            >
              <Button>上一步</Button>
            </div>
          )}
          {nextTrigger({ stepFormUtils, stepIdx })}
        </>
      );
    },
  };
}
