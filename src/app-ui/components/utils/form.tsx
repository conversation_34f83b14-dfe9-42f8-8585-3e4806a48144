import {
  type SelectProps,
  Flex,
  Form,
  FormItemProps,
  FormProps,
  Select,
  Typography,
} from "antd";
import { NamePath } from "antd/es/form/interface";
import { ReactNode } from "react";

export function FormSelect(props: SelectProps) {
  const desc = props.options?.find((x) => x.value === props.value)?.desc;
  return (
    <>
      <Select {...props} />
      {desc && (
        <Typography.Text className="text-xs" type="secondary">
          {desc}
        </Typography.Text>
      )}
    </>
  );
}

export function FormLabel({ name, desc }: { name: string; desc?: string }) {
  return (
    <Flex vertical gap={"small"}>
      <Typography.Text>{name} :</Typography.Text>
      {desc && (
        <Typography.Text className="text-xs" type="secondary">
          {desc}
        </Typography.Text>
      )}
    </Flex>
  );
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type FormItemInfo<FormValues = any> = {
  name?: NamePath<FormValues>;
  label?: { name: string; desc?: string };
  formItemProps: FormItemProps<FormValues>;
  nameForLayout?: string;
};

export type IrsFormInfo<FormValues> = {
  formItems: FormItemInfo<FormValues>[];
  formProps?: FormProps<FormValues>;
  description?: ReactNode;
};

export function IrsForm<FormValues>({
  description,
  formItems,
  formProps,
}: IrsFormInfo<FormValues>) {
  return (
    <>
      {description}
      <Form
        requiredMark={(node) => node}
        colon={false}
        className="py-4"
        labelCol={{ flex: "0 0 340px" }}
        labelAlign="left"
        labelWrap
        wrapperCol={{ flex: "1 0 400px" }}
        {...formProps}
      >
        {formItems.map((item) => {
          const keyForReact = item.name ? `${item.name}` : item.nameForLayout;
          if (!keyForReact) {
            throw new Error("name or nameForLayout is required");
          }

          const isFieldValue = !!item.name;

          return (
            <Form.Item
              key={keyForReact}
              name={item.name}
              label={item.label && <FormLabel {...item.label} />}
              rules={
                item.formItemProps.rules || [
                  {
                    required: isFieldValue,
                    message: item.label?.desc ?? item.label?.name,
                  },
                ]
              }
              {...item.formItemProps}
            />
          );
        })}
      </Form>
    </>
  );
}
