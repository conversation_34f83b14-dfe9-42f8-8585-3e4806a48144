import { Modal, ModalProps } from "antd";
import { useState, type ReactNode } from "react";

type ModalUtils = {
  showModal(): void;
  closeModal(): void;
  modalOpenState: boolean;
};

function useModalUtils() {
  const [modalOpenState, setModalOpenState] = useState(false);

  const showModal = () => {
    setModalOpenState(true);
  };

  const closeModal = () => {
    setModalOpenState(false);
  };

  const modalUtils: ModalUtils = {
    showModal,
    closeModal,
    modalOpenState,
  };

  return modalUtils;
}

export function ModalTrigger({
  modalProps,
  trigger,
}: {
  modalProps: ModalProps;
  trigger: ReactNode;
}) {
  const modalUtils = useModalUtils();
  const classNames = {
    ...modalProps.classNames,
    body: modalProps.classNames?.body ?? "max-h-[75vh] flex flex-col",
  };
  return (
    <>
      <Modal
        width={800}
        footer={null}
        destroyOnHidden
        open={modalUtils.modalOpenState}
        onCancel={() => {
          modalUtils.closeModal();
        }}
        {...modalProps}
        classNames={classNames}
      />

      <div
        onClick={() => {
          modalUtils.showModal();
        }}
      >
        {trigger}
      </div>
    </>
  );
}
