"use client";

import { ChannelToolSteps } from "@/app-ui/components/app/channel-tool/steps";
import ChannelToolTitle from "@/app-ui/components/app/channel-tool/title";
import { RegisterSteps } from "@/app-ui/components/app/register/steps";
import RegisterTitle from "@/app-ui/components/app/register/title";
import { paths } from "@/app/route-path";
import { App, Button } from "antd";
import { useRouter } from "next/navigation";
import React, { ReactNode, useRef, useState } from "react";

export type StepHandler = {
  saveStep(stepIdx: number): Promise<void>;
  submitAll(): Promise<{ taskId: string }>;
};

export type StepsComponentProps = {
  stepIdx: number;
  className?: string;
  initFormData?: unknown;
  ref?: React.Ref<StepHandler>;
  projectId: string;
};

type StepsComponent = React.FC<StepsComponentProps>;

export type AppType = "register" | "channel-tool";

export function AppCreate({
  appType,
  projectId,
}: {
  appType: AppType;
  projectId: string;
}) {
  const router = useRouter();
  const app = App.useApp();
  const [stepIdx, setStepIdx] = useState(0);
  const stepsRef = useRef<StepHandler>(null);

  let StepsFC: StepsComponent;
  let submitStepIdx: number;
  let appTitle: ReactNode;

  switch (appType) {
    case "register":
      StepsFC = RegisterSteps;
      appTitle = <RegisterTitle />;
      submitStepIdx = 2;
      break;
    case "channel-tool":
      StepsFC = ChannelToolSteps;
      submitStepIdx = 2;
      appTitle = <ChannelToolTitle />;
      break;
    default:
      throw new Error("Unknown app type");
  }

  const submitAllSteps = stepIdx == submitStepIdx;
  const prevStep = () => {
    app.modal.confirm({
      title: "注意",
      content: "回到上一步后，不会保留当前参数，请确认是否返回？",
      onOk: () => {
        setStepIdx((step) => {
          return Math.max(0, step - 1);
        });
      },
      maskClosable: true,
    });
  };

  const nextStep = async () => {
    await stepsRef.current?.saveStep(stepIdx);
    if (submitAllSteps) {
      app.modal.confirm({
        title: "确认",
        content: "请确认是否按照当前配置执行操作？",
        onOk: async () => {
          const res = await stepsRef.current?.submitAll();
          if (res) {
            router.replace(
              paths.project.projectId(projectId).task.taskId(res.taskId).path
            );
          }
        },
      });
    } else {
      setStepIdx((step) => {
        return Math.min(submitStepIdx, step + 1);
      });
    }
  };

  return (
    <>
      <div className="flex h-full flex-col">
        <div>{appTitle}</div>
        <div className="border-t p-6 flex-auto">
          <StepsFC
            className="max-w-(--breakpoint-lg)"
            stepIdx={stepIdx}
            ref={stepsRef}
            projectId={projectId}
          />
        </div>

        <div className="border-t py-6 flex items-center justify-around">
          {stepIdx !== 0 && (
            <Button
              className="px-14"
              size="large"
              type="default"
              disabled={stepIdx === 0}
              onClick={prevStep}
            >
              上一步
            </Button>
          )}

          <Button
            className="px-14"
            size="large"
            type="primary"
            onClick={nextStep}
          >
            {submitAllSteps ? "启动" : "下一步"}
          </Button>
        </div>
      </div>
    </>
  );
}
