import { HelpLinks } from "@/lib/domain/common/constants";
import IconChannelApp from "@/public/<EMAIL>";
import { Button, Flex, Space } from "antd";
import Text from "antd/es/typography/Text";
import Image from "next/image";
import { AiFillFileText } from "react-icons/ai";

export default function ChannelToolTitle() {
  return (
    <>
      <Flex align="center" gap={16} className="p-4">
        <Flex flex={"0 0 auto"}>
          <Image src={IconChannelApp} alt="merge-channels" className="m-auto" />
        </Flex>
        <Flex vertical style={{ height: "100%" }} justify="space-around">
          <Space>
            <Text className="text-xl">通道管理</Text>
            <Button
              type="link"
              icon={<AiFillFileText />}
              href={HelpLinks.ChannelApp}
              target="_blank"
            >
              使用说明
            </Button>
          </Space>
          <Text type="secondary">
            将若干相同尺寸的图像，按需进行无损合成等操作，得到一张新的多通道图像。
          </Text>
        </Flex>
      </Flex>
    </>
  );
}
