import { FolderSelector } from "@/app-ui/components/item/folder-selector";
import {
  StepForm,
  type StepFormInfo,
} from "@/app-ui/components/utils/step-form";
import { FormInstance, Input, type FormProps } from "antd";
import { useEffect } from "react";

interface InputImg {
  id: string;
  name: string;
}

interface Props {
  projectId: string;
  form: FormInstance;
  disabled: boolean;
  inputImgs: InputImg[];
  initialValues?: FormProps["initialValues"];
}

export default function StepChooseOutput({
  form,
  disabled,
  inputImgs,
  initialValues,
  projectId,
}: Props) {
  if (!initialValues) {
    const defaultMergedName = inputImgs.at(0)?.name.replace(".irs", "-Merge");

    initialValues = {
      outputImgName: defaultMergedName,
    };
  }

  useEffect(() => {
    form.resetFields();
  }, [form]);

  const stepFormInfo: StepFormInfo = {
    stepName: "choose-output",
    subTitle: "选择存储位置以及融合图名称",
    formItems: [
      {
        name: "outputImgName",
        label: { name: "融合图名称" },
        formItemProps: {
          children: <Input addonAfter=".irs" placeholder="融合图名称" />,
          rules: [
            {
              required: true,
              message: "请输入30个中文字符以内的名称，支持中英文、数字、符号等",
              max: 30,
            },
          ],
        },
      },
      {
        name: "outputFolder",
        label: { name: "存储位置", desc: "请选择结果存储的文件夹" },
        formItemProps: {
          children: <FolderSelector prjId={projectId} />,
        },
      },
    ],
    formProps: {
      disabled: disabled,
      initialValues: initialValues,
    },
    form: form,
  };

  return <StepForm {...stepFormInfo} />;
}
