import {
  StepForm,
  type StepFormInfo,
} from "@/app-ui/components/utils/step-form";
import { diverseApi } from "@/lib/apis/diverse/api";
import {
  Checkbox,
  Flex,
  Form,
  FormInstance,
  Input,
  Skeleton,
  Typography,
  type FormProps,
} from "antd";
import useFormInstance from "antd/es/form/hooks/useFormInstance";
import { useEffect, useState } from "react";

interface InputImg {
  id: string;
  name: string;
}

interface ChannelOption {
  label: string;
  value: number;
}

interface Props {
  form: FormInstance;
  disabled: boolean;
  inputImgs: InputImg[];
  initialValues?: FormProps["initialValues"];
}

const ImgInfosNamePath = ["imgInfos"];

export default function StepChooseChannels({
  form,
  disabled,
  inputImgs,
  initialValues,
}: Props) {
  useEffect(() => {
    form.resetFields();
  }, [form]);

  const stepFormInfo: StepFormInfo = {
    stepName: "choose-channels",
    subTitle: "按需勾选融合图像通道",
    formItems: [
      {
        name: "mergeChannels",
        label: { name: "融合通道" },
        formItemProps: {
          children: <MergeChannelChooser inputImgs={inputImgs} />,
          rules: [
            {
              required: true,
              async validator(rule, value) {
                const selected = Object.values(value || {}).flat().length;
                if (selected === 0) {
                  return Promise.reject(new Error("至少选择一个融合通道"));
                }

                const imgInfos = form.getFieldValue(ImgInfosNamePath);
                const mergeImgDims = Object.entries(value)
                  .filter(([imgId, channels]) => {
                    return (channels as [])?.length > 0;
                  })
                  .map(([imgId, channels]) => {
                    const imgInfo = imgInfos[imgId];
                    return `${imgInfo.imgX} * ${imgInfo.imgY}`;
                  });

                if (new Set(mergeImgDims).size > 1) {
                  return Promise.reject(
                    new Error("所选融合通道的图像分辨率不一致")
                  );
                }
              },
            },
          ],
        },
      },
      {
        name: ImgInfosNamePath,
        formItemProps: {
          children: <Input type="hidden" />,
          hidden: true,
        },
      },
    ],
    formProps: {
      disabled: disabled,
      initialValues: initialValues,
    },
    form: form,
  };

  return <StepForm {...stepFormInfo} />;
}

function MergeChannelChooser({ inputImgs }: { inputImgs: InputImg[] }) {
  return (
    <Flex vertical>
      {inputImgs.map((img) => {
        return <ChannelChooser key={img.id} img={img} />;
      })}
    </Flex>
  );
}

function ChannelChooser({ img }: { img: InputImg }) {
  const imgId = img.id;
  const form = useFormInstance();
  const [imgInfo, setImgInfo] = useState(() => {
    return form.getFieldValue(ImgInfosNamePath.concat([imgId]));
  });

  const [getImgMeta] = diverseApi.useLazyGetImgMetaQuery();

  useEffect(() => {
    if (!imgInfo) {
      getImgMeta(imgId)
        .unwrap()
        .then((res) => {
          const imgX = res.imgMeta.base_x;
          const imgY = res.imgMeta.base_y;
          const channelOptions = res.imgMeta.channels.map((x) => {
            return {
              label: x.name,
              value: x.id,
            };
          });
          const allChannels = channelOptions.map((x) => x.value);
          form.setFieldValue(["mergeChannels", imgId], allChannels);
          const loadedImgInfo = {
            channelOptions,
            imgX,
            imgY,
          };
          form.setFieldValue(ImgInfosNamePath.concat([imgId]), loadedImgInfo);
          setImgInfo(loadedImgInfo);
        });
    }
  }, [form, getImgMeta, imgId, imgInfo]);

  return (
    <Flex vertical>
      <Typography.Text>{img.name}</Typography.Text>
      <Skeleton loading={!imgInfo}>
        <Typography.Text type="secondary">
          分辨率： {imgInfo?.imgX} * {imgInfo?.imgY}
        </Typography.Text>
        <ChannelGroup imgId={imgId} channelOptions={imgInfo?.channelOptions} />
      </Skeleton>
    </Flex>
  );
}

function ChannelGroup({
  imgId,
  channelOptions,
}: {
  imgId: string;
  channelOptions: ChannelOption[];
}) {
  return (
    <Form.Item
      name={["mergeChannels", imgId]}
      rules={[{ required: false, message: "至少选择 1 个通道" }]}
    >
      <Checkbox.Group className="flex-col gap-2" options={channelOptions} />
    </Form.Item>
  );
}
