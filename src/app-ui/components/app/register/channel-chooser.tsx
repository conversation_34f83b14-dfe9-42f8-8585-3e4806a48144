import { PreviewBtn } from "@/app-ui/components/image/previewer";
import { diverseApi } from "@/lib/apis/diverse/api";

import { useHandleApiErrorDefault } from "@/lib/utils";
import { Form, Select, Table, Typography } from "antd";
import { useWatch } from "antd/es/form/Form";
import { DefaultOptionType } from "antd/es/select";
import type { SelectProps } from "antd/lib";
import { useState } from "react";

interface InputImg {
  id: string;
  name: string;
}

export const NonParamsRegChannelsNamePath = ["nonParams", "regChannels"];

export function RegisterChannelChooser(props: {
  inputImgs: InputImg[];
  fixedImgKey: string;
  onLoadChannels?: (channelsByImg: {
    imgId: string;
    channels: ChannelOption[];
  }) => void;
}) {
  const regChannels = useWatch(NonParamsRegChannelsNamePath);

  return (
    <Table
      columns={[
        {
          title: "类型",
          dataIndex: "id",
          render: (v, r) => {
            return v === props.fixedImgKey ? (
              <Typography.Text strong>Fixed</Typography.Text>
            ) : props.fixedImgKey ? (
              "Moving"
            ) : (
              "-"
            );
          },
          width: 80,
        },
        {
          title: "图像",
          dataIndex: "name",
          ellipsis: true,
        },
        {
          title: "配准通道",
          dataIndex: "id",
          width: 140,
          render: (imgId, _) => {
            return (
              <Form.Item
                noStyle
                name={NonParamsRegChannelsNamePath.concat([imgId])}
                getValueFromEvent={(v) => ({ label: v.label, value: v.value })}
                rules={[{ required: true, message: <span key={imgId}></span> }]}
              >
                <IrsChannelOptions
                  imgId={imgId}
                  onLoadOptions={(channels) =>
                    props.onLoadChannels?.({ imgId: imgId, channels })
                  }
                />
              </Form.Item>
            );
          },
        },
        {
          title: "操作",
          dataIndex: "id",
          width: 80,
          render: (v, r, i) => {
            const shownChannelId = regChannels?.[r.id]?.value;
            return <PreviewBtn imgId={r.id} channelId={shownChannelId} />;
          },
        },
      ]}
      dataSource={props.inputImgs}
      pagination={false}
      size="small"
      rowKey={"id"}
    />
  );
}

interface ChannelOption {
  label: string;
  value: number;
}

function IrsChannelOptions({
  imgId,
  onLoadOptions,
  ...rests
}: {
  imgId: string;
  onLoadOptions?: (channels: ChannelOption[]) => void;
} & SelectProps<ChannelOption>) {
  const [getImgMeta] = diverseApi.useLazyGetImgMetaQuery();
  const [options, setOptions] = useState<DefaultOptionType[]>([]);
  const handleApiErrorDefault = useHandleApiErrorDefault();
  return (
    <Select
      labelInValue
      options={options}
      onOpenChange={async (open) => {
        if (open && options.length === 0) {
          const imgMeta = await getImgMeta(imgId)
            .unwrap()
            .catch(handleApiErrorDefault);
          const channelOptions: ChannelOption[] = imgMeta.imgMeta.channels.map(
            (x) => {
              return {
                label: x.name,
                value: x.id,
              };
            }
          );
          onLoadOptions?.(channelOptions);
          setOptions(channelOptions);
        }
      }}
      {...rests}
    />
  );
}
