import { IrsSelector } from "@/app-ui/components/item/irs-selector";
import {
  StepForm,
  type StepFormInfo,
} from "@/app-ui/components/utils/step-form";
import { FormInstance } from "antd";
import type { FormProps } from "antd/lib";

interface Props {
  form: FormInstance;
  disabled: boolean;
  initialValues?: FormProps["initialValues"];
}

export default function StepChooseInput({
  form,
  disabled,
  initialValues,
}: Props) {
  const stepFormInfo: StepFormInfo = {
    stepName: "choose-input",
    subTitle: "选择用于配准的图像以及配准结果存储位置",
    formItems: [
      {
        name: "inputImgs",
        label: {
          name: "配准图像",
          desc: "请选择添加用于配准的图像，仅支持irs格式",
        },
        formItemProps: {
          rules: [
            {
              type: "array",
              required: true,
              message: "请选择添加用于配准的图像，仅支持irs格式",
            },
          ],
          children: (
            <>
              <IrsSelector disabled={disabled} />
            </>
          ),
        },
      },
    ],
    formProps: {
      disabled: disabled,
      initialValues: initialValues,
    },
    form: form,
  };

  return <StepForm {...stepFormInfo} />;
}
