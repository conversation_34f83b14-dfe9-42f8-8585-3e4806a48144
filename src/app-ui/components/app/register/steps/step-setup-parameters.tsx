import {
  NonParamsRegChannelsNamePath,
  RegisterChannelChooser,
} from "@/app-ui/components/app/register/channel-chooser";
import { FormSelect } from "@/app-ui/components/utils/form";
import {
  StepForm,
  StepFormInfo,
  StepFormItemInfo,
} from "@/app-ui/components/utils/step-form";
import { HelpLinks } from "@/lib/domain/common/constants";
import { FormInstance, Input, InputNumber, Typography } from "antd";
import { useWatch } from "antd/es/form/Form";
import type { FormProps } from "antd/lib";
import Link from "next/link";
import { useEffect, useMemo } from "react";

interface InputImg {
  id: string;
  name: string;
}

interface Props {
  form: FormInstance;
  disabled: boolean;
  inputImgs: InputImg[];
  initialValues?: FormProps["initialValues"];
}

export const NonParamsFixedImgKeyNamePath = ["nonParams", "fixedImgKey"];
export const NonParamsChannelsNamePath = ["nonParams", "channels"];
export const NonParamsRegisterMethodPath = ["nonParams", "registerMethod"];
const NonParamsShowAdvancedParamsNamePath = ["nonParams", "showAdvancedParams"];

export function StepSetupParameters({
  form,
  disabled,
  inputImgs,
  initialValues,
}: Props) {
  const fixedImgKey =
    useWatch(NonParamsFixedImgKeyNamePath, form) ??
    initialValues?.nonParams?.fixedImgKey;
  const registerMethod =
    useWatch(NonParamsRegisterMethodPath, form) ??
    initialValues?.nonParams?.registerMethod;
  const showAdvanced =
    useWatch(NonParamsShowAdvancedParamsNamePath, form) ??
    initialValues?.nonParams?.showAdvancedParams;

  const formItemsByRegisterType = useMemo(() => {
    let items: StepFormItemInfo[] = [];
    switch (registerMethod) {
      case "elastix":
        items = getElastixFormItems({ form, disabled, showAdvanced });
        break;
      case "sift":
        items = getSiftFormItems({ form, disabled, showAdvanced });
        break;
      case "orb":
        items = getOrbFormItems({ form, disabled, showAdvanced });
        break;
    }
    return items;
  }, [disabled, form, registerMethod, showAdvanced]);

  useEffect(() => {
    form.resetFields();
  }, [form]);

  useEffect(() => {
    if (!registerMethod) {
      form.setFieldValue(NonParamsRegisterMethodPath, "orb");
    } else {
      let defaultParams = { basic: {}, advanced: {} };
      switch (registerMethod) {
        case "elastix":
          defaultParams = defaultElastixParams;
          break;
        case "sift":
          defaultParams = defaultSiftParams;
          break;
        case "orb":
          defaultParams = defaultOrbParams;
          break;
      }
      // when step form items changed, which caused by register method change.
      // we need to reset to its initil values,
      // the original initial values should also be considered
      form.setFieldsValue({
        ...defaultParams.basic,
        ...defaultParams.advanced,
        ...initialValues,
      });
    }
  }, [form, initialValues, registerMethod]);

  const stepFormInfo: StepFormInfo = {
    stepName: "setup-parameters",
    subTitle: (
      <>
        根据图像情况和自身需求，参照文档
        <Link href={HelpLinks.Register} target="_blank">
          《IRRISSin应用-配准》
        </Link>
        相关说明，设置配准所需参数
      </>
    ),
    formProps: {
      disabled: disabled,
      initialValues: initialValues,
    },
    form: form,
    formItems: [
      {
        name: NonParamsFixedImgKeyNamePath,
        label: {
          name: "Fixed Image",
          desc: "选择作为配准进程中的固定图像",
        },
        formItemProps: {
          children: (
            <FormSelect
              options={inputImgs.map((x) => {
                return {
                  value: x.id,
                  label: x.name,
                  title: x.id,
                };
              })}
            />
          ),
        },
      },
      {
        name: NonParamsRegChannelsNamePath,
        label: {
          name: "Channels",
          desc: "选择固定图像&移动图像在配准中所使用 Channel",
        },
        formItemProps: {
          rules: [{ required: true, message: "" }],
          children: (
            <RegisterChannelChooser
              inputImgs={inputImgs}
              fixedImgKey={fixedImgKey}
              onLoadChannels={(channelsByImg) => {
                form.setFieldValue(
                  NonParamsChannelsNamePath.concat([channelsByImg.imgId]),
                  channelsByImg.channels
                );
              }}
            />
          ),
        },
      },
      {
        name: NonParamsChannelsNamePath,
        formItemProps: {
          children: <Input type="hidden" />,
          hidden: true,
        },
      },
      {
        name: NonParamsRegisterMethodPath,
        label: {
          name: "Register method",
          desc: "配准方式",
        },
        formItemProps: {
          children: (
            <FormSelect
              options={[
                {
                  value: "orb",
                  label: "ORB",
                  desc: "对关键点快速创建特征向量，进而识别对象。常规配准算法，适用于小尺寸、通道较少的图像快速配准场景。",
                },
                {
                  value: "sift",
                  label: "SIFT",
                  desc: "对图像进行特征点检测与匹配。适用于多张图像存在可识别特征点的配准场景（即使仅有少量重叠区域）。",
                },
                {
                  value: "elastix",
                  label: "Elastix",
                  desc: "包含多种度量方式（如：互信息、均方差）的灵活配准框架。适用于较为复杂的配准场景（多轮图像、大尺寸）。",
                },
              ]}
            />
          ),
        },
      },
      ...formItemsByRegisterType,
    ],
  };

  return <StepForm {...stepFormInfo} />;
}

const defaultElastixParams = {
  basic: {
    TransformType: "rigid",
    NumberOfResolutions: 10,
    NumberOfSpatialSamples: 5000,
    MaximumNumberOfIterations: 2000,
  },
  advanced: {
    FixedImagePyramid: "FixedRecursiveImagePyramid",
    MovingImagePyramid: "MovingRecursiveImagePyramid",
    ImageSampler: "RandomCoordinate",
    Interpolator: "LinearInterpolator",
    ResampleInterpolator: "FinalNearestNeighborInterpolator",
    Metric: "AdvancedMeanSquares",
    Optimizer: "AdaptiveStochasticGradientDescent",
  },
};

const defaultSiftParams = {
  basic: {},
  advanced: {},
};

const defaultOrbParams = {
  basic: {},
  advanced: {},
};

function getElastixFormItems({
  form,
  disabled,
  showAdvanced,
}: {
  form: FormInstance;
  disabled: boolean;
  showAdvanced: boolean;
}) {
  const resetAndShowAdvancedItems = () => {
    form.setFieldsValue(defaultElastixParams.advanced);
    form.setFieldValue(NonParamsShowAdvancedParamsNamePath, true);
  };

  const resetAndHideAdvancedItems = () => {
    form.setFieldsValue(defaultElastixParams.advanced);
    form.setFieldValue(NonParamsShowAdvancedParamsNamePath, false);
  };

  const advancedItems: StepFormItemInfo[] = [
    {
      name: NonParamsShowAdvancedParamsNamePath,
      formItemProps: {
        children: <Input type="hidden" />,
        hidden: true,
        rules: [{ required: false }],
      },
    },
    {
      nameForLayout: "showAdvancedItems",
      formItemProps: {
        children: (
          <Typography.Link onClick={resetAndShowAdvancedItems}>
            更多自定义参数
          </Typography.Link>
        ),
        hidden: disabled || showAdvanced,
      },
    },
    {
      nameForLayout: "dividerAdvancedItems",
      formItemProps: {
        children: <div className="border-solid border-t" />,
        hidden: !showAdvanced,
      },
    },
    {
      name: "FixedImagePyramid",
      label: {
        name: "Fixed image pyramid",
        desc: "用于捕捉固定图像的全局和局部特征",
      },
      formItemProps: {
        hidden: !showAdvanced,
        children: (
          <FormSelect
            options={[
              {
                value: "FixedRecursiveImagePyramid",
                label: "Recursive Image Pyramid",
                desc: "递归金字塔，对图像进行平滑处理和降采样处理",
              },
              {
                value: "FixedSmoothingImagePyramid",
                label: "Smoothing Image Pyramid",
                desc: "高斯平滑金字塔，仅做平滑处理，不做降采样处理",
              },
              {
                value: "FixedShrinkingImagePyramid",
                label: "Shrinking Image Pyramid",
                desc: "收缩金字塔，仅做降取样处理，不做平滑处理",
              },
            ]}
          />
        ),
      },
    },
    {
      name: "MovingImagePyramid",
      label: {
        name: "Moving image pyramid",
        desc: "用于捕捉移动图像的全局和局部特征",
      },
      formItemProps: {
        hidden: !showAdvanced,
        children: (
          <FormSelect
            options={[
              {
                value: "MovingRecursiveImagePyramid",
                label: "Recursive Image Pyramid",
                desc: "递归金字塔，对图像进行平滑处理和降采样处理",
              },
              {
                value: "MovingSmoothingImagePyramid",
                label: "Smoothing Image Pyramid",
                desc: "高斯平滑金字塔，仅做平滑处理，不做降采样处理",
              },
              {
                value: "MovingShrinkingImagePyramid",
                label: "Shrinking Image Pyramid",
                desc: "收缩金字塔，仅做降取样处理，不做平滑处理",
              },
            ]}
          />
        ),
      },
    },
    {
      name: "ImageSampler",
      label: {
        name: "Image sampler",
        desc: "在图像的指定区域内采集一定数量的像素以进行相似性度量",
      },
      formItemProps: {
        hidden: !showAdvanced,
        children: (
          <FormSelect
            options={[
              {
                value: "RandomCoordinate",
                label: "Random Coordinate",
                desc: "随机选取指定数量的体素，不限于体素位置（可选择体素之间的位置）",
              },
              {
                value: "Grid",
                label: "Grid",
                desc: "在固定图像上定义规则网格，并选择网格上的坐标",
              },
              {
                value: "Random",
                label: "Random",
                desc: "在固定图像中随机选择用户指定数量的体素",
              },
            ]}
          />
        ),
      },
    },
    {
      name: "Interpolator",
      label: {
        name: "Interpolator",
        desc: "配准过程中在非整数坐标位置上计算像素值",
      },
      formItemProps: {
        hidden: !showAdvanced,
        children: (
          <FormSelect
            options={[
              {
                value: "LinearInterpolator",
                label: "Linear",
                desc: "使用距离为权重，返回周围体素的加权平均值",
              },
              {
                value: "NearestNeighborInterpolator",
                label: "Nearest Neighbor",
                desc: "返回距离最近的体素的强度进行插值进行插值",
              },
            ]}
          />
        ),
      },
    },
    {
      name: "ResampleInterpolator",
      label: {
        name: "Resample interpolator",
        desc: "应用最终配准结果变换时，计算最终结果像素值",
      },
      formItemProps: {
        hidden: !showAdvanced,
        children: (
          <FormSelect
            options={[
              {
                value: "FinalNearestNeighborInterpolator",
                label: "Nearest Neighbor",
                desc: "返回距离最近的体素的强度进行插值进行插值",
              },
              {
                value: "FinalBSplineInterpolator",
                label: "BSpline",
                desc: "N 阶 B样条插值法，更高的精度和平滑度（默认 3 阶）",
              },
              {
                value: "FinalLinearInterpolator",
                label: "Linear",
                desc: "使用距离为权重，返回周围体素的加权平均值",
              },
            ]}
          />
        ),
      },
    },
    {
      name: "Metric",
      label: {
        name: "Metric",
        desc: "用于度量固定图像和移动图像之间的相似性",
      },
      formItemProps: {
        hidden: !showAdvanced,
        children: (
          <FormSelect
            options={[
              {
                value: "AdvancedMeanSquares",
                label: "Advanced Mean Squares",
                desc: "均方差 MSD",
              },
              {
                value: "AdvancedMattesMutualInformation",
                label: "Advanced Mattes Mutual Information",
                desc: "互信息MI，计算两个图像的强度的概率分布关系",
              },
              {
                value: "AdvancedNormalizedCorrelation",
                label: "Advanced Normalized Correlation",
                desc: "归一化相关系数 NCC",
              },
            ]}
          />
        ),
      },
    },
    {
      name: "Optimizer",
      label: {
        name: "Optimizer",
        desc: "调整变换参数以最小化（或最大化）图像配准过程中的相似性度量",
      },
      formItemProps: {
        hidden: !showAdvanced,
        children: (
          <FormSelect
            options={[
              {
                value: "AdaptiveStochasticGradientDescent",
                label: "Adaptive Stochastic Gradient Descent",
                desc: "(ASGD)自适应随机梯度下降法，参数更少，且更稳健",
              },
            ]}
          />
        ),
      },
    },
    {
      nameForLayout: "hideAdvancedItems",
      formItemProps: {
        children: (
          <Typography.Link onClick={resetAndHideAdvancedItems}>
            恢复默认义参数
          </Typography.Link>
        ),
        hidden: disabled || !showAdvanced,
      },
    },
  ];

  const stepFormItems: StepFormItemInfo[] = [
    {
      name: "TransformType",
      label: {
        name: "Transform type",
        desc: "配准过程中移动图像的变换方式",
      },
      formItemProps: {
        children: (
          <FormSelect
            options={[
              {
                value: "rigid",
                label: "Euler",
                desc: "欧拉变换（刚体变换）仅平移&旋转，不缩放/拉伸",
              },
            ]}
          />
        ),
      },
    },
    {
      name: "NumberOfResolutions",
      label: {
        name: "Number of resolutions",
        desc: "定义图像金字塔的层数",
      },
      formItemProps: {
        rules: [{ required: true, message: "" }],
        children: (
          <InputNumber
            className="w-full"
            changeOnWheel
            min={1}
            max={20}
            placeholder="请输入 1~20 的正整数"
          />
        ),
      },
    },
    {
      name: "NumberOfSpatialSamples",
      label: {
        name: "Number of spatial samples",
        desc: "设置每次迭代中使用的空间样本数量",
      },
      formItemProps: {
        rules: [{ required: true, message: "" }],
        children: (
          <InputNumber
            className="w-full"
            changeOnWheel
            min={1}
            max={8000}
            step={100}
            placeholder="请输入 1~8000 的正整数"
          />
        ),
      },
    },
    {
      name: "MaximumNumberOfIterations",
      label: {
        name: "Max number of iterations",
        desc: "每层金字塔分辨率下的最大迭代次数",
      },
      formItemProps: {
        rules: [{ required: true, message: "" }],
        children: (
          <InputNumber
            className="w-full"
            changeOnWheel
            min={1}
            max={2000}
            step={100}
            placeholder="请输入 1~2000 的正整数"
          />
        ),
      },
    },
    ...advancedItems,
  ];

  return stepFormItems;
}

function getSiftFormItems({
  form,
  disabled,
  showAdvanced,
}: {
  form: FormInstance;
  disabled: boolean;
  showAdvanced: boolean;
}) {
  return [];
}

function getOrbFormItems({
  form,
  disabled,
  showAdvanced,
}: {
  form: FormInstance;
  disabled: boolean;
  showAdvanced: boolean;
}) {
  return [];
}
