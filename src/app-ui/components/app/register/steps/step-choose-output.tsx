import { FolderSelector } from "@/app-ui/components/item/folder-selector";
import {
  StepForm,
  type StepFormInfo,
} from "@/app-ui/components/utils/step-form";
import {
  Checkbox,
  Flex,
  Form,
  FormInstance,
  Input,
  Typography,
  type FormProps,
} from "antd";
import { useEffect } from "react";

interface InputImg {
  id: string;
  name: string;
}

interface ChannelOption {
  label: string;
  value: number;
}

interface Props {
  projectId: string;
  form: FormInstance;
  disabled: boolean;
  inputImgs: InputImg[];
  fixedImgKey: string;
  channels: Record<string, ChannelOption[]>;
  initialValues?: FormProps["initialValues"];
}

export default function StepChooseOutput({
  projectId,
  form,
  disabled,
  inputImgs,
  fixedImgKey,
  channels,
  initialValues,
}: Props) {
  if (!initialValues) {
    const fixedImg = inputImgs.find((x) => x.id === fixedImgKey);
    const defaultMergedName = fixedImg?.name.replace(".irs", "-Merge");
    const mergeChannels: Record<string, number[]> = {};
    inputImgs.forEach((img) => {
      const allChannels = channels?.[img.id]?.map((x) => x.value) || [];
      mergeChannels[img.id] = allChannels;
    });

    initialValues = {
      outputImgName: defaultMergedName,
      mergeChannels: mergeChannels,
    };
  }

  useEffect(() => {
    form.resetFields();
  }, [form]);

  const stepFormInfo: StepFormInfo = {
    stepName: "choose-output",
    subTitle:
      "选择将计算所得的配准策略应用于哪些图像和通道，并生成配准后的融合图",
    formItems: [
      {
        name: "outputImgName",
        label: { name: "融合图名称" },
        formItemProps: {
          children: <Input addonAfter=".irs" placeholder="融合图名称" />,
          rules: [
            {
              required: true,
              message: "请输入30个中文字符以内的名称，支持中英文、数字、符号等",
              max: 30,
            },
          ],
        },
      },
      {
        name: "mergeChannels",
        label: { name: "融合通道" },
        formItemProps: {
          children: (
            <MergeChannelChooser
              inputImgs={inputImgs}
              fixedImgKey={fixedImgKey}
              channels={channels}
            />
          ),
          rules: [
            {
              required: true,
              async validator(rule, value) {
                const selected = Object.values(value || {}).flat().length;
                if (selected === 0) {
                  return Promise.reject(new Error("至少选择一个融合通道"));
                }
              },
            },
          ],
        },
      },
      {
        name: "outputFolder",
        label: { name: "存储位置", desc: "请选择配准结果存储的文件夹" },
        formItemProps: {
          children: <FolderSelector prjId={projectId} />,
        },
      },
    ],
    formProps: {
      disabled: disabled,
      initialValues: initialValues,
    },
    form: form,
  };

  return <StepForm {...stepFormInfo} />;
}

function MergeChannelChooser({
  inputImgs,
  fixedImgKey,
  channels,
}: {
  inputImgs: InputImg[];
  fixedImgKey: string;
  channels: Record<string, ChannelOption[]>;
}) {
  return (
    <Flex vertical>
      {inputImgs.map((img) => {
        const channelOptions = channels?.[img.id];
        return (
          <Flex key={img.id} vertical>
            <Typography.Text strong={img.id === fixedImgKey}>
              {img.name}
            </Typography.Text>
            <Form.Item
              name={["mergeChannels", img.id]}
              rules={[{ required: false, message: "至少选择 1 个通道" }]}
            >
              <Checkbox.Group
                className="flex-col gap-2"
                options={channelOptions}
              />
            </Form.Item>
          </Flex>
        );
      })}
    </Flex>
  );
}
