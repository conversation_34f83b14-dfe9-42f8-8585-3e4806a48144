import { HelpLinks } from "@/lib/domain/common/constants";
import IconRegister from "@/public/<EMAIL>";
import { Button, Flex, Space } from "antd";
import Text from "antd/es/typography/Text";
import Image from "next/image";
import { AiFillFileText } from "react-icons/ai";

export default function RegisterTitle() {
  return (
    <>
      <Flex align="center" gap={16} className="p-4">
        <Flex flex={"0 0 auto"}>
          <Image src={IconRegister} alt="register" className="m-auto" />
        </Flex>
        <Flex vertical style={{ height: "100%" }}>
          <Space>
            <Text className="text-xl">配准</Text>
            <Button
              type="link"
              icon={<AiFillFileText />}
              href={HelpLinks.Register}
              target="_blank"
            >
              使用说明
            </Button>
          </Space>
          <Text type="secondary">
            提供一系列高度灵活解决图像配准问题的算法 (Elastix, SIFT,
            ORB)，支持二维多通道刚性（平移、欧拉）配准，为复杂的生物图像分析提供高效解决方案。
          </Text>
        </Flex>
      </Flex>
    </>
  );
}
