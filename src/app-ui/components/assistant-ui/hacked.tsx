"use client";

import { ChatRunConfigCustom } from "@/app/api/chat/route";
import { llmSlice } from "@/lib/app-store/llm-slice";
import { useAppSelector } from "@/lib/app-store/store";
import { useEnsureATPSufficient } from "@/lib/utils";
import {
  useComposerRuntime,
  useMessageRuntime,
  useThreadListItem,
  useThreadListItemRuntime,
  useThreadRuntime,
} from "@assistant-ui/react";

import { useCallback, type ReactEventHandler } from "react";

export function useCreateThreadBeforeSend({
  parentId,
  preventDefault = true,
}: {
  parentId?: string;
  preventDefault?: boolean;
}) {
  const cr = useComposerRuntime();
  const tlir = useThreadListItemRuntime();
  const tr = useThreadRuntime();
  const modelId = useAppSelector(llmSlice.selectors.selectModelId);
  const ensureATPSufficient = useEnsureATPSufficient();

  const onSend: ReactEventHandler = useCallback(
    async (e) => {
      if (preventDefault) {
        e.preventDefault();
      }

      const isSufficient = await ensureATPSufficient();
      if (!isSufficient) {
        return;
      }

      const crs = cr.getState();
      const trs = tr.getState();
      if (trs.isRunning || !crs.isEditing || crs.isEmpty) {
        return;
      }

      const res = await tlir.initialize();

      const custom: ChatRunConfigCustom = {
        threadId: res.remoteId,
        parentId,
        modelId,
      };
      cr.setRunConfig({ custom });

      cr.send();
    },
    [preventDefault, cr, tr, tlir, parentId, modelId, ensureATPSufficient]
  );

  return onSend;
}

export const useRunConfigReload = ({
  preventDefault = true,
}: {
  preventDefault?: boolean;
}) => {
  const mr = useMessageRuntime();
  const tr = useThreadRuntime();
  const tli = useThreadListItem();
  const threadId = tli.remoteId || tli.id;
  const modelId = useAppSelector(llmSlice.selectors.selectModelId);
  const ensureATPSufficient = useEnsureATPSufficient();

  const onReload: ReactEventHandler = useCallback(
    async (e) => {
      if (preventDefault) {
        e.preventDefault();
      }

      const isSufficient = await ensureATPSufficient();
      if (!isSufficient) {
        return;
      }

      const trs = tr.getState();
      const mrs = mr.getState();
      if (trs.isRunning || trs.isDisabled || mrs.role !== "assistant") {
        return;
      }

      const custom: ChatRunConfigCustom = {
        threadId: threadId, modelId
      };

      mr.reload({ runConfig: { custom } });
    },
    [modelId, mr, preventDefault, threadId, tr, ensureATPSufficient]
  );

  return onReload;
};
