"use client";

import { Dropdown, Flex, Input, type MenuProps, Tooltip, App } from "antd";
import {
  AiOutlinePlus,
  AiOutlineReload,
  AiOutlineSearch,
} from "react-icons/ai";
import { useState } from "react";
import { ItemCreateDirBtn } from "@/app-ui/components/item/create-dir-btn";
import { useUpdateTreeData } from "@/lib/app-store/item-slice";

export function ItemOperate({ projectId }: { projectId: string }) {
  const treeId = "viewer";
  const { message } = App.useApp();
  const updateTreeData = useUpdateTreeData();

  const [createTipOpen, setCreateTipOpen] = useState(false);

  const refreshData = async () => {
    await updateTreeData({
      projectId,
      treeId,
      generateUpdateKeys: (expandedKeys) => ({
        refreshKeys: [...expandedKeys, projectId],
      }),
    });
    message.success("刷新成功");
  };

  const items: MenuProps["items"] = [
    {
      key: "createFolder",
      label: <ItemCreateDirBtn projectId={projectId} treeId={treeId} />,
    },
  ];
  return (
    <Flex
      className="mt-2 px-3 text-[#969A9F] mb-3"
      justify="space-between"
      gap={12}
      align="center"
    >
      <Input
        prefix={<AiOutlineSearch className="size-4 text-[#BFBFBF]" />}
        variant="filled"
        className="flex-1"
        size="small"
        placeholder="搜索"
      />

      <Tooltip title="刷新" placement="bottom">
        <AiOutlineReload
          className="size-4 cursor-pointer"
          onClick={refreshData}
        />
      </Tooltip>

      <Dropdown
        menu={{ items }}
        trigger={["click"]}
        onOpenChange={(open) => {
          if (open) {
            setCreateTipOpen(false);
          }
        }}
      >
        <Tooltip
          title="新建"
          placement="bottom"
          open={createTipOpen}
          onOpenChange={setCreateTipOpen}
        >
          <AiOutlinePlus className="size-4 cursor-pointer" />
        </Tooltip>
      </Dropdown>
    </Flex>
  );
}
