import type { ItemsHierarchyRes } from "@/app/api/item/hierarchy/route";
import { diverseApi } from "@/lib/apis/diverse/api";
import { ItemTreeNode } from "@/lib/app-store/item-slice";
import { dropTargetForElements } from "@atlaskit/pragmatic-drag-and-drop/element/adapter";
import { Cascader, Result } from "antd";
import type { DefaultOptionType } from "antd/es/cascader";
import { useEffect, useRef } from "react";

interface Option extends DefaultOptionType {
  isLeaf?: boolean;
}

type Props = Parameters<typeof Cascader<Option>>[0] & {
  prjId: string;
};

export function FolderSelector({ prjId, ...resProps }: Props) {
  const [
    getItemsHierarchy,
    { data: items = [], isLoading: itemsLoading, error: queryItemsError },
  ] = diverseApi.useLazyGetItemsHierarchyQuery();
  const ref = useRef<HTMLInputElement>(null);

  const onChange = resProps.onChange;

  useEffect(() => {
    if (!ref.current) {
      return;
    }

    const cleanup = dropTargetForElements({
      element: ref.current,
      onDrop(args) {
        const node = args.source.data.node as ItemTreeNode;
        const ids = node.pids?.slice(1).concat(node.key);
        if (ids) {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          onChange?.(ids as any, []);
        }
      },
    });
    return cleanup;
  }, [onChange]);

  if (queryItemsError) {
    return <Result status="error" title="something wrong" />;
  }

  const options = buildOptions(items);

  const onOpenChange = (open: boolean) => {
    if (open) {
      getItemsHierarchy({ prjId }, true);
    }
  };

  return (
    <div ref={ref}>
      <Cascader
        onOpenChange={onOpenChange}
        options={options}
        changeOnSelect
        loading={itemsLoading}
        {...resProps}
      />
    </div>
  );
}

function buildOptions(items: ItemsHierarchyRes): Option[] {
  const optionsMap = new Map<string, Option>();
  const rootOptions: Option[] = [];

  for (const item of items) {
    optionsMap.set(item.id, {
      label: item.name,
      value: item.id,
      isLeaf: true,
    });
  }

  for (const item of items) {
    const option = optionsMap.get(item.id)!;

    if (item.pid) {
      const parent = optionsMap.get(item.pid);
      if (parent) {
        parent.children ??= [];
        parent.children.push(option);
        parent.isLeaf = false;
      }
    } else {
      rootOptions.push(option);
    }
  }

  return rootOptions;
}

if (import.meta.vitest) {
  const { it, expect, describe } = import.meta.vitest;
  describe("buildOptions", () => {
    it("should build options correctly", () => {
      const items = [
        {
          iid: 30,
          id: "uuid_1",
          piid_path: "/prjId/22/26/",
          name: "1444",
          pid: "uuid_2",
        },
        {
          iid: 26,
          id: "uuid_2",
          piid_path: "/prjId/22/",
          name: "144",
          pid: "uuid_3",
        },
        {
          iid: 22,
          id: "uuid_3",
          piid_path: "/prjId/",
          name: "14",
          pid: undefined,
        },
        {
          iid: 21,
          id: "uuid_4",
          piid_path: "/prjId/",
          name: "13",
          pid: undefined,
        },
      ];
      const options = buildOptions(items as ItemsHierarchyRes);
      expect(options).toEqual([
        {
          label: "14",
          value: "uuid_3",
          isLeaf: false,
          children: [
            {
              isLeaf: false,
              label: "144",
              value: "uuid_2",
              children: [
                {
                  isLeaf: true,
                  label: "1444",
                  value: "uuid_1",
                },
              ],
            },
          ],
        },
        {
          label: "13",
          value: "uuid_4",
          isLeaf: true,
        },
      ]);
    });
  });
}
