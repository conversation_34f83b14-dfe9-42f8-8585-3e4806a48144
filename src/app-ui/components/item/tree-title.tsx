import { PreviewBtn } from "@/app-ui/components/image/previewer";
import { ItemCopyPathBtn } from "@/app-ui/components/item/copy-path-btn";
import { ItemCreateDirBtn } from "@/app-ui/components/item/create-dir-btn";
import { ItemCreateDirInput } from "@/app-ui/components/item/create-dir-input";
import { ItemDeleteBtn } from "@/app-ui/components/item/delete-btn";
import { ItemMoveBtn } from "@/app-ui/components/item/move-btn";
import { ItemRenameBtn } from "@/app-ui/components/item/rename-btn";
import { ItemRenameInput } from "@/app-ui/components/item/rename-input";
import {
  itemSlice,
  type ItemTreeId,
  type ItemTreeNode,
} from "@/lib/app-store/item-slice";
import { useAppDispatch, useAppSelector } from "@/lib/app-store/store";
import { humanFileSize } from "@/lib/utils";
import { draggable } from "@atlaskit/pragmatic-drag-and-drop/element/adapter";
import { Dropdown, Typography } from "antd";
import type { ItemType } from "antd/es/menu/interface";
import { useEffect, useRef } from "react";
import { AiOutlineEllipsis } from "react-icons/ai";

function TitleMenus({
  node,
  projectId,
  treeId,
}: {
  node: ItemTreeNode;
  projectId: string;
  treeId: ItemTreeId;
}) {
  const dispatch = useAppDispatch();

  const { isLeaf: isFile, key: itemId, size, pid, namePath } = node;

  const showItemIf = (item: ItemType, show?: boolean) => {
    return show ? item : undefined;
  };

  if (!pid) {
    return;
  }

  const items: ItemType[] = [
    showItemIf(
      {
        key: "open-viewer",
        label: <PreviewBtn imgId={itemId} />,
      },
      isFile
    ),
    {
      key: "copy-path",
      label: <ItemCopyPathBtn namePath={namePath} />,
    },
    showItemIf(
      {
        key: "mkdir",
        label: (
          <ItemCreateDirBtn id={itemId} projectId={projectId} treeId={treeId} />
        ),
      },
      !isFile
    ),
    {
      key: "rename",
      label: <ItemRenameBtn id={itemId} treeId={treeId} />,
    },
    {
      type: "divider",
    } as ItemType,
    {
      key: "move",
      label: (
        <ItemMoveBtn
          projectId={projectId}
          treeId={treeId}
          pid={pid}
          id={itemId}
        />
      ),
    },
    showItemIf(
      {
        type: "divider",
      } as ItemType,
      isFile
    ),
    showItemIf(
      {
        key: "size",
        label: `大小 ${humanFileSize(size)}`,
        disabled: true,
      },
      isFile
    ),
    {
      type: "divider",
    } as ItemType,
    {
      key: "delete",
      label: (
        <ItemDeleteBtn
          id={itemId}
          projectId={projectId}
          treeId={treeId}
          pid={pid}
        />
      ),
    },
  ].filter((v) => v !== undefined);

  return (
    <Dropdown
      overlayClassName="w-40 [&_.ant-dropdown-menu-item-disabled]:cursor-text"
      menu={{
        items,
        onClick: ({ domEvent }) => {
          domEvent.stopPropagation();
          dispatch(
            itemSlice.actions.setActiveNode({
              treeId,
              activeNode: node,
            })
          );
        },
      }}
    >
      <AiOutlineEllipsis size={20} className="text-black/45" />
    </Dropdown>
  );
}

export type ItemTreeTitleProps = {
  node: ItemTreeNode;
  projectId: string;
  treeId: ItemTreeId;
  showMenu?: boolean;
};

export function ItemTreeTitle({
  node,
  projectId,
  treeId,
  showMenu,
}: ItemTreeTitleProps) {
  const { isNewFolder, pid, name, key, isLeaf: isFile } = node;

  const renamingNodeKey = useAppSelector((s) =>
    itemSlice.selectors.selectRenamingNodeKey(s, treeId)
  );

  if (!showMenu || !pid) {
    return (
      <div className="flex items-center justify-between">
        <DragableItem node={node} projectId={projectId} />
      </div>
    );
  } else if (isNewFolder && pid) {
    return (
      <ItemCreateDirInput pid={pid} projectId={projectId} treeId={treeId} />
    );
  } else if (renamingNodeKey === node.key) {
    return (
      <ItemRenameInput
        id={key}
        pid={pid}
        treeId={treeId}
        projectId={projectId}
        name={name}
        isFile={isFile}
      />
    );
  } else {
    return (
      <div className="flex items-center justify-between group break-all">
        <DragableItem node={node} projectId={projectId} />
        <span className="hidden group-hover:block">
          <TitleMenus node={node} projectId={projectId} treeId={treeId} />
        </span>
      </div>
    );
  }
}

function DragableItem({
  node,
  projectId,
}: {
  node: ItemTreeNode;
  projectId: string;
}) {
  const ref = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (!ref.current) {
      return;
    }

    const cleanup = draggable({
      element: ref.current,
      getInitialData() {
        return {
          type: "item-node",
          node,
          projectId,
        };
      },
      canDrag() {
        return !!node.pid;
      },
    });
    return cleanup;
  }, [node, projectId]);

  return (
    <div ref={ref} className="flex-auto w-0">
      <Typography.Text ellipsis>{node.name}</Typography.Text>
    </div>
  );
}
