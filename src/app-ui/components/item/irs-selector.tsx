import { ItemTreeNode } from "@/lib/app-store/item-slice";
import { dropTargetForElements } from "@atlaskit/pragmatic-drag-and-drop/element/adapter";
import { Button, Table } from "antd";
import { uniqBy } from "es-toolkit";
import { useCallback, useEffect, useRef, useState } from "react";

type IrsItem = {
  id: string;
  name: string;
};

export function IrsSelector({
  disabled,
  onChange,
  value,
}: {
  value?: IrsItem[];
  onChange?: (irsItems: IrsItem[]) => void;
  disabled?: boolean;
}) {
  const ref = useRef<HTMLInputElement>(null);
  const [items, setItems] = useState<IrsItem[]>([]);

  const addItem = useCallback(
    (node: ItemTreeNode) => {
      let irsNodes = [node];
      if (!node.isLeaf) {
        irsNodes = node.children?.filter((x: ItemTreeNode) => x.isLeaf) || [];
      }

      let newItems = irsNodes.map<IrsItem>((x) => {
        return { id: x.key, name: x.name };
      });

      newItems = uniqBy([...items, ...newItems], (x) => x.id);
      setItems(newItems);
      onChange?.(newItems);
    },
    [items, onChange]
  );

  const removeItem = (id: string) => {
    const newItems = items.filter((item) => item.id !== id);
    setItems(newItems);
    onChange?.(newItems);
  };

  useEffect(() => {
    if (!ref.current || disabled) {
      return;
    }

    const cleanup = dropTargetForElements({
      element: ref.current,
      onDrop(args) {
        const node = args.source.data.node as ItemTreeNode;
        addItem(node);
      },
    });
    return cleanup;
  }, [addItem, disabled]);

  return (
    <div ref={ref} className="bg-gray-100/75 min-h-10 border border-dashed">
      <div>拖拽到此</div>
      <IrsSelectorResult items={items} onRemoveItem={removeItem} />
    </div>
  );
}

function IrsSelectorResult({
  items,
  onRemoveItem,
}: {
  items: IrsItem[];
  onRemoveItem: (id: string) => void;
}) {
  return (
    items.length > 0 && (
      <Table<IrsItem>
        columns={[
          {
            dataIndex: "name",
            ellipsis: true,
          },
          {
            dataIndex: "id",
            align: "right",
            width: 100,
            render: (v, r, i) => {
              return (
                <>
                  <Button
                    type="link"
                    onClick={() => {
                      onRemoveItem(r.id);
                    }}
                  >
                    移除
                  </Button>
                </>
              );
            },
          },
        ]}
        dataSource={items}
        pagination={false}
        size="small"
        showHeader={false}
        rowKey={"id"}
      />
    )
  );
}
