import type { IdVerifyResponse } from "@/lib/infra/aliyun/client";
import { http, HttpResponse } from "msw";

export const handlers = [
  http.get(
    "https://zidv2.market.alicloudapi.com/idcard/VerifyIdcardv2",
    ({ request }) => {
      const url = new URL(request.url);
      const realName = url.searchParams.get("realName");

      if (realName === "错误姓名") {
        const result: IdVerifyResponse = {
          error_code: 0,
          reason: "身份证号码与姓名不匹配",
          result: {
            realname: realName!,
            idcard: "",
            isok: false,
            IdCardInfor: {
              province: "",
              city: "",
              district: "",
              area: "",
              sex: "",
              birthday: "",
            },
          },
        };
        return HttpResponse.text(JSON.stringify(result));
      }

      const result: IdVerifyResponse = {
        error_code: 0,
        reason: "",
        result: {
          realname: "test**",
          idcard: "513123***********1",
          isok: true,
          IdCardInfor: {
            province: "xx省",
            city: "xx市",
            district: "xx县",
            area: "xxxx地区",
            sex: "男",
            birthday: "2001-4-1",
          },
        },
      };
      return HttpResponse.text(JSON.stringify(result));
    }
  ),
];
