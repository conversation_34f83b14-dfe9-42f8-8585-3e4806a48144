diff --git a/dist/server/lib/start-server.js b/dist/server/lib/start-server.js
index 431891033ced7c3376c9edc74c661d7f9b80ae6f..a544208db107abfaeaf5eca797139bd138aed07a 100644
--- a/dist/server/lib/start-server.js
+++ b/dist/server/lib/start-server.js
@@ -250,6 +250,14 @@ async function startServer(serverOptions) {
     if (keepAliveTimeout) {
         server.keepAliveTimeout = keepAliveTimeout;
     }
+    
+    // To allow for configurable request timeout (i.e. for file uploads)
+    // https://github.com/vercel/next.js/discussions/71651
+    if (process.env.IRS_HTTP_REQ_TIMEOUT) {
+        server.requestTimeout = parseInt(process.env.IRS_HTTP_REQ_TIMEOUT, 10);
+        console.log(`> next server requestTimeout set to ${server.requestTimeout}ms`);
+    }
+
     server.on('upgrade', async (req, socket, head)=>{
         try {
             await upgradeHandler(req, socket, head);
