import react from "@vitejs/plugin-react";
import tsconfigPaths from "vite-tsconfig-paths";
import { defineConfig } from "vitest/config";

import nextjsEnv from "@next/env";
nextjsEnv.loadEnvConfig(process.cwd());

export default defineConfig({
  plugins: [tsconfigPaths(), react()],
  test: {
    environment: "jsdom",
    setupFiles: "vitest-setup.ts",
    server: {
      deps: {
        inline: ["next-auth"],
      },
    },
    projects: [
      {
        extends: true,
        test: {
          name: "unit-test",
          includeSource: ["src/**/*.(ts|tsx)"],
        },
      },
      {
        extends: true,
        test: {
          name: "db-test",
          include: ["tests/db/*.test.ts"],
        },
      },
    ],
    coverage: {
      exclude: [
        "src/lib/auth.ts",
        "src/lib/layers/*.ts",
        "src/lib/app-store/*.ts",
      ],
      include: ["src/lib/**/*.ts"],
      thresholds: {
        branches: 93,
        functions: 37,
        lines: 56.87,
        statements: 56.87,
        autoUpdate: false,
      },
    },
  },
  define: {
    "import.meta.vitest": "undefined", // might not be needed as we are using nextjs
  },
});
