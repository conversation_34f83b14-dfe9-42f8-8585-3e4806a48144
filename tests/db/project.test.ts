import { getProject } from "@/lib/domain/project/dbt";
import { getTestPrisma } from "@/tests/db/db-fixture";
import { describe, expect, it } from "vitest";

describe("getProject", async () => {
  const prisma = await getTestPrisma();

  it("should get project", async () => {
    const mockProject = await prisma.project.create({
      data: {
        name: "test-prj",
        created_by_id: "userId",
        updated_by_id: "userId",
      },
    });

    const projectFromDb = await getProject(prisma, mockProject.id);
    expect(projectFromDb).toEqual(mockProject);
  });

  it("should return null if project deleted", async () => {
    const mockProject = await prisma.project.create({
      data: {
        name: "test-prj",
        created_by_id: "userId",
        updated_by_id: "userId",
        deleted_at: 1,
      },
    });

    const projectFromDbPromise = await getProject(prisma, mockProject.id);
    expect(projectFromDbPromise).toBeNull();
  });
});
