import {
  getItem,
  getItemsByPiidPath,
  getItemsByPrjId,
} from "@/lib/domain/item/dbt";
import { ItemTypeFlag } from "@/lib/domain/item/svc";
import { getTestPrisma } from "@/tests/db/db-fixture";
import { PrismaClient, Project } from "@prisma/client";
import { beforeEach, describe, expect, it } from "vitest";

describe("getItem", async () => {
  it("should get item", async () => {
    const prisma = await getTestPrisma();
    const mockItem = await prisma.item.create({
      data: {
        piid_path: "/prjId/xxx/",
        name: "item-name",
        type_flag: ItemTypeFlag.File,
        created_by_id: "userId",
        updated_by_id: "userId",
      },
    });

    const itemFromDb = await getItem(prisma, { itemId: mockItem.id });
    expect(itemFromDb).toEqual(mockItem);
  });

  it("should throw if item deleted", async () => {
    const prisma = await getTestPrisma();
    const mockItem = await prisma.item.create({
      data: {
        piid_path: "/prjId/xxx/",
        name: "item-name",
        type_flag: ItemTypeFlag.File,
        created_by_id: "userId",
        updated_by_id: "userId",
        deleted_at: 1,
      },
    });

    const itemFromDbPromise = getItem(prisma, { itemId: mockItem.id });
    await expect(itemFromDbPromise).rejects.toThrow("item_not_found");
  });

  it("should throw if type flag not matched", async () => {
    const prisma = await getTestPrisma();
    const mockItem = await prisma.item.create({
      data: {
        piid_path: "/prjId/xxx/",
        name: "item-name",
        type_flag: ItemTypeFlag.File,
        created_by_id: "userId",
        updated_by_id: "userId",
      },
    });

    const itemFromDbPromise = getItem(prisma, {
      itemId: mockItem.id,
      typeFlag: ItemTypeFlag.Folder,
    });

    await expect(itemFromDbPromise).rejects.toThrow("item_not_found");
  });
});

describe("getItemsByPiidPath", async () => {
  let prisma: PrismaClient;
  beforeEach(async () => {
    prisma = await getTestPrisma();
    await prisma.item.createMany({
      data: [
        {
          id: "uuid_1",
          piid_path: "/prjId/",
          name: "folder1",
          type_flag: ItemTypeFlag.Folder,
          created_by_id: "userId",
          updated_by_id: "userId",
          created_at: new Date("2025-01-01T01:23:00.000Z"),
          updated_at: new Date("2025-01-01T01:23:00.000Z"),
          deleted_at: 2389223,
        },
        {
          id: "uuid_2",
          piid_path: "/prjId/",
          name: "folder2",
          type_flag: ItemTypeFlag.Folder,
          created_by_id: "userId",
          updated_by_id: "userId",
          created_at: new Date("2025-01-01T01:33:00.000Z"),
          updated_at: new Date("2025-01-01T01:33:00.000Z"),
          deleted_at: 0,
        },
        {
          id: "uuid_3",
          piid_path: "/prjId/",
          name: "folder3",
          type_flag: ItemTypeFlag.Folder,
          created_by_id: "userId",
          updated_by_id: "userId",
          created_at: new Date("2025-01-01T00:43:00.000Z"),
          updated_at: new Date("2025-01-01T00:43:00.000Z"),
          deleted_at: 0,
        },
        {
          id: "uuid_4",
          piid_path: "/prjId/2/",
          name: "bmx_file",
          type_flag: ItemTypeFlag.File,
          created_by_id: "userId",
          updated_by_id: "userId",
          created_at: new Date("2025-01-01T00:53:00.000Z"),
          updated_at: new Date("2025-01-01T00:53:00.000Z"),
          deleted_at: 0,
          size: 100,
        },
        {
          id: "uuid_5",
          piid_path: "/prjId/3/",
          name: "folder3-1",
          type_flag: ItemTypeFlag.Folder,
          created_by_id: "userId",
          updated_by_id: "userId",
          created_at: new Date("2025-01-01T00:55:00.000Z"),
          updated_at: new Date("2025-01-01T00:55:00.000Z"),
          deleted_at: 0,
        },
        {
          id: "uuid_6",
          piid_path: "/prjId/",
          name: "file_6",
          type_flag: ItemTypeFlag.File,
          created_by_id: "userId",
          updated_by_id: "userId",
          created_at: new Date("2025-01-01T00:58:00.000Z"),
          updated_at: new Date("2025-01-01T00:58:00.000Z"),
          deleted_at: 0,
          size: 100,
        },
        {
          id: "uuid_7",
          piid_path: "/prjId/",
          name: "file_7",
          type_flag: ItemTypeFlag.File,
          created_by_id: "userId",
          updated_by_id: "userId",
          created_at: new Date("2025-01-01T00:58:00.000Z"),
          updated_at: new Date("2025-01-01T00:58:00.000Z"),
          deleted_at: 0,
          size: 0,
        },
      ],
    });
  });

  it("should return items rightly", async () => {
    const items = await getItemsByPiidPath(prisma, { piid_path: "/prjId/" });

    expect(items.map((x) => x.id).join(",")).toBe("uuid_2,uuid_6,uuid_3");
  });

  it("should filter by type flag", async () => {
    const items = await getItemsByPiidPath(prisma, {
      piid_path: "/prjId/",
      typeFlag: ItemTypeFlag.Folder,
    });
    expect(items.map((x) => x.id).join(",")).toBe("uuid_2,uuid_3");
  });
});

describe("getItemsByPrjId", async () => {
  let mockPrj: Project;
  let otherPrj: Project;
  let prisma: PrismaClient;

  beforeEach(async () => {
    prisma = await getTestPrisma();
    mockPrj = await prisma.project.create({
      data: {
        name: "test-project",
        created_by_id: "userId",
        updated_by_id: "userId",
      },
    });
    otherPrj = await prisma.project.create({
      data: {
        name: "other-project",
        created_by_id: "userId",
        updated_by_id: "userId",
      },
    });

    await prisma.item.createMany({
      data: [
        {
          name: "file1.jpg",
          piid_path: `/${mockPrj.id}/`,
          type_flag: ItemTypeFlag.File,
          created_by_id: "userId",
          updated_by_id: "userId",
          created_at: new Date("2025-01-01T09:00:00Z"),
          updated_at: new Date("2025-01-01T10:00:00Z"),
          deleted_at: 0,
          size: 100,
        },
        {
          name: "file2.png",
          piid_path: `/${mockPrj.id}/`,
          type_flag: ItemTypeFlag.File,
          created_by_id: "userId",
          updated_by_id: "userId",
          created_at: new Date("2025-01-01T10:00:00Z"),
          updated_at: new Date("2025-01-01T09:00:00Z"),
          deleted_at: 0,
          size: 100,
        },
        {
          name: "file3.pdf",
          piid_path: `/${mockPrj.id}/`,
          type_flag: ItemTypeFlag.File,
          created_by_id: "userId",
          updated_by_id: "userId",
          created_at: new Date("2025-01-01T08:00:00Z"),
          updated_at: new Date("2025-01-01T11:00:00Z"),
          deleted_at: 0,
          size: 100,
        },
        {
          name: "folder1",
          piid_path: `/${mockPrj.id}/`,
          type_flag: ItemTypeFlag.Folder,
          created_by_id: "userId",
          updated_by_id: "userId",
          deleted_at: 0,
        },
        {
          name: "deleted-file.txt",
          piid_path: `/${mockPrj.id}/`,
          type_flag: ItemTypeFlag.File,
          created_by_id: "userId",
          updated_by_id: "userId",
          deleted_at: 1,
          size: 100,
        },
        {
          name: "other-file.doc",
          piid_path: `/${otherPrj.id}/`,
          type_flag: ItemTypeFlag.File,
          created_by_id: "userId",
          updated_by_id: "userId",
          deleted_at: 0,
          size: 100,
        },
        {
          name: "0-size-file.doc",
          piid_path: `/${otherPrj.id}/`,
          type_flag: ItemTypeFlag.File,
          created_by_id: "userId",
          updated_by_id: "userId",
          deleted_at: 0,
          size: 0,
        },
        {
          name: "folder_uas",
          piid_path: `/${otherPrj.id}/5/`,
          type_flag: ItemTypeFlag.Folder,
          created_by_id: "userId",
          updated_by_id: "userId",
          deleted_at: 0,
          size: 100,
        },
      ],
    });
  });

  it("should filter and return only files from project", async () => {
    const items = await getItemsByPrjId(prisma, {
      prjId: mockPrj.id,
      orderBy: { created_at: "desc" },
      typeFlag: ItemTypeFlag.File,
    });
    expect(items).toHaveLength(3);
    expect(
      items.every(
        (item) => item.type_flag === ItemTypeFlag.File && item.deleted_at === 0
      )
    ).toBe(true);
    expect(items.map((item) => item.name)).toEqual(
      expect.not.arrayContaining(["folder1", "deleted-file.txt"])
    );
  });

  it("should sort correctly", async () => {
    const byCreatedAt = await getItemsByPrjId(prisma, {
      prjId: mockPrj.id,
      orderBy: [{ created_at: "desc" }],
      typeFlag: ItemTypeFlag.File,
    });
    expect(byCreatedAt[0].name).toBe("file2.png");

    const byUpdatedAt = await getItemsByPrjId(prisma, {
      prjId: mockPrj.id,
      orderBy: { updated_at: "desc" },
      typeFlag: ItemTypeFlag.File,
    });
    expect(byUpdatedAt[0].name).toBe("file3.pdf");
  });

  it("should take right count of items", async () => {
    const [limitedItems, mockPrjItems, otherPrjItems] = await Promise.all([
      getItemsByPrjId(prisma, {
        prjId: mockPrj.id,
        length: 2,
        typeFlag: ItemTypeFlag.File,
      }),
      getItemsByPrjId(prisma, {
        prjId: mockPrj.id,
        length: 99,
        typeFlag: ItemTypeFlag.File,
      }),
      getItemsByPrjId(prisma, {
        prjId: otherPrj.id,
        length: 99,
        typeFlag: ItemTypeFlag.File,
      }),
    ]);

    expect([
      limitedItems.length,
      mockPrjItems.length,
      otherPrjItems.length,
    ]).toEqual([2, 3, 1]);
    expect(otherPrjItems[0].name).toBe("other-file.doc");

    const allItems = await getItemsByPrjId(prisma, {
      prjId: otherPrj.id,
      length: 99,
    });
    expect(allItems.length).toBe(2);
    expect(allItems[0].name).toBe("other-file.doc");
    expect(allItems[1].name).toBe("folder_uas");
  });

  it("should return empty array if project not found", async () => {
    const [nonExistentItems, largeLength] = await Promise.all([
      getItemsByPrjId(prisma, { prjId: "non-existent-id" }),
      getItemsByPrjId(prisma, {
        prjId: mockPrj.id,
        length: 100,
        typeFlag: ItemTypeFlag.File,
      }),
    ]);

    expect([nonExistentItems.length, largeLength.length]).toEqual([0, 3]);
  });
});
