services:
  db-irs:
    image: postgres
    restart: always
    environment:
      POSTGRES_PASSWORD: test
    volumes:
      - pgdata-irs:/var/lib/postgresql/data
    ports:
      - 5432:5432
  restate:
    image: docker.restate.dev/restatedev/restate:1.4
    ports:
      - 8080:8080
      - 9070:9070
      - 9071:9071
    extra_hosts:
      - "host.docker.internal:host-gateway"
volumes:
  pgdata-irs:


