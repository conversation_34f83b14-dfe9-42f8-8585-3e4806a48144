import { vi, beforeAll, afterAll, afterEach } from "vitest";
import { server } from "@/mocks/server";

vi.mock("server-only", () => {
  return {};
});

vi.mock("@/app-ui/actions/client", () => {
  return {
    authActionClient: {
      inputSchema: vi.fn().mockReturnValue({
        action: vi.fn().mockReturnValue(vi.fn()),
      }),
      action: vi.fn().mockReturnValue(vi.fn()),
    },
  };
});

beforeAll(() => server.listen({ onUnhandledRequest: "error" }));

afterEach(() => server.resetHandlers());

afterAll(() => server.close());
