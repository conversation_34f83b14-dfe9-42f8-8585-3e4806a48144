# since these are general development env, we can share things that can not do harm
# this file is meant for share same env for all devs

AUTH_SECRET="5wXxcvid9b9zLdbXgi9awDAFQtw/TTj/BBeJE6DqtDU="
# https://github.com/nextauthjs/next-auth/issues/10928, 
# as we have set it in .env , so need to set it here as well coz we can't remove this env
AUTH_URL="http://localhost:3000/api/auth"

AUTHING_ID="67dbacb2ba92dd6d911a2068"
AUTHING_SECRET="68e301abc9555c693cc18483e4483ece"
AUTHING_APP_HOST="https://irs-dev.authing.cn"
AUTHING_USER_POOL_ID="67dbacb114905a0074ddd47f"
AUTHING_USER_POOL_SECRET="4b0b8e3a8bb77c4a542328f58a26faf1"

CLOAK_MASTER_KEY="k1.aesgcm256.zKnYXWKrooPu8LW8ndUDi0A3JkDH3-gaEzUF0i1UqKg="
CLOAK_KEYCHAIN="v1.aesgcm256.d773f105.CKBANCWqin9qC4xK.HtZc59IRfhl_DR3S6ieNHhn-6X_j9P1L_3Kml8ACSHjp9Xbn10BsUsRXh4NUkDDex161EZgIK6I4ZYnzvbCCq7bbUMU-V6j9Ni9yUQh6N9kVjnjzOJLLYjxn9xM1SOy4of1-iI7ykJCrqVIW12vsrZhNjehl-pYZTmNRczstyCovQBI6Wks0d0dHU4lNO94OqHUX1_Uan3eay2OiXrVkeT0QViPfwOI1RmI9G7-MFEqZjB8VCaIQX-2k6T3tsjn2dIPfVAca7L1z5kT9ato3u4RTwHX0a6rbCmWKF9XzLeYAq4zlaHS4Kl8tYNWYZPNla6CQsg3nvj2fwsGjrzM677HDuaTLBwA2sKpal32aUxTpRzYVwHGlF3Mv0XKx03CwnhXiuXWIkCPwIuM="
CLOAK_CURRENT_KEY="4428bb36"

REMOTE_TASK_API_URL="http://localhost:8000"
RESTATE_API_URL="http://localhost:8080"
GAZE_API_URL="http://localhost:8099"

OSS_REGION="oss-cn-chengdu"
OSS_BUCKET="irs-dev-test"
OSS_ENDPOINT="https://oss-cn-chengdu.aliyuncs.com"

DATABASE_URL="postgresql://postgres:test@localhost:5432/postgres?schema=irs"
DATABASE_DIRECT_URL="postgresql://postgres:test@localhost:5432/postgres?schema=irs"

NEXT_PUBLIC_API_MOCKING="enabled"
