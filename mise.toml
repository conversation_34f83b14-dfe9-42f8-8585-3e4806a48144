[tools]
node = "24"

[tasks.local-prisma]
description = "run prisma with dev env"
run = "pnpx dotenv-cli -e .env.development -- pnpm exec prisma"

[tasks.test-prisma]
description = "run prisma with test env"
run = "pnpx dotenv-cli -e .env.test -- pnpm exec prisma"

[tasks.setup-local-dev-svc]
description = "setup local dev svc"
run = '''
    docker compose -f ops/local-dev/compose.yml up -d
    curl localhost:9070/deployments --json '{"uri": "http://host.docker.internal:3000/api/restate", "use_http_11": true, "force":true}'
'''
