{"name": "diverse", "version": "0.2.0", "private": true, "scripts": {"dev": "IRS_HTTP_REQ_TIMEOUT=0 next dev --turbopack", "build": "IRS_HTTP_REQ_TIMEOUT=0 next build", "start": "IRS_HTTP_REQ_TIMEOUT=0 next start", "lint": "next typegen && eslint ./src && tsc", "test": "vitest", "test:coverage": "vitest --coverage", "ut": "vitest --project unit-test", "dbt": "vitest --project db-test", "postinstall": "prisma generate", "route-path": "pnpx nextjs-paths generate -f route-path.ts"}, "dependencies": {"@47ng/cloak": "^1.2.0", "@ai-sdk/openai-compatible": "^1.0.18", "@ai-sdk/provider": "^2.0.0", "@ai-sdk/react": "^2.0.47", "@ant-design/colors": "^7.2.1", "@ant-design/nextjs-registry": "^1.1.0", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@assistant-ui/react": "^0.11.12", "@assistant-ui/react-ai-sdk": "^1.1.0", "@assistant-ui/react-markdown": "^0.11.0", "@atlaskit/pragmatic-drag-and-drop": "^1.7.7", "@auth/core": "^0.40.0", "@auth/prisma-adapter": "^2.10.0", "@aws-sdk/client-s3": "^3.892.0", "@ctrl/tinycolor": "^4.2.0", "@deck.gl-community/editable-layers": "^9.1.1", "@deck.gl/core": "^9.1.14", "@deck.gl/geo-layers": "^9.1.14", "@deck.gl/layers": "^9.1.14", "@deck.gl/react": "^9.1.14", "@luma.gl/core": "^9.1.9", "@luma.gl/engine": "^9.1.9", "@luma.gl/shadertools": "^9.1.9", "@prisma/client": "^6.16.2", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.8", "@reduxjs/toolkit": "^2.9.0", "@restatedev/restate-sdk": "^1.9.0", "@restatedev/restate-sdk-clients": "^1.9.0", "@svgr/webpack": "^8.1.0", "@turf/bbox": "^7.2.0", "@turf/bbox-polygon": "^7.2.0", "@turf/meta": "^7.2.0", "@tus/s3-store": "^2.0.1", "@tus/server": "^2.3.0", "@uppy/core": "^5.0.2", "@uppy/react": "^5.0.3", "@uppy/tus": "^5.0.1", "@zumer/snapdom": "^1.9.11", "ai": "^5.0.47", "ali-oss": "^6.23.0", "aliyun-api-gateway": "^1.1.6", "antd": "^5.27.4", "authing-node-sdk": "^4.0.1", "big.js": "^7.0.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.18", "es-toolkit": "^1.39.10", "lucide-react": "^0.544.0", "mermaid": "^11.12.0", "nanoid": "^5.1.5", "next": "15.5.3", "next-auth": "5.0.0-beta.29", "next-navigation-guard": "^0.2.0", "next-safe-action": "^8.0.11", "node-forge": "^1.3.1", "nuqs": "^2.6.0", "p-retry": "7.0.0", "plotly.js": "^3.1.0", "proxy-agent": "^6.5.0", "react": "19.1.1", "react-dom": "19.1.1", "react-hotkeys-hook": "^5.1.0", "react-icons": "^5.5.0", "react-plotly.js": "^2.6.0", "react-redux": "^9.2.0", "react-shiki": "^0.8.0", "react18-json-view": "^0.2.9", "remark-gfm": "^4.0.1", "server-only": "^0.0.1", "shiki": "^3.12.2", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "three": "^0.180.0", "wechatpay-nextjs-v3": "^1.0.1", "wretch": "^2.11.0", "zod": "^4.1.9", "zod-config": "^1.3.0"}, "devDependencies": {"@chax-at/transactional-prisma-testing": "^1.4.0", "@eslint/eslintrc": "^3.3.1", "@next/env": "15.5.3", "@next/eslint-plugin-next": "15.5.3", "@restatedev/restate-sdk-zod": "^1.9.0", "@tailwindcss/postcss": "^4.1.13", "@testing-library/dom": "^10.4.1", "@testing-library/react": "^16.3.0", "@types/ali-oss": "^6.16.11", "@types/big.js": "^6.2.2", "@types/node": "^24.5.2", "@types/plotly.js": "^3.0.6", "@types/react": "19.1.13", "@types/react-dom": "19.1.9", "@types/react-plotly.js": "^2.6.3", "@types/three": "^0.180.0", "@vitejs/plugin-react": "^5.0.3", "@vitest/coverage-v8": "^3.2.4", "eslint": "^9.35.0", "eslint-config-next": "15.5.3", "eslint-plugin-react-hooks": "^5.2.0", "jsdom": "^27.0.0", "msw": "^2.11.2", "postcss": "^8.5.6", "prisma": "^6.16.2", "tailwindcss": "^4.1.13", "typescript": "^5.9.2", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4", "vitest-mock-extended": "^3.1.0"}, "packageManager": "pnpm@10.11.0+sha512.6540583f41cc5f628eb3d9773ecee802f4f9ef9923cc45b69890fb47991d4b092964694ec3a4f738a420c918a333062c8b925d312f42e4f0c263eb603551f977", "pnpm": {"overrides": {"@types/react": "19.1.13", "@types/react-dom": "19.1.9"}}}